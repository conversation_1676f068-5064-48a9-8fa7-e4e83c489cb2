# Исправление проблемы с отображением дополнений (extras) в Specials

## Проблема
В Specials (комбо меню) не отображались дополнения (extras). Вместо 4 групп с селектируемыми элементами пользователи видели только названия групп без самих элементов.

## Причина
Бэкенд обновился и стал отправлять новую групповую структуру extras, но фронтенд использовал старую логику парсинга.

## Решение

### 1. Обновление модели SpecialDish

Файл: `lib/src/domain/models/special/special_dish.dart`

```dart
@freezed
class SpecialDish with _$SpecialDish {
  const factory SpecialDish({
    @JsonKey(name: '_id') required String id,
    required String name,
    @Default([]) List<SpecialExtraModel> extras,
    @JsonKey(includeFromJson: true, includeToJson: false) @Default([]) List<ExtraGroupModel> extraGroups,
    @Default(true) bool allowMultipleExtras,
    @Default(true) bool allowMultipleSauces,
  }) = _SpecialDish;
  
  factory SpecialDish.fromJson(Map<String, dynamic> json) {
    // Parse new grouped extras structure from backend
    final List<ExtraGroupModel> groups = (json['extras'] as List<dynamic>?)
            ?.map((e) => ExtraGroupModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        const [];

    // If we have grouped structure, extraGroups will be populated but excluded from JSON
    // If we have old structure, use default fromJson
    if (groups.isNotEmpty) {
      return SpecialDish(
        id: json['_id'] as String,
        name: json['name'] as String,
        extraGroups: groups,
        extras: const [], // Keep old field empty for backward compatibility
        allowMultipleExtras: json['allowMultipleExtras'] as bool? ?? true,
        allowMultipleSauces: json['allowMultipleSauces'] as bool? ?? true,
      );
    } else {
      // Fallback to default generated fromJson for old structure
      return _$$SpecialDishImplFromJson(json);
    }
  }
}

extension SpecialDishX on SpecialDish {
  // Helper to check if dish uses new grouped structure
  bool get hasGroupedExtras => extraGroups.isNotEmpty;
}
```

### 2. Обновление UI компонента

Файл: `lib/src/presentation/order_flow/widgets/ingridients/special_additions.dart`

Добавлено условное отображение на основе `hasGroupedExtras`:

```dart
if (widget.dish.hasGroupedExtras)
  ...widget.dish.extraGroups
      .map((group) => _buildGroupedExtrasSection(group))
      .toList()
```

### 3. Исправление логики выбора

Файл: `lib/src/presentation/widgets/inputs/special_checkbox_expansion.dart`

```dart
// Use new grouped extras structure OR old extras structure
final hasExtras = dish.hasGroupedExtras || dish.extras.isNotEmpty;
```

## Ключевые изменения

1. **Кастомный fromJson** - парсит новую групповую структуру от бэкенда
2. **@JsonKey(includeFromJson: true, includeToJson: false)** - включает extraGroups при чтении, исключает при записи
3. **Backward compatibility** - поддержка как новой, так и старой структуры
4. **UI адаптация** - рендеринг групп на основе `hasGroupedExtras`

## Результат
✅ Specials теперь корректно отображают 4 группы дополнений с селектируемыми элементами
✅ Поддерживается обратная совместимость со старой структурой
✅ UI правильно обрабатывает как групповые, так и старые extras

## Файлы изменены
- `lib/src/domain/models/special/special_dish.dart`
- `lib/src/presentation/order_flow/widgets/ingridients/special_additions.dart` 
- `lib/src/presentation/widgets/inputs/special_checkbox_expansion.dart`