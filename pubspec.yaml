name: mutualz
description: "An incredible project for the restaurant business"

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.5 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: # add this
    sdk: flutter
  cupertino_icons: ^1.0.2
  # navigation
  go_router: 13.2.0
  # state management
  flutter_bloc: ^8.1.4
  # di
  get_it: 7.6.7
  # localization
  slang: 3.29.0
  slang_flutter: 3.29.0
  # utils
  intl: ^0.19.0
  logger: 2.0.2+1
  freezed_annotation: 2.4.1
  flutter_dotenv: 5.1.0
  mask_text_input_formatter: ^2.9.0
  url_launcher: ^6.3.0
  json_annotation: ^4.8.1
  app_links: ^6.1.4
  collection: ^1.18.0
  package_info_plus: ^8.1.0
  shimmer: ^3.0.0
  easy_debounce: ^2.0.3
  rxdart: ^0.27.7
  device_info_plus: ^10.1.2
  sentry_flutter: ^8.14.0
  uuid: ^4.5.1
  # image
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  image_picker: 1.1.0
  image_cropper: ^5.0.1
  # network
  dio: 5.4.1
  retrofit: 4.1.0
  pretty_dio_logger: 1.3.1
  # text
  animated_text_kit: ^4.2.2
  simple_gradient_text: ^1.3.0
  # input
  pin_code_fields: ^8.0.1
  # social auth
  google_sign_in: 6.2.1
  sign_in_with_apple: 5.0.0
  flutter_facebook_auth: 7.1.1
  # storage
  flutter_secure_storage: 9.2.2
  shared_preferences: ^2.3.2
  # map
  google_maps_flutter: 2.6.1
  geolocator: 11.0.0
  # camera
  mobile_scanner: 6.0.2
  # ui components
  flutter_rating_bar: ^4.0.1
  table_calendar: ^3.1.1
  dotted_border: ^2.1.0
  intl_phone_field: ^3.1.0
  confetti: ^0.7.0
  animated_number: ^1.0.2
  # payments
  flutter_stripe: 10.1.1
  # push notifications
  firebase_core: ^3.12.0
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^18.0.1
  # analytics
  app_tracking_transparency: ^2.0.6+1
  showcaseview: ^4.0.1
  permission_handler: ^11.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner:
  freezed: 2.4.7
  retrofit_generator: '>=7.0.0 <8.0.0'
  flutter_launcher_icons: ^0.13.1
  json_serializable: 6.7.1
  analyzer: ^6.4.1

dependency_overrides:
  intl: ^0.18.1

flutter:
  disable-swift-package-manager: true
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/collection/
    - assets/images/placeholders/
    - assets/images/onboarding/
    - assets/images/onboarding/drinks/
    - assets/images/onboarding/food/
    - assets/images/onboarding/events/
    - assets/images/subscriptions/
    - assets/images/reward/
    - assets/images/tutorial/
    - assets/images/tutorial/arrows/
    - assets/icons/
    - assets/icons/info/
    - assets/icons/social/
    - assets/icons/bottom_navigation/
    - assets/icons/panel/
    - assets/icons/logo/
    - assets/icons/settings/
    - assets/icons/settings/general/
    - assets/icons/payment/
    - assets/icons/levels/
    - assets/launcher/
    - assets/map/
    - assets/seed/
    - .env

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
    - family: GoldenPlains
      fonts:
        - asset: assets/fonts/Golden-Plains.ttf
          weight: 400

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/launcher/launcher.png"
  min_sdk_android: 21
