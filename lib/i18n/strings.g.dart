/// Generated file. Do not edit.
///
/// Original: lib/i18n
/// To regenerate, run: `dart run slang`
///
/// Locales: 2
/// Strings: 1054 (527 per locale)
///
/// Built on 2025-06-29 at 16:14 UTC

// coverage:ignore-file
// ignore_for_file: type=lint

import 'package:flutter/widgets.dart';
import 'package:slang/builder/model/node.dart';
import 'package:slang_flutter/slang_flutter.dart';
export 'package:slang_flutter/slang_flutter.dart';

const AppLocale _baseLocale = AppLocale.en;

/// Supported locales, see extension methods below.
///
/// Usage:
/// - LocaleSettings.setLocale(AppLocale.en) // set locale
/// - Locale locale = AppLocale.en.flutterLocale // get flutter locale from enum
/// - if (LocaleSettings.currentLocale == AppLocale.en) // locale check
enum AppLocale with BaseAppLocale<AppLocale, Translations> {
	en(languageCode: 'en', build: Translations.build),
	de(languageCode: 'de', build: _StringsDe.build);

	const AppLocale({required this.languageCode, this.scriptCode, this.countryCode, required this.build}); // ignore: unused_element

	@override final String languageCode;
	@override final String? scriptCode;
	@override final String? countryCode;
	@override final TranslationBuilder<AppLocale, Translations> build;

	/// Gets current instance managed by [LocaleSettings].
	Translations get translations => LocaleSettings.instance.translationMap[this]!;
}

/// Method A: Simple
///
/// No rebuild after locale change.
/// Translation happens during initialization of the widget (call of t).
/// Configurable via 'translate_var'.
///
/// Usage:
/// String a = t.someKey.anotherKey;
/// String b = t['someKey.anotherKey']; // Only for edge cases!
Translations get t => LocaleSettings.instance.currentTranslations;

/// Method B: Advanced
///
/// All widgets using this method will trigger a rebuild when locale changes.
/// Use this if you have e.g. a settings page where the user can select the locale during runtime.
///
/// Step 1:
/// wrap your App with
/// TranslationProvider(
/// 	child: MyApp()
/// );
///
/// Step 2:
/// final t = Translations.of(context); // Get t variable.
/// String a = t.someKey.anotherKey; // Use t variable.
/// String b = t['someKey.anotherKey']; // Only for edge cases!
class TranslationProvider extends BaseTranslationProvider<AppLocale, Translations> {
	TranslationProvider({required super.child}) : super(settings: LocaleSettings.instance);

	static InheritedLocaleData<AppLocale, Translations> of(BuildContext context) => InheritedLocaleData.of<AppLocale, Translations>(context);
}

/// Method B shorthand via [BuildContext] extension method.
/// Configurable via 'translate_var'.
///
/// Usage (e.g. in a widget's build method):
/// context.t.someKey.anotherKey
extension BuildContextTranslationsExtension on BuildContext {
	Translations get t => TranslationProvider.of(this).translations;
}

/// Manages all translation instances and the current locale
class LocaleSettings extends BaseFlutterLocaleSettings<AppLocale, Translations> {
	LocaleSettings._() : super(utils: AppLocaleUtils.instance);

	static final instance = LocaleSettings._();

	// static aliases (checkout base methods for documentation)
	static AppLocale get currentLocale => instance.currentLocale;
	static Stream<AppLocale> getLocaleStream() => instance.getLocaleStream();
	static AppLocale setLocale(AppLocale locale, {bool? listenToDeviceLocale = false}) => instance.setLocale(locale, listenToDeviceLocale: listenToDeviceLocale);
	static AppLocale setLocaleRaw(String rawLocale, {bool? listenToDeviceLocale = false}) => instance.setLocaleRaw(rawLocale, listenToDeviceLocale: listenToDeviceLocale);
	static AppLocale useDeviceLocale() => instance.useDeviceLocale();
	@Deprecated('Use [AppLocaleUtils.supportedLocales]') static List<Locale> get supportedLocales => instance.supportedLocales;
	@Deprecated('Use [AppLocaleUtils.supportedLocalesRaw]') static List<String> get supportedLocalesRaw => instance.supportedLocalesRaw;
	static void setPluralResolver({String? language, AppLocale? locale, PluralResolver? cardinalResolver, PluralResolver? ordinalResolver}) => instance.setPluralResolver(
		language: language,
		locale: locale,
		cardinalResolver: cardinalResolver,
		ordinalResolver: ordinalResolver,
	);
}

/// Provides utility functions without any side effects.
class AppLocaleUtils extends BaseAppLocaleUtils<AppLocale, Translations> {
	AppLocaleUtils._() : super(baseLocale: _baseLocale, locales: AppLocale.values);

	static final instance = AppLocaleUtils._();

	// static aliases (checkout base methods for documentation)
	static AppLocale parse(String rawLocale) => instance.parse(rawLocale);
	static AppLocale parseLocaleParts({required String languageCode, String? scriptCode, String? countryCode}) => instance.parseLocaleParts(languageCode: languageCode, scriptCode: scriptCode, countryCode: countryCode);
	static AppLocale findDeviceLocale() => instance.findDeviceLocale();
	static List<Locale> get supportedLocales => instance.supportedLocales;
	static List<String> get supportedLocalesRaw => instance.supportedLocalesRaw;
}

// translations

// Path: <root>
class Translations implements BaseTranslations<AppLocale, Translations> {
	/// Returns the current translations of the given [context].
	///
	/// Usage:
	/// final t = Translations.of(context);
	static Translations of(BuildContext context) => InheritedLocaleData.of<AppLocale, Translations>(context).translations;

	/// You can call this constructor and build your own translation instance of this locale.
	/// Constructing via the enum [AppLocale.build] is preferred.
	Translations.build({Map<String, Node>? overrides, PluralResolver? cardinalResolver, PluralResolver? ordinalResolver})
		: assert(overrides == null, 'Set "translation_overrides: true" in order to enable this feature.'),
		  $meta = TranslationMetadata(
		    locale: AppLocale.en,
		    overrides: overrides ?? {},
		    cardinalResolver: cardinalResolver,
		    ordinalResolver: ordinalResolver,
		  ) {
		$meta.setFlatMapFunction(_flatMapFunction);
	}

	/// Metadata for the translations of <en>.
	@override final TranslationMetadata<AppLocale, Translations> $meta;

	/// Access flat map
	dynamic operator[](String key) => $meta.getTranslation(key);

	late final Translations _root = this; // ignore: unused_field

	// Translations
	String get language => 'English';
	String get title => 'Mutualz';
	String get titleDev => 'Mutualz Dev';
	late final _StringsErrorsEn errors = _StringsErrorsEn._(_root);
	late final _StringsButtonsEn buttons = _StringsButtonsEn._(_root);
	late final _StringsDialogsEn dialogs = _StringsDialogsEn._(_root);
	late final _StringsScreensEn screens = _StringsScreensEn._(_root);
	late final _StringsBnbEn bnb = _StringsBnbEn._(_root);
	late final _StringsFiltersEn filters = _StringsFiltersEn._(_root);
	late final _StringsToolsEn tools = _StringsToolsEn._(_root);
	late final _StringsCustomEn custom = _StringsCustomEn._(_root);
	late final _StringsEmptyEn empty = _StringsEmptyEn._(_root);
	late final _StringsCouponsEn coupons = _StringsCouponsEn._(_root);
	late final _StringsCouponEn coupon = _StringsCouponEn._(_root);
	late final _StringsCitiesEn cities = _StringsCitiesEn._(_root);
	late final _StringsSpecialsEn specials = _StringsSpecialsEn._(_root);
	late final _StringsGendersEn genders = _StringsGendersEn._(_root);
	late final _StringsRestaurantInfoTabEn restaurantInfoTab = _StringsRestaurantInfoTabEn._(_root);
	late final _StringsScannerEn scanner = _StringsScannerEn._(_root);
	late final _StringsPostCardEn postCard = _StringsPostCardEn._(_root);
	late final _StringsWeekdaysEn weekdays = _StringsWeekdaysEn._(_root);
	late final _StringsPaymentInfoEn paymentInfo = _StringsPaymentInfoEn._(_root);
	late final _StringsLoyaltyEn loyalty = _StringsLoyaltyEn._(_root);
	late final _StringsEarnWidgetEn earnWidget = _StringsEarnWidgetEn._(_root);
	late final _StringsReportUserPostEn reportUserPost = _StringsReportUserPostEn._(_root);
	late final _StringsBlockUserPostEn blockUserPost = _StringsBlockUserPostEn._(_root);
	late final _StringsBlockUserEn blockUser = _StringsBlockUserEn._(_root);
	late final _StringsReportUserEn reportUser = _StringsReportUserEn._(_root);
	late final _StringsStrangerProfileEn strangerProfile = _StringsStrangerProfileEn._(_root);
	late final _StringsTutorialEn tutorial = _StringsTutorialEn._(_root);
}

// Path: errors
class _StringsErrorsEn {
	_StringsErrorsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get fieldEmpty => 'This field shouldn\'t be empty';
	String get emailInvalid => 'Invalid email';
	String get passwordInvalid => 'Password should be at least 8 long and less than 60 characters';
	String get userNameInvalid => 'Username should be at least 2 long and less than 60 characters';
	String get passwordNotMatch => 'Passwords don\'t match';
	String get otpEmpty => 'OTP cannot be empty';
	String get unknown => 'Unknown error';
	String get errorAuthConflict => 'Authentication conflict';
	String get errorAuthTokenExpired => 'Authentication token expired';
	String get errorSocialAuthFailed => 'Social authentication failed';
	String get errorAuthOperationInProgress => 'Authentication operation in progress';
	String get errorAuthCancelled => 'Authentication cancelled';
	String get privacyNotAccepted => 'Privacy policy and Terms & Conditions not accepted';
	String get baseTextLength => 'Length should be at between 2 and 60 characters';
	String get phoneNumberInvalid => 'Invalid phone number';
	String get doesNotContainNumber => 'Password should contain at least one number';
	String get doesNotContainSpecialCharacter => 'Password should contain at least one special character';
	String get doesNotContainUppercase => 'Password should contain at least one uppercase letter';
	String get doesNotContainLowercase => 'Password should contain at least one lowercase letter';
	String get disabledLocationService => 'Location services are disabled, please enable it to continue';
	String get locationPermissionDenied => 'Location permissions are denied, please enable it to continue';
	String get locationPermissionPermanentlyDenied => 'Location permissions are permanently denied, we cannot request permissions';
	String get noRestaurantImage => 'No Restaurant Image';
	String get emptyRating => 'All ratings should be filled';
	String get defaultPaymentMethod => 'You must have at least one payment method';
	String get restartError => 'There is problem with the payment method. Please restart your app. it might help you.';
}

// Path: buttons
class _StringsButtonsEn {
	_StringsButtonsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get save => 'Save';
	String get send => 'Send';
	String get ok => 'Ok';
	String get add => 'Add';
	String get edit => 'Edit';
	String get order => 'Order';
	String get pay => 'Pay';
	String get orderAndPay => 'Order & Pay';
	String get seeAll => 'See all';
	String get back => 'Go Back';
	String get savePoints => 'Gain even more points';
	String get tryAgain => 'Try again';
	String get tryAgainLater => 'Try again later';
	String get skip => 'Skip';
}

// Path: dialogs
class _StringsDialogsEn {
	_StringsDialogsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsDialogsErrorEn error = _StringsDialogsErrorEn._(_root);
	late final _StringsDialogsSuccessEn success = _StringsDialogsSuccessEn._(_root);
	String get buttonLabel => 'Ok';
	late final _StringsDialogsOptionsEn options = _StringsDialogsOptionsEn._(_root);
	late final _StringsDialogsNumberTableEn numberTable = _StringsDialogsNumberTableEn._(_root);
}

// Path: screens
class _StringsScreensEn {
	_StringsScreensEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsScreensSplashEn splash = _StringsScreensSplashEn._(_root);
	late final _StringsScreensAuthEn auth = _StringsScreensAuthEn._(_root);
	late final _StringsScreensForgotEn forgot = _StringsScreensForgotEn._(_root);
	late final _StringsScreensResetEn reset = _StringsScreensResetEn._(_root);
	late final _StringsScreensOnboardingEn onboarding = _StringsScreensOnboardingEn._(_root);
	late final _StringsScreensSocialOnboardingEn socialOnboarding = _StringsScreensSocialOnboardingEn._(_root);
	late final _StringsScreensNotificationPermissionEn notificationPermission = _StringsScreensNotificationPermissionEn._(_root);
	late final _StringsScreensSearchEn search = _StringsScreensSearchEn._(_root);
	late final _StringsScreensDiscoverEn discover = _StringsScreensDiscoverEn._(_root);
	late final _StringsScreensRestaurantEn restaurant = _StringsScreensRestaurantEn._(_root);
	late final _StringsScreensProfileEn profile = _StringsScreensProfileEn._(_root);
	late final _StringsScreensGeneralSettingsEn generalSettings = _StringsScreensGeneralSettingsEn._(_root);
	late final _StringsScreensChangePasswordSettingsEn changePasswordSettings = _StringsScreensChangePasswordSettingsEn._(_root);
	late final _StringsScreensChangePreferencesEn changePreferences = _StringsScreensChangePreferencesEn._(_root);
	late final _StringsScreensUserProfileSettingsEn userProfileSettings = _StringsScreensUserProfileSettingsEn._(_root);
	late final _StringsScreensFeedbackEn feedback = _StringsScreensFeedbackEn._(_root);
	late final _StringsScreensFaqEn faq = _StringsScreensFaqEn._(_root);
	late final _StringsScreensContactEn contact = _StringsScreensContactEn._(_root);
	late final _StringsScreensSubscriptionEn subscription = _StringsScreensSubscriptionEn._(_root);
	late final _StringsScreensCommentsEn comments = _StringsScreensCommentsEn._(_root);
	late final _StringsScreensOrderProcessEn orderProcess = _StringsScreensOrderProcessEn._(_root);
	late final _StringsScreensCartEn cart = _StringsScreensCartEn._(_root);
	late final _StringsScreensInvoiceEn invoice = _StringsScreensInvoiceEn._(_root);
	late final _StringsScreensPaymentMethodSettingsEn paymentMethodSettings = _StringsScreensPaymentMethodSettingsEn._(_root);
	late final _StringsScreensAddPostEn addPost = _StringsScreensAddPostEn._(_root);
	late final _StringsScreensPaymentEn payment = _StringsScreensPaymentEn._(_root);
	late final _StringsScreensOrdersEn orders = _StringsScreensOrdersEn._(_root);
	late final _StringsScreensProgressRewardEn progressReward = _StringsScreensProgressRewardEn._(_root);
	late final _StringsScreensReviewEn review = _StringsScreensReviewEn._(_root);
}

// Path: bnb
class _StringsBnbEn {
	_StringsBnbEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get discover => 'Discover';
	String get feed => 'Feed';
	String get search => 'Search';
	String get profile => 'Profile';
}

// Path: filters
class _StringsFiltersEn {
	_StringsFiltersEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get foodTypeTitle => 'Types of food';
	String get dietPreferencesTitle => 'Diet preferences';
	String get foodTitle => 'Food';
	String get sortTitle => 'Sort';
	late final _StringsFiltersFoodTypeEn foodType = _StringsFiltersFoodTypeEn._(_root);
	late final _StringsFiltersDietPreferencesEn dietPreferences = _StringsFiltersDietPreferencesEn._(_root);
	late final _StringsFiltersFoodEn food = _StringsFiltersFoodEn._(_root);
	late final _StringsFiltersSortEn sort = _StringsFiltersSortEn._(_root);
}

// Path: tools
class _StringsToolsEn {
	_StringsToolsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsToolsImagePickerEn imagePicker = _StringsToolsImagePickerEn._(_root);
}

// Path: custom
class _StringsCustomEn {
	_StringsCustomEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsCustomPointProgressEn pointProgress = _StringsCustomPointProgressEn._(_root);
	String get isReached => 'is reached';
	String get youMadeIt => 'You made it';
}

// Path: empty
class _StringsEmptyEn {
	_StringsEmptyEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get image => 'No Image';
}

// Path: coupons
class _StringsCouponsEn {
	_StringsCouponsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get available => 'Available Coupons';
	String get activatable => 'Activatable Coupons';
	String get noCoupons => 'No available coupons';
	String get appliable => 'Appliable';
	String get canBeReactivated => 'Can be reactivated';
	String get nonAppliable => 'Non appliable';
}

// Path: coupon
class _StringsCouponEn {
	_StringsCouponEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Mutualz';
	String get off => 'off';
	String percentage({required Object value}) => '${value}%';
	String get separator => 'for';
}

// Path: cities
class _StringsCitiesEn {
	_StringsCitiesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get berlin => 'Berlin';
	String get oldenburg => 'Oldenburg';
	String get osnaburg => 'Osnabrück';
}

// Path: specials
class _StringsSpecialsEn {
	_StringsSpecialsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Specials';
	String get single => 'Special';
	String get availableTitle => 'Special available';
	String get availableAt => 'Available at';
}

// Path: genders
class _StringsGendersEn {
	_StringsGendersEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get male => 'Male';
	String get female => 'Female';
	String get diverse => 'Diverse';
}

// Path: restaurantInfoTab
class _StringsRestaurantInfoTabEn {
	_StringsRestaurantInfoTabEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get deals => 'Deals';
	String get menu => 'Menu';
	String get feed => 'Feed';
}

// Path: scanner
class _StringsScannerEn {
	_StringsScannerEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get label => 'Scan the QR code\nto access the restaurant';
	String get errorDifferentRestaurant => 'You are scanning a QR code from a different restaurant';
	String errorRestaurantClosed({required Object name}) => '${name} is closed. Try again during the opening hours.';
}

// Path: postCard
class _StringsPostCardEn {
	_StringsPostCardEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get likes => 'Likes';
	String get comments => 'Comments';
}

// Path: weekdays
class _StringsWeekdaysEn {
	_StringsWeekdaysEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get monday => 'Monday';
	String get tuesday => 'Tuesday';
	String get wednesday => 'Wednesday';
	String get thursday => 'Thursday';
	String get friday => 'Friday';
	String get saturday => 'Saturday';
	String get sunday => 'Sunday';
}

// Path: paymentInfo
class _StringsPaymentInfoEn {
	_StringsPaymentInfoEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get total => 'Total';
	String get discount => 'Discount';
	String get totalWithMutualz => 'Total with Mutualz';
}

// Path: loyalty
class _StringsLoyaltyEn {
	_StringsLoyaltyEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get test => 'Test';
	String get member => 'Member';
	String get silver => 'Silver';
	String get gold => 'Gold';
}

// Path: earnWidget
class _StringsEarnWidgetEn {
	_StringsEarnWidgetEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get label => 'You\'ve earned';
	String get points => 'points';
}

// Path: reportUserPost
class _StringsReportUserPostEn {
	_StringsReportUserPostEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Report User Post';
	String get hint => 'Please describe the reason for your report';
	late final _StringsReportUserPostItemsEn items = _StringsReportUserPostItemsEn._(_root);
}

// Path: blockUserPost
class _StringsBlockUserPostEn {
	_StringsBlockUserPostEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Block User Post';
	String get hint => 'Please describe the reason for your report';
	late final _StringsBlockUserPostItemsEn items = _StringsBlockUserPostItemsEn._(_root);
}

// Path: blockUser
class _StringsBlockUserEn {
	_StringsBlockUserEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Block User';
	String get hint => 'Please describe the block for your report';
	late final _StringsBlockUserItemsEn items = _StringsBlockUserItemsEn._(_root);
}

// Path: reportUser
class _StringsReportUserEn {
	_StringsReportUserEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Report User';
	String get hint => 'Please describe the reason for your report';
	late final _StringsReportUserItemsEn items = _StringsReportUserItemsEn._(_root);
}

// Path: strangerProfile
class _StringsStrangerProfileEn {
	_StringsStrangerProfileEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get followers => 'Followers';
	String get following => 'Following';
	String get friendsInCommon => 'friends in common';
}

// Path: tutorial
class _StringsTutorialEn {
	_StringsTutorialEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsTutorialMapEn map = _StringsTutorialMapEn._(_root);
	late final _StringsTutorialFilterEn filter = _StringsTutorialFilterEn._(_root);
	late final _StringsTutorialFeedEn feed = _StringsTutorialFeedEn._(_root);
	late final _StringsTutorialRestaurantEn restaurant = _StringsTutorialRestaurantEn._(_root);
	late final _StringsTutorialProfileEn profile = _StringsTutorialProfileEn._(_root);
	late final _StringsTutorialOrdersEn orders = _StringsTutorialOrdersEn._(_root);
	late final _StringsTutorialLoyaltyEn loyalty = _StringsTutorialLoyaltyEn._(_root);
}

// Path: dialogs.error
class _StringsDialogsErrorEn {
	_StringsDialogsErrorEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Ups!!!';
}

// Path: dialogs.success
class _StringsDialogsSuccessEn {
	_StringsDialogsSuccessEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Excellent!';
	String get changes => 'Your changes have been saved';
}

// Path: dialogs.options
class _StringsDialogsOptionsEn {
	_StringsDialogsOptionsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get deletePost => 'Delete Post';
	String get report => 'Report';
	String get unfollow => 'Unfollow';
	String get block => 'Block';
	String get follow => 'Follow';
}

// Path: dialogs.numberTable
class _StringsDialogsNumberTableEn {
	_StringsDialogsNumberTableEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Put in your table number.';
	String get hint => 'Tablenumber';
}

// Path: screens.splash
class _StringsScreensSplashEn {
	_StringsScreensSplashEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Mutualz';
}

// Path: screens.auth
class _StringsScreensAuthEn {
	_StringsScreensAuthEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get welcome => 'Welcome\nto Mutualz';
	late final _StringsScreensAuthTabsEn tabs = _StringsScreensAuthTabsEn._(_root);
	late final _StringsScreensAuthLoginEn login = _StringsScreensAuthLoginEn._(_root);
	late final _StringsScreensAuthRegistrationEn registration = _StringsScreensAuthRegistrationEn._(_root);
	late final _StringsScreensAuthOptEn opt = _StringsScreensAuthOptEn._(_root);
}

// Path: screens.forgot
class _StringsScreensForgotEn {
	_StringsScreensForgotEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Forgot Password';
	String get description => 'Please enter your email with which you\'re registered. A link for changing your password will be send immediately.';
	String get emailLabel => 'Mail';
	String get buttonLabel => 'Send';
	String get backButtonLabel => 'Go Back';
	String get successDescription => 'You can check your email to enter the password recovery link';
}

// Path: screens.reset
class _StringsScreensResetEn {
	_StringsScreensResetEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Change your password';
	String get description => 'Add your new password here';
	String get passwordLabel => 'Password*';
	String get repeatPasswordLabel => 'Repeat password*';
	String get buttonLabel => 'Change Password';
	String get successMessage => 'Congratulations, your password has been changed';
}

// Path: screens.onboarding
class _StringsScreensOnboardingEn {
	_StringsScreensOnboardingEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsScreensOnboardingPersonalInfoEn personalInfo = _StringsScreensOnboardingPersonalInfoEn._(_root);
	late final _StringsScreensOnboardingLifestyleEn lifestyle = _StringsScreensOnboardingLifestyleEn._(_root);
	late final _StringsScreensOnboardingTasteEn taste = _StringsScreensOnboardingTasteEn._(_root);
	late final _StringsScreensOnboardingDrinksEn drinks = _StringsScreensOnboardingDrinksEn._(_root);
	late final _StringsScreensOnboardingFoodEn food = _StringsScreensOnboardingFoodEn._(_root);
	late final _StringsScreensOnboardingEventsEn events = _StringsScreensOnboardingEventsEn._(_root);
	String get nextButtonLabel => 'Next';
	String get backButtonLabel => 'Go back';
}

// Path: screens.socialOnboarding
class _StringsScreensSocialOnboardingEn {
	_StringsScreensSocialOnboardingEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Let\'s get acquainted';
	String get description => 'Share information about yourself with us';
	String get nextButtonLabel => 'Next';
	String get skipButtonLabel => 'Skip';
}

// Path: screens.notificationPermission
class _StringsScreensNotificationPermissionEn {
	_StringsScreensNotificationPermissionEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'NOTIFICATIONS';
	String get description => 'Turn on your notifications so we can inform you when your food is ready.';
	late final _StringsScreensNotificationPermissionButtonsEn buttons = _StringsScreensNotificationPermissionButtonsEn._(_root);
}

// Path: screens.search
class _StringsScreensSearchEn {
	_StringsScreensSearchEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get noResults => 'Sorry, no results were found';
	String get inputLabel => 'Search...';
	String get sortByRating => 'Sort by rating';
	String get emptyCollection => 'We need to get to\nknow you better';
}

// Path: screens.discover
class _StringsScreensDiscoverEn {
	_StringsScreensDiscoverEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsScreensDiscoverInfoEn info = _StringsScreensDiscoverInfoEn._(_root);
}

// Path: screens.restaurant
class _StringsScreensRestaurantEn {
	_StringsScreensRestaurantEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get bookTableButton => 'Book a table';
	String get orderPayButton => 'Order here';
	late final _StringsScreensRestaurantBookingEn booking = _StringsScreensRestaurantBookingEn._(_root);
	late final _StringsScreensRestaurantInfoEn info = _StringsScreensRestaurantInfoEn._(_root);
	late final _StringsScreensRestaurantMenuEn menu = _StringsScreensRestaurantMenuEn._(_root);
	String get emptyDietPreferences => 'No diet preferences';
	late final _StringsScreensRestaurantDealsEn deals = _StringsScreensRestaurantDealsEn._(_root);
}

// Path: screens.profile
class _StringsScreensProfileEn {
	_StringsScreensProfileEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get hint => 'This profile has been deleted.';
	late final _StringsScreensProfileTabsEn tabs = _StringsScreensProfileTabsEn._(_root);
	String get followers => 'Followers';
	String get following => 'Following';
	String get subscriptions => 'Subscriptions';
	late final _StringsScreensProfileSettingsEn settings = _StringsScreensProfileSettingsEn._(_root);
}

// Path: screens.generalSettings
class _StringsScreensGeneralSettingsEn {
	_StringsScreensGeneralSettingsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get profile => 'User Profile';
	String get password => 'Change Password';
	String get preferences => 'Preferences';
	String get payment => 'Payment Method';
}

// Path: screens.changePasswordSettings
class _StringsScreensChangePasswordSettingsEn {
	_StringsScreensChangePasswordSettingsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Change Password';
	String get newPassword => 'New Password';
	String get confirmNewPassword => 'Confirm New Password';
	String get buttonLabel => 'Change Password';
}

// Path: screens.changePreferences
class _StringsScreensChangePreferencesEn {
	_StringsScreensChangePreferencesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get buttonLabel => 'Save';
}

// Path: screens.userProfileSettings
class _StringsScreensUserProfileSettingsEn {
	_StringsScreensUserProfileSettingsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get deleteAccount => 'Delete account';
	String get deleteAccountTitle => 'Are you sure you want to delete your account?';
	String get deleteAccountDescription => 'You will loose access to all of Mutualz exclusive  discounts and your profile and posts will be deleted  permanently.';
	String get buttonCancel => 'I changed my mind';
	String get buttonOk => 'I\'m sure';
}

// Path: screens.feedback
class _StringsScreensFeedbackEn {
	_StringsScreensFeedbackEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Feedback';
	String get messageLabel => 'Message';
	String get design => 'Design';
	String get userFriendliness => 'User friendliness';
	String get paymentFunction => 'Payment function';
	String get orderFunction => 'Order function';
	String get community => 'Community';
	String get couponsDiscounts => 'Coupons & Discounts';
	String get recommendations => 'Recommendations';
	String get pushNotifications => 'Push Notifications';
	String get speed => 'Speed';
	String get otherFeatures => 'Other features';
	String get successMessage => 'Thank you for your feedback! Our team strives to deliver the best for you.';
}

// Path: screens.faq
class _StringsScreensFaqEn {
	_StringsScreensFaqEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'FAQ\'s';
	String get contactUs => 'Contact us';
	String get empty => 'No questions/answers found';
}

// Path: screens.contact
class _StringsScreensContactEn {
	_StringsScreensContactEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Contact Form';
	String get messageLabel => 'Write your message';
	String get registration => 'Registration process';
	String get payment => 'Payment function';
	String get subscription => 'Subscription';
	String get report => 'Report error';
	String get other => 'Other categories';
	String get successMessage => 'Thank you for your message! We will get back to you as soon as possible.';
}

// Path: screens.subscription
class _StringsScreensSubscriptionEn {
	_StringsScreensSubscriptionEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Subscriptions';
	String get description => 'You`ll advance the subscription tiers by\ngathering points on every restaurant visit';
	late final _StringsScreensSubscriptionLevelsEn levels = _StringsScreensSubscriptionLevelsEn._(_root);
	late final _StringsScreensSubscriptionLevelsDescriptionEn levelsDescription = _StringsScreensSubscriptionLevelsDescriptionEn._(_root);
	late final _StringsScreensSubscriptionButtonsEn buttons = _StringsScreensSubscriptionButtonsEn._(_root);
	String info({required Object date}) => 'Your discount is still active till ${date}';
	late final _StringsScreensSubscriptionDiscountEn discount = _StringsScreensSubscriptionDiscountEn._(_root);
	String daysRemaining({required num n}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('en'))(n,
		one: '1 day left',
		other: '${n} days left',
	);
	String get discountOnTotalBill => 'Discount on\nthe total bill*';
}

// Path: screens.comments
class _StringsScreensCommentsEn {
	_StringsScreensCommentsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Comments';
	String get inputLabel => 'Add a comment';
	String get empty => 'No comments available';
}

// Path: screens.orderProcess
class _StringsScreensOrderProcessEn {
	_StringsScreensOrderProcessEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Menu';
	String get empty => 'No menu available';
	String get orderButton => 'Order/Pay';
	String get orderSuccess => 'Order created successfully. Please enjoy your meal!';
	String get paymentSuccess => 'Payment successfully completed';
}

// Path: screens.cart
class _StringsScreensCartEn {
	_StringsScreensCartEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get empty => 'No elements in the cart';
	String get total => 'Total:';
	late final _StringsScreensCartDialogNotesEn dialogNotes = _StringsScreensCartDialogNotesEn._(_root);
	late final _StringsScreensCartDialogRemoveEn dialogRemove = _StringsScreensCartDialogRemoveEn._(_root);
	late final _StringsScreensCartDialogSpecialRemoveEn dialogSpecialRemove = _StringsScreensCartDialogSpecialRemoveEn._(_root);
	late final _StringsScreensCartDialogConfirmEn dialogConfirm = _StringsScreensCartDialogConfirmEn._(_root);
	late final _StringsScreensCartDialogShareEn dialogShare = _StringsScreensCartDialogShareEn._(_root);
	String get createOrderSuccess => 'Order created successfully. Please enjoy your meal!';
}

// Path: screens.invoice
class _StringsScreensInvoiceEn {
	_StringsScreensInvoiceEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Thanks\nfor joining us';
	String get dishes => 'Dishes';
	String get coupons => 'Coupons:';
	String get couponSelection => 'Coupon Selection';
	String get doNotUseCoupon => 'Do not use a coupon';
	String get proceedToPayment => 'Proceed to payment';
}

// Path: screens.paymentMethodSettings
class _StringsScreensPaymentMethodSettingsEn {
	_StringsScreensPaymentMethodSettingsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Payment Methods';
	String get card => 'Card';
	String get applePay => 'Apple Pay';
	String get googlePay => 'Google Pay';
}

// Path: screens.addPost
class _StringsScreensAddPostEn {
	_StringsScreensAddPostEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Share your experience with everyone';
	String get description => 'Create a post of your best moments';
	String get uploadImage => 'Upload Image';
	String get restaurantHint => 'Select a restaurant';
	String get descriptionHint => 'Add a description here';
	String get attachImage => 'Attach Image';
	String get tagYourFriends => 'Tag your friends';
}

// Path: screens.payment
class _StringsScreensPaymentEn {
	_StringsScreensPaymentEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get message => 'Thank you for your visit!';
	String get buttonLabel => 'Collect your points';
	String get success => 'Payment successfully completed';
}

// Path: screens.orders
class _StringsScreensOrdersEn {
	_StringsScreensOrdersEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Previous Orders';
	String get empty => 'No previous orders';
	String get total => 'Total';
	String get memberDiscount => 'Member discount';
	String get couponDiscount => 'Coupon discount';
	String get totalWithMutualz => 'Total with Mutualz';
	String get summary => 'Order summary';
	String get sendInvoice => 'Send Invoice';
	String get invoiceSent => 'Invoice has been successfully sent';
	String get sendingInvoice => 'Sending invoice...';
}

// Path: screens.progressReward
class _StringsScreensProgressRewardEn {
	_StringsScreensProgressRewardEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get unlockCoupons => 'Spend them to unlock\nCoupons';
}

// Path: screens.review
class _StringsScreensReviewEn {
	_StringsScreensReviewEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get rating => 'Rating';
	late final _StringsScreensReviewRatingItemsEn ratingItems = _StringsScreensReviewRatingItemsEn._(_root);
	String get commentHint => 'Write your comment here';
	String get verifyDietary => 'Verify the dietary claims';
	late final _StringsScreensReviewDietPreferencesEn dietPreferences = _StringsScreensReviewDietPreferencesEn._(_root);
	String get uploadImage => 'Upload Image';
	String get descriptionHint => 'Add a description here';
	String get rateTitle => 'Rate now to get extra Points';
	String get points => 'Points';
	String get rate => 'Rate';
	String get rateDescription => 'Rate the service and earn 5 extra points';
	String get rateAndPost => 'Rate & Post';
	String get rateAndPostDescription => 'Rate the service and earn 10 extra points';
	String get skip => 'Rate later';
}

// Path: filters.foodType
class _StringsFiltersFoodTypeEn {
	_StringsFiltersFoodTypeEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get american => 'American';
	String get arabic => 'Arabic';
	String get asian => 'Asian';
	String get bakery => 'Bakery';
	String get burger => 'Burger';
	String get chinese => 'Chinese';
	String get curry => 'Curry';
	String get german => 'German';
	String get doner => 'Doner';
	String get iceCream => 'Ice Cream';
	String get espresso => 'Espresso';
	String get falafel => 'Falafel';
	String get fish => 'Fish';
	String get breakfast => 'Breakfast';
	String get drinks => 'Drinks';
	String get chicken => 'Chicken';
	String get hotdog => 'Hotdog';
	String get indian => 'Indian';
	String get italian => 'Italian';
	String get japanese => 'Japanese';
	String get coffee => 'Coffee';
	String get cake => 'Cake';
	String get seafood => 'Seafood';
	String get mexican => 'Mexican';
	String get lunch => 'Lunch';
	String get desserts => 'Desserts';
	String get noodles => 'Noodles';
	String get austrian => 'Austrian';
	String get pakistani => 'Pakistani';
	String get pasta => 'Pasta';
	String get persian => 'Persian';
	String get pizza => 'Pizza';
	String get pokeBowl => 'Poke Bowl';
	String get ramen => 'Ramen';
	String get frenchFries => 'French Fries';
	String get salads => 'Salads';
	String get sandwiches => 'Sandwiches';
	String get snacks => 'Snacks';
	String get steaks => 'Steaks';
	String get soups => 'Soups';
	String get sushi => 'Sushi';
	String get syrian => 'Syrian';
	String get thai => 'Thai';
	String get turkish => 'Turkish';
	String get vegan => 'Vegan';
	String get vegetarian => 'Vegetarian';
	String get vietnamese => 'Vietnamese';
	String get wraps => 'Wraps';
	String get bar => 'Bar';
	String get bowls => 'Bowls';
	String get cafe => 'Café';
	String get cocktails => 'Cocktails';
	String get greek => 'Greek';
	String get kebab => 'Kebab';
	String get restaurant => 'Restaurant';
	String get spanish => 'Spanish';
}

// Path: filters.dietPreferences
class _StringsFiltersDietPreferencesEn {
	_StringsFiltersDietPreferencesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get kosher => 'Kosher';
	String get halal => 'Halal';
	String get vegetarian => 'Vegetarian';
	String get vegan => 'Vegan';
	String get glutenFree => 'Gluten Free';
	String get lactoseFree => 'Lactose Free';
	String get nutsFree => 'Nuts Free';
}

// Path: filters.food
class _StringsFiltersFoodEn {
	_StringsFiltersFoodEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get breakfast => 'Breakfast';
	String get brunch => 'Brunch';
	String get lunch => 'Lunch';
	String get dinner => 'Dinner';
	String get snack => 'Snack';
}

// Path: filters.sort
class _StringsFiltersSortEn {
	_StringsFiltersSortEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get rating => 'by rating';
}

// Path: tools.imagePicker
class _StringsToolsImagePickerEn {
	_StringsToolsImagePickerEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Cropper';
}

// Path: custom.pointProgress
class _StringsCustomPointProgressEn {
	_StringsCustomPointProgressEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get silver => 'Silver';
	String get gold => 'Gold';
	String get test => 'Test';
	String get member => 'Member';
	String points({required Object value, required Object opposite}) => '${value}/${opposite} Points';
	String pointsString({required Object value}) => '${value} Points';
}

// Path: reportUserPost.items
class _StringsReportUserPostItemsEn {
	_StringsReportUserPostItemsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get nudity => 'Nudity or sexualization';
	String get violence => 'Blood or violence';
	String get suicide => 'Suicide, self-harm or eating disorders';
	String get hate => 'Hate or incitement to hatred';
	String get ads => 'Sale or advertising of products/services outside the catering sector';
}

// Path: blockUserPost.items
class _StringsBlockUserPostItemsEn {
	_StringsBlockUserPostItemsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get nudity => 'Nudity or sexualization';
	String get violence => 'Blood or violence';
	String get suicide => 'Suicide, self-harm or eating disorders';
	String get hate => 'Hate or incitement to hatred';
	String get ads => 'Sale or advertising of products/services outside the catering sector';
}

// Path: blockUser.items
class _StringsBlockUserItemsEn {
	_StringsBlockUserItemsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get insultsOrBullying => 'Insults or bullying';
	String get nudityOrSexualization => 'Nudity or sexualization';
	String get bloodOrViolence => 'Blood or violence';
	String get spam => 'Spam';
	String get falseIdentityOrIdentityStolen => 'False identity/identity stolen';
	String get accountCouldHaveBeenHacked => 'Account could have been hacked';
	String get personUnder16 => 'Person is under 16';
}

// Path: reportUser.items
class _StringsReportUserItemsEn {
	_StringsReportUserItemsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get insultsOrBullying => 'Insults or bullying';
	String get nudityOrSexualization => 'Nudity or sexualization';
	String get bloodOrViolence => 'Blood or violence';
	String get spam => 'Spam';
	String get falseIdentityOrIdentityStolen => 'False identity/identity stolen';
	String get accountCouldHaveBeenHacked => 'Account could have been hacked';
	String get personUnder16 => 'Person is under 16';
}

// Path: tutorial.map
class _StringsTutorialMapEn {
	_StringsTutorialMapEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get qrCode => 'Start your order. Here you can scan the QR code to place your order.';
}

// Path: tutorial.filter
class _StringsTutorialFilterEn {
	_StringsTutorialFilterEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get info => 'Filtering changes the results on the map and in the list view. Slide the filters up for the list view.';
}

// Path: tutorial.feed
class _StringsTutorialFeedEn {
	_StringsTutorialFeedEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get addPost => 'Here you can post a contribution.';
	String get search => 'Do you want to follow friends or family? Here you can search for the person.';
}

// Path: tutorial.restaurant
class _StringsTutorialRestaurantEn {
	_StringsTutorialRestaurantEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get rating => 'Click on the stars or the clock to get more information';
	String get favorite => 'Here you can mark the location as a favorite';
	String get dietPreferences => 'Dietary forms and intolerances. All gray icons are information from the restaurateur.';
	String get dietPreferencesConfirmed => 'If it\'s red, the information has been confirmed by users.';
	String get deals => 'Here you can see all deals and coupons';
	String get menu => 'Here you can browse in advance';
}

// Path: tutorial.profile
class _StringsTutorialProfileEn {
	_StringsTutorialProfileEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get subscriptions => 'Here are all the levels you can reach within the loyalty program. Additionally, you can see how many points you have in total.';
	String get orders => 'Here you can view and download your bills';
	String get dietPreferences => 'Here you can adjust your dietary preferences';
}

// Path: tutorial.orders
class _StringsTutorialOrdersEn {
	_StringsTutorialOrdersEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get tableNumber => 'You can find the table number next to the QR code on the table';
	String get cart => 'This is your shopping cart, here you can see all the products you have added.';
	String get cartInside => 'This is what your shopping cart looks like inside. You can adjust your orders here';
	String get orderAndPayNow => 'Here you order and pay immediately, this makes the most sense if you need to leave quickly, for example';
	String get orderAndPayLater => 'Here you can order and pay later when you leave the venue';
	String get usedCoupons => 'You have already used these coupons and can unlock them again with points';
	String get loyaltyPoints => 'You can use these points at this venue to unlock coupons again';
	String get congratulations => 'Congratulations! You have collected your first points in the loyalty program!';
}

// Path: tutorial.loyalty
class _StringsTutorialLoyaltyEn {
	_StringsTutorialLoyaltyEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get fourLevels => 'There are four levels in the loyalty program. You always start at the first level and then have one month to advance to the Gold level.';
	String get test => 'You collect points with every purchase and thus advance to the next level.\n\nThe best part: Every purchase counts regardless of which MUTUALZ partner you shop with!\n\nImportant: At the end of the month you will be reset back to this level and 0 points!*';
	String get member => 'At this level you receive 5% discount on every bill with all partners.\n\nThe faster you reach this level, the more time you have to climb the next levels!\n\nSecret tip: With the Mutualz subscription you start at this level at the beginning of each month! Time advantage means price advantage!';
	String get silver => 'This level rewards the reliable and loyal!\n\nWith Silver you save 10% on the total bill in restaurants where you have reached Silver.\n\nYou can only reach this level by collecting enough points in one restaurant and advancing through levels.*';
	String get gold => 'Congratulations Your Majesty!\n\nWhen they say: "The customer is king!" Then they speak exclusively about you!\n\nYou save 15% on the total bill in the restaurant where you achieved this level.';
	String get subscription => 'Subscribe to MUTUALZ now and get 5% discount immediately in addition to your coupons and start every month with a time advantage !!!';
	late final _StringsTutorialLoyaltyButtonsEn buttons = _StringsTutorialLoyaltyButtonsEn._(_root);
	String get terms => '*for more information check our Terms and Conditions';
	String get level => 'Level';
	String get oneCalendarMonth => 'One calendar month time';
}

// Path: screens.auth.tabs
class _StringsScreensAuthTabsEn {
	_StringsScreensAuthTabsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get login => 'Login';
	String get registration => 'Registration';
}

// Path: screens.auth.login
class _StringsScreensAuthLoginEn {
	_StringsScreensAuthLoginEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get email => 'Mail';
	String get password => 'Password';
	String get buttonLabel => 'Login';
	String get forgotPassword => 'Forgot Password?';
}

// Path: screens.auth.registration
class _StringsScreensAuthRegistrationEn {
	_StringsScreensAuthRegistrationEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	late final _StringsScreensAuthRegistrationLabelsEn labels = _StringsScreensAuthRegistrationLabelsEn._(_root);
	late final _StringsScreensAuthRegistrationTermsEn terms = _StringsScreensAuthRegistrationTermsEn._(_root);
	String get buttonLabel => 'Sign up';
}

// Path: screens.auth.opt
class _StringsScreensAuthOptEn {
	_StringsScreensAuthOptEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Insert OTP code';
	String get description => 'We have sent the OTP code to your email.';
	String get buttonLabel => 'Ok';
	String get resendLabel => 'Resend access code';
	String get resendCodeAvailable => 'Resend code available';
}

// Path: screens.onboarding.personalInfo
class _StringsScreensOnboardingPersonalInfoEn {
	_StringsScreensOnboardingPersonalInfoEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Voluntary information,';
	String get description => 'share some basic details with us';
}

// Path: screens.onboarding.lifestyle
class _StringsScreensOnboardingLifestyleEn {
	_StringsScreensOnboardingLifestyleEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Tell us about you,';
	String get description => 'so we can offer you deals that fit your life';
	late final _StringsScreensOnboardingLifestyleJobEn job = _StringsScreensOnboardingLifestyleJobEn._(_root);
	late final _StringsScreensOnboardingLifestyleAllergiesEn allergies = _StringsScreensOnboardingLifestyleAllergiesEn._(_root);
	late final _StringsScreensOnboardingLifestyleDietEn diet = _StringsScreensOnboardingLifestyleDietEn._(_root);
}

// Path: screens.onboarding.taste
class _StringsScreensOnboardingTasteEn {
	_StringsScreensOnboardingTasteEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Show us what you like';
	String get description => 'Select your preferences';
}

// Path: screens.onboarding.drinks
class _StringsScreensOnboardingDrinksEn {
	_StringsScreensOnboardingDrinksEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Drinks';
	String get tea => 'Tea';
	String get softDrinks => 'Soft drinks';
	String get coffee => 'Coffee';
	String get beer => 'Beer';
	String get cocktails => 'Cocktails';
	String get juice => 'Juice';
	String get smoothie => 'Smoothie';
	String get wine => 'Wine';
	String get iceTea => 'Ice Tea';
}

// Path: screens.onboarding.food
class _StringsScreensOnboardingFoodEn {
	_StringsScreensOnboardingFoodEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Food';
	String get kebab => 'Kebab';
	String get sweets => 'Sweets';
	String get pizza => 'Pizza';
	String get burger => 'Burger';
	String get sushi => 'Sushi';
	String get pasta => 'Pasta';
	String get fish => 'Fish';
	String get salad => 'Salad';
	String get fingerFood => 'Finger Food';
}

// Path: screens.onboarding.events
class _StringsScreensOnboardingEventsEn {
	_StringsScreensOnboardingEventsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Events';
	String get party => 'Party';
	String get concert => 'Concert';
	String get markets => 'Markets';
	String get tastings => 'Tastings';
	String get workshops => 'Workshops';
	String get sports => 'Sports';
	String get theater => 'Theater';
	String get readings => 'Readings';
	String get cinema => 'Cinema';
}

// Path: screens.notificationPermission.buttons
class _StringsScreensNotificationPermissionButtonsEn {
	_StringsScreensNotificationPermissionButtonsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get decideLater => 'Decide later';
	String get next => 'Continue';
}

// Path: screens.discover.info
class _StringsScreensDiscoverInfoEn {
	_StringsScreensDiscoverInfoEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get timeLabel => 'Opening Hours';
}

// Path: screens.restaurant.booking
class _StringsScreensRestaurantBookingEn {
	_StringsScreensRestaurantBookingEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String title({required Object name}) => 'Hi${name}, it would be great to offer you a table.';
	String get calendarButtonLabel => 'Next';
	String get timeButtonLabel => 'Book';
	String get resultButtonLabel => 'Finish';
	String get errorButtonLabel => 'Ok';
	String get availableTimeLabel => 'Available times';
	String get estimatedTimeLabel => 'Estimated time of your visit';
	String get numberOfDinnersLabel => 'Number of diners';
	String get resultTitle => 'You reserved a\ntable at the';
	String get resultDescription => 'We\'re gonna send you a confirmation via Mail and Notification.';
}

// Path: screens.restaurant.info
class _StringsScreensRestaurantInfoEn {
	_StringsScreensRestaurantInfoEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get scheduleTitle => 'Opening Hours';
}

// Path: screens.restaurant.menu
class _StringsScreensRestaurantMenuEn {
	_StringsScreensRestaurantMenuEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get empty => 'No menu available';
	String get ratingFilter => '4+';
	String get budgetFilter => 'Budget';
	String get budgetFilterHint => '15.99';
	String get extras => 'Extras';
	String get sauces => 'Sauces';
}

// Path: screens.restaurant.deals
class _StringsScreensRestaurantDealsEn {
	_StringsScreensRestaurantDealsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Specials';
	String get empty => 'No specials available';
}

// Path: screens.profile.tabs
class _StringsScreensProfileTabsEn {
	_StringsScreensProfileTabsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get post => 'Posts';
	String get settings => 'Settings';
}

// Path: screens.profile.settings
class _StringsScreensProfileSettingsEn {
	_StringsScreensProfileSettingsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get general => 'General Settings';
	String get help => 'Help';
	String get feedback => 'Feedback';
	String get orders => 'Previous Orders';
	String get logout => 'Log out';
}

// Path: screens.subscription.levels
class _StringsScreensSubscriptionLevelsEn {
	_StringsScreensSubscriptionLevelsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get test => 'Just Test';
	String get member => 'Member';
	String get silver => 'Silver';
	String get gold => 'Gold';
}

// Path: screens.subscription.levelsDescription
class _StringsScreensSubscriptionLevelsDescriptionEn {
	_StringsScreensSubscriptionLevelsDescriptionEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get test => 'Try MUTUALZ and use all our coupons for free';
	String get member => 'You pay 5% less on your whole bill.';
	String get silver => 'You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 10% on your whole bill (only in the restaurants in which the subscription was reached)';
	String get gold => 'You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 15% on your whole bill (only in the restaurants in which the subscription was reached)';
}

// Path: screens.subscription.buttons
class _StringsScreensSubscriptionButtonsEn {
	_StringsScreensSubscriptionButtonsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get upgrade => 'Upgrade now for 9,99€ monthly';
	String get recommend => 'Recommend us to a friend to get 1 month for free';
	String get cancel => 'Unsubscribe and loose your benefits';
	String get reactivate => 'Reactivate your subscription';
}

// Path: screens.subscription.discount
class _StringsScreensSubscriptionDiscountEn {
	_StringsScreensSubscriptionDiscountEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String withPercent({required num n, required Object percent}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('en'))(n,
		one: '${percent}% for 1 day',
		other: '${percent}% for ${n} days',
	);
}

// Path: screens.cart.dialogNotes
class _StringsScreensCartDialogNotesEn {
	_StringsScreensCartDialogNotesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Do you have any special requirements?';
	String get hint => 'Special request';
}

// Path: screens.cart.dialogRemove
class _StringsScreensCartDialogRemoveEn {
	_StringsScreensCartDialogRemoveEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Remove dish from the cart';
	String get description => 'Are you sure you want to remove this dish from the cart?';
	String get buttonCancel => 'Cancel';
	String get buttonConfirm => 'I\'m sure';
}

// Path: screens.cart.dialogSpecialRemove
class _StringsScreensCartDialogSpecialRemoveEn {
	_StringsScreensCartDialogSpecialRemoveEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Remove special from the cart';
	String get description => 'Are you sure you want to remove this special from the cart?';
	String get buttonCancel => 'Cancel';
	String get buttonConfirm => 'I\'m sure';
}

// Path: screens.cart.dialogConfirm
class _StringsScreensCartDialogConfirmEn {
	_StringsScreensCartDialogConfirmEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Are you sure you\'ve found everything?';
	String get description => 'Then order here';
	String get buttonConfirm => 'Confirm Selection';
}

// Path: screens.cart.dialogShare
class _StringsScreensCartDialogShareEn {
	_StringsScreensCartDialogShareEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get description => 'Recommend us to your friends to get a 1 month membership for free and save more money on your bill';
	String get buttonShare => 'To Share';
	String get buttonCancel => 'Share later';
}

// Path: screens.review.ratingItems
class _StringsScreensReviewRatingItemsEn {
	_StringsScreensReviewRatingItemsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get service => 'Service';
	String get cleanliness => 'Cleanliness';
	String get waitingTime => 'Waiting time';
	String get foodQuality => 'Food quality';
	String get athmosphere => 'Athmosphere';
}

// Path: screens.review.dietPreferences
class _StringsScreensReviewDietPreferencesEn {
	_StringsScreensReviewDietPreferencesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get yes => 'Yes';
	String get no => 'No';
}

// Path: tutorial.loyalty.buttons
class _StringsTutorialLoyaltyButtonsEn {
	_StringsTutorialLoyaltyButtonsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get later => 'Rather later...';
	String get subscribe => 'Subscribe';
	String get next => 'Continue';
}

// Path: screens.auth.registration.labels
class _StringsScreensAuthRegistrationLabelsEn {
	_StringsScreensAuthRegistrationLabelsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get gender => 'Gender';
	String get firstName => 'First name';
	String get lastName => 'Last name';
	String get username => 'Username*';
	String get birthDate => 'Birth date*';
	String get email => 'Mail*';
	String get phone => 'Phone number';
	String get password => 'Password*';
	String get confirmPassword => 'Repeat password*';
}

// Path: screens.auth.registration.terms
class _StringsScreensAuthRegistrationTermsEn {
	_StringsScreensAuthRegistrationTermsEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get agreement => 'I agree to ';
	String get conditions => 'Terms & Conditions';
	String get separator => ' and ';
	String get privacy => 'Privacy Policy';
}

// Path: screens.onboarding.lifestyle.job
class _StringsScreensOnboardingLifestyleJobEn {
	_StringsScreensOnboardingLifestyleJobEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Occupation/Job';
	String get employee => 'Employee';
	String get student => 'Student';
	String get collegeStudent => 'College Student';
	String get selfEmployed => 'Self-employed';
	String get housewife => 'Househusband/housewife';
	String get retired => 'Retired';
}

// Path: screens.onboarding.lifestyle.allergies
class _StringsScreensOnboardingLifestyleAllergiesEn {
	_StringsScreensOnboardingLifestyleAllergiesEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Intolerances/allergies';
	String get gluten => 'Gluten Free';
	String get lactose => 'Lactose Free';
	String get nutAllergy => 'Nut allergy';
}

// Path: screens.onboarding.lifestyle.diet
class _StringsScreensOnboardingLifestyleDietEn {
	_StringsScreensOnboardingLifestyleDietEn._(this._root);

	final Translations _root; // ignore: unused_field

	// Translations
	String get title => 'Dietary preferences';
	String get halal => '100% Halal';
	String get kosher => 'Kosher';
	String get vegan => 'Vegan';
	String get vegetarian => 'Vegetarian';
}

// Path: <root>
class _StringsDe implements Translations {
	/// You can call this constructor and build your own translation instance of this locale.
	/// Constructing via the enum [AppLocale.build] is preferred.
	_StringsDe.build({Map<String, Node>? overrides, PluralResolver? cardinalResolver, PluralResolver? ordinalResolver})
		: assert(overrides == null, 'Set "translation_overrides: true" in order to enable this feature.'),
		  $meta = TranslationMetadata(
		    locale: AppLocale.de,
		    overrides: overrides ?? {},
		    cardinalResolver: cardinalResolver,
		    ordinalResolver: ordinalResolver,
		  ) {
		$meta.setFlatMapFunction(_flatMapFunction);
	}

	/// Metadata for the translations of <de>.
	@override final TranslationMetadata<AppLocale, Translations> $meta;

	/// Access flat map
	@override dynamic operator[](String key) => $meta.getTranslation(key);

	@override late final _StringsDe _root = this; // ignore: unused_field

	// Translations
	@override String get language => 'Deutsch';
	@override String get title => 'Mutualz';
	@override String get titleDev => 'Mutualz Dev';
	@override late final _StringsErrorsDe errors = _StringsErrorsDe._(_root);
	@override late final _StringsButtonsDe buttons = _StringsButtonsDe._(_root);
	@override late final _StringsDialogsDe dialogs = _StringsDialogsDe._(_root);
	@override late final _StringsScreensDe screens = _StringsScreensDe._(_root);
	@override late final _StringsBnbDe bnb = _StringsBnbDe._(_root);
	@override late final _StringsFiltersDe filters = _StringsFiltersDe._(_root);
	@override late final _StringsToolsDe tools = _StringsToolsDe._(_root);
	@override late final _StringsCustomDe custom = _StringsCustomDe._(_root);
	@override late final _StringsEmptyDe empty = _StringsEmptyDe._(_root);
	@override late final _StringsCouponsDe coupons = _StringsCouponsDe._(_root);
	@override late final _StringsCouponDe coupon = _StringsCouponDe._(_root);
	@override late final _StringsCitiesDe cities = _StringsCitiesDe._(_root);
	@override late final _StringsSpecialsDe specials = _StringsSpecialsDe._(_root);
	@override late final _StringsGendersDe genders = _StringsGendersDe._(_root);
	@override late final _StringsRestaurantInfoTabDe restaurantInfoTab = _StringsRestaurantInfoTabDe._(_root);
	@override late final _StringsScannerDe scanner = _StringsScannerDe._(_root);
	@override late final _StringsPostCardDe postCard = _StringsPostCardDe._(_root);
	@override late final _StringsWeekdaysDe weekdays = _StringsWeekdaysDe._(_root);
	@override late final _StringsPaymentInfoDe paymentInfo = _StringsPaymentInfoDe._(_root);
	@override late final _StringsLoyaltyDe loyalty = _StringsLoyaltyDe._(_root);
	@override late final _StringsEarnWidgetDe earnWidget = _StringsEarnWidgetDe._(_root);
	@override late final _StringsReportUserPostDe reportUserPost = _StringsReportUserPostDe._(_root);
	@override late final _StringsBlockUserPostDe blockUserPost = _StringsBlockUserPostDe._(_root);
	@override late final _StringsBlockUserDe blockUser = _StringsBlockUserDe._(_root);
	@override late final _StringsReportUserDe reportUser = _StringsReportUserDe._(_root);
	@override late final _StringsStrangerProfileDe strangerProfile = _StringsStrangerProfileDe._(_root);
	@override late final _StringsTutorialDe tutorial = _StringsTutorialDe._(_root);
}

// Path: errors
class _StringsErrorsDe implements _StringsErrorsEn {
	_StringsErrorsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get fieldEmpty => 'Dieses Feld sollte nicht leer sein';
	@override String get emailInvalid => 'Ungültige E-Mail-Adresse';
	@override String get passwordInvalid => 'Das Passwort sollte mindestens 8 Zeichen lang sein.';
	@override String get userNameInvalid => 'Der Benutzername sollte mindestens 2 Zeichen lang sein.';
	@override String get passwordNotMatch => 'Die Passwörter stimmen nicht überein';
	@override String get otpEmpty => 'OTP sollte nicht leer sein';
	@override String get unknown => 'Unbekannter Fehler';
	@override String get errorAuthConflict => 'Authentifizierungskonflikt';
	@override String get errorAuthTokenExpired => 'Authentifizierungstoken abgelaufen';
	@override String get errorSocialAuthFailed => 'Soziale Authentifizierung fehlgeschlagen';
	@override String get errorAuthOperationInProgress => 'Authentifizierungsvorgang läuft';
	@override String get errorAuthCancelled => 'Authentifizierung abgebrochen';
	@override String get privacyNotAccepted => 'Datenschutzerklärung und Allgemeine Geschäftsbedingungen nicht akzeptiert';
	@override String get baseTextLength => 'Der Text sollte zwischen 2 und 60 Zeichen lang sein.';
	@override String get phoneNumberInvalid => 'Ungültige Telefonnummer';
	@override String get doesNotContainNumber => 'Das Passwort sollte mindestens eine Zahl enthalten';
	@override String get doesNotContainSpecialCharacter => 'Das Passwort sollte mindestens ein Sonderzeichen enthalten';
	@override String get doesNotContainUppercase => 'Das Passwort muss mindestens einen Großbuchstaben enthalten';
	@override String get doesNotContainLowercase => 'Das Passwort muss mindestens einen Kleinbuchstaben enthalten';
	@override String get disabledLocationService => 'Standortdienste sind deaktiviert. Bitte aktiviere diese, um fortzufahren.';
	@override String get locationPermissionDenied => 'Standortberechtigungen sind verweigert. Bitte aktiviere diese, um fortzufahren.';
	@override String get locationPermissionPermanentlyDenied => 'Standortberechtigungen sind dauerhaft verweigert. Wir können keine Berechtigungen anfordern.';
	@override String get noRestaurantImage => 'Kein Restaurantbild verfügbar';
	@override String get emptyRating => 'Bitte vergib Sterne für alle Bewertungskriterien.';
	@override String get defaultPaymentMethod => 'Bitte wähle eine Zahlungsmethode aus';
	@override String get restartError => 'Ein Fehler ist aufgetreten. Bitte starte die App neu.';
}

// Path: buttons
class _StringsButtonsDe implements _StringsButtonsEn {
	_StringsButtonsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get save => 'Speichern';
	@override String get send => 'Senden';
	@override String get ok => 'OK';
	@override String get add => '';
	@override String get edit => 'Bearbeiten';
	@override String get order => 'Bestellen';
	@override String get pay => 'Bezahlen';
	@override String get orderAndPay => 'Bestellen & Zahlen';
	@override String get seeAll => 'Alle anzeigen';
	@override String get back => 'Zurück';
	@override String get savePoints => 'Mehr Punkte sammeln';
	@override String get tryAgain => 'Nochmals versuchen';
	@override String get tryAgainLater => 'Später erneut versuchen';
	@override String get skip => 'Überspringen';
}

// Path: dialogs
class _StringsDialogsDe implements _StringsDialogsEn {
	_StringsDialogsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsDialogsErrorDe error = _StringsDialogsErrorDe._(_root);
	@override late final _StringsDialogsSuccessDe success = _StringsDialogsSuccessDe._(_root);
	@override String get buttonLabel => 'OK';
	@override late final _StringsDialogsOptionsDe options = _StringsDialogsOptionsDe._(_root);
	@override late final _StringsDialogsNumberTableDe numberTable = _StringsDialogsNumberTableDe._(_root);
}

// Path: screens
class _StringsScreensDe implements _StringsScreensEn {
	_StringsScreensDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsScreensSplashDe splash = _StringsScreensSplashDe._(_root);
	@override late final _StringsScreensAuthDe auth = _StringsScreensAuthDe._(_root);
	@override late final _StringsScreensForgotDe forgot = _StringsScreensForgotDe._(_root);
	@override late final _StringsScreensResetDe reset = _StringsScreensResetDe._(_root);
	@override late final _StringsScreensOnboardingDe onboarding = _StringsScreensOnboardingDe._(_root);
	@override late final _StringsScreensSocialOnboardingDe socialOnboarding = _StringsScreensSocialOnboardingDe._(_root);
	@override late final _StringsScreensNotificationPermissionDe notificationPermission = _StringsScreensNotificationPermissionDe._(_root);
	@override late final _StringsScreensSearchDe search = _StringsScreensSearchDe._(_root);
	@override late final _StringsScreensDiscoverDe discover = _StringsScreensDiscoverDe._(_root);
	@override late final _StringsScreensRestaurantDe restaurant = _StringsScreensRestaurantDe._(_root);
	@override late final _StringsScreensProfileDe profile = _StringsScreensProfileDe._(_root);
	@override late final _StringsScreensGeneralSettingsDe generalSettings = _StringsScreensGeneralSettingsDe._(_root);
	@override late final _StringsScreensChangePasswordSettingsDe changePasswordSettings = _StringsScreensChangePasswordSettingsDe._(_root);
	@override late final _StringsScreensChangePreferencesDe changePreferences = _StringsScreensChangePreferencesDe._(_root);
	@override late final _StringsScreensUserProfileSettingsDe userProfileSettings = _StringsScreensUserProfileSettingsDe._(_root);
	@override late final _StringsScreensFeedbackDe feedback = _StringsScreensFeedbackDe._(_root);
	@override late final _StringsScreensFaqDe faq = _StringsScreensFaqDe._(_root);
	@override late final _StringsScreensContactDe contact = _StringsScreensContactDe._(_root);
	@override late final _StringsScreensSubscriptionDe subscription = _StringsScreensSubscriptionDe._(_root);
	@override late final _StringsScreensCommentsDe comments = _StringsScreensCommentsDe._(_root);
	@override late final _StringsScreensOrderProcessDe orderProcess = _StringsScreensOrderProcessDe._(_root);
	@override late final _StringsScreensCartDe cart = _StringsScreensCartDe._(_root);
	@override late final _StringsScreensInvoiceDe invoice = _StringsScreensInvoiceDe._(_root);
	@override late final _StringsScreensPaymentMethodSettingsDe paymentMethodSettings = _StringsScreensPaymentMethodSettingsDe._(_root);
	@override late final _StringsScreensAddPostDe addPost = _StringsScreensAddPostDe._(_root);
	@override late final _StringsScreensPaymentDe payment = _StringsScreensPaymentDe._(_root);
	@override late final _StringsScreensOrdersDe orders = _StringsScreensOrdersDe._(_root);
	@override late final _StringsScreensProgressRewardDe progressReward = _StringsScreensProgressRewardDe._(_root);
	@override late final _StringsScreensReviewDe review = _StringsScreensReviewDe._(_root);
}

// Path: bnb
class _StringsBnbDe implements _StringsBnbEn {
	_StringsBnbDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get discover => 'Entdecken';
	@override String get feed => 'Feed';
	@override String get search => 'Suche';
	@override String get profile => 'Profil';
}

// Path: filters
class _StringsFiltersDe implements _StringsFiltersEn {
	_StringsFiltersDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get foodTypeTitle => 'Kulinarische Ausrichtung';
	@override String get dietPreferencesTitle => 'Ernährungsform';
	@override String get foodTitle => 'Essenszeiten';
	@override String get sortTitle => 'Sortieren';
	@override late final _StringsFiltersFoodTypeDe foodType = _StringsFiltersFoodTypeDe._(_root);
	@override late final _StringsFiltersDietPreferencesDe dietPreferences = _StringsFiltersDietPreferencesDe._(_root);
	@override late final _StringsFiltersFoodDe food = _StringsFiltersFoodDe._(_root);
	@override late final _StringsFiltersSortDe sort = _StringsFiltersSortDe._(_root);
}

// Path: tools
class _StringsToolsDe implements _StringsToolsEn {
	_StringsToolsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsToolsImagePickerDe imagePicker = _StringsToolsImagePickerDe._(_root);
}

// Path: custom
class _StringsCustomDe implements _StringsCustomEn {
	_StringsCustomDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsCustomPointProgressDe pointProgress = _StringsCustomPointProgressDe._(_root);
	@override String get isReached => 'erreicht';
	@override String get youMadeIt => 'Du hast es geschafft';
}

// Path: empty
class _StringsEmptyDe implements _StringsEmptyEn {
	_StringsEmptyDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get image => 'Keine Ergebnisse';
}

// Path: coupons
class _StringsCouponsDe implements _StringsCouponsEn {
	_StringsCouponsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get available => 'Verfügbare Gutscheine';
	@override String get activatable => 'Aktivierbare Gutscheine';
	@override String get noCoupons => 'No available coupons';
	@override String get appliable => 'Anwendbar';
	@override String get canBeReactivated => 'Kann reaktiviert werden';
	@override String get nonAppliable => 'Nicht zutreffend';
}

// Path: coupon
class _StringsCouponDe implements _StringsCouponEn {
	_StringsCouponDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Mutualz';
	@override String get off => 'Rabatt';
	@override String percentage({required Object value}) => '${value}%';
	@override String get separator => 'für';
}

// Path: cities
class _StringsCitiesDe implements _StringsCitiesEn {
	_StringsCitiesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get berlin => 'Berlin';
	@override String get oldenburg => 'Oldenburg';
	@override String get osnaburg => 'Osnabrück';
}

// Path: specials
class _StringsSpecialsDe implements _StringsSpecialsEn {
	_StringsSpecialsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Sonderangebote';
	@override String get single => 'Sonderangebot';
	@override String get availableTitle => 'Sonderangebot erhältlich';
	@override String get availableAt => 'Genießen um';
}

// Path: genders
class _StringsGendersDe implements _StringsGendersEn {
	_StringsGendersDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get male => 'Männlich';
	@override String get female => 'Weiblich';
	@override String get diverse => 'Divers';
}

// Path: restaurantInfoTab
class _StringsRestaurantInfoTabDe implements _StringsRestaurantInfoTabEn {
	_StringsRestaurantInfoTabDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get deals => 'Angebote';
	@override String get menu => 'Speisekarte';
	@override String get feed => 'Feed';
}

// Path: scanner
class _StringsScannerDe implements _StringsScannerEn {
	_StringsScannerDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get label => 'Scanne den QR-Code um zu bestellen';
	@override String get errorDifferentRestaurant => 'Du scannst einen QR-Code von einem anderen Restaurant';
	@override String errorRestaurantClosed({required Object name}) => '${name} ist geschlossen. Versuche es während der Öffnungszeiten erneut.';
}

// Path: postCard
class _StringsPostCardDe implements _StringsPostCardEn {
	_StringsPostCardDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get likes => 'Likes';
	@override String get comments => 'Kommentare';
}

// Path: weekdays
class _StringsWeekdaysDe implements _StringsWeekdaysEn {
	_StringsWeekdaysDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get monday => 'Montag';
	@override String get tuesday => 'Dienstag';
	@override String get wednesday => 'Mittwoch';
	@override String get thursday => 'Donnerstag';
	@override String get friday => 'Freitag';
	@override String get saturday => 'Samstag';
	@override String get sunday => 'Sonntag';
}

// Path: paymentInfo
class _StringsPaymentInfoDe implements _StringsPaymentInfoEn {
	_StringsPaymentInfoDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get total => 'Gesamt';
	@override String get discount => 'Rabatt';
	@override String get totalWithMutualz => 'Gesamt mit Mutualz';
}

// Path: loyalty
class _StringsLoyaltyDe implements _StringsLoyaltyEn {
	_StringsLoyaltyDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get test => 'Test';
	@override String get member => 'Member';
	@override String get silver => 'Silber';
	@override String get gold => 'Gold';
}

// Path: earnWidget
class _StringsEarnWidgetDe implements _StringsEarnWidgetEn {
	_StringsEarnWidgetDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get label => 'Punkte sammeln';
	@override String get points => 'Punkte';
}

// Path: reportUserPost
class _StringsReportUserPostDe implements _StringsReportUserPostEn {
	_StringsReportUserPostDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Beitrag melden';
	@override String get hint => 'Gib den Grund für die Meldung an';
	@override late final _StringsReportUserPostItemsDe items = _StringsReportUserPostItemsDe._(_root);
}

// Path: blockUserPost
class _StringsBlockUserPostDe implements _StringsBlockUserPostEn {
	_StringsBlockUserPostDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Beitrag melden';
	@override String get hint => 'Gib den Grund für die Meldung an';
	@override late final _StringsBlockUserPostItemsDe items = _StringsBlockUserPostItemsDe._(_root);
}

// Path: blockUser
class _StringsBlockUserDe implements _StringsBlockUserEn {
	_StringsBlockUserDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Benutzer melden';
	@override String get hint => 'Gib den Grund für die Meldung an';
	@override late final _StringsBlockUserItemsDe items = _StringsBlockUserItemsDe._(_root);
}

// Path: reportUser
class _StringsReportUserDe implements _StringsReportUserEn {
	_StringsReportUserDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Benutzer melden';
	@override String get hint => 'Gib den Grund für die Meldung an';
	@override late final _StringsReportUserItemsDe items = _StringsReportUserItemsDe._(_root);
}

// Path: strangerProfile
class _StringsStrangerProfileDe implements _StringsStrangerProfileEn {
	_StringsStrangerProfileDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get followers => 'Follower';
	@override String get following => 'Folgt';
	@override String get friendsInCommon => 'Gemeinsame Freunde';
}

// Path: tutorial
class _StringsTutorialDe implements _StringsTutorialEn {
	_StringsTutorialDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsTutorialMapDe map = _StringsTutorialMapDe._(_root);
	@override late final _StringsTutorialFilterDe filter = _StringsTutorialFilterDe._(_root);
	@override late final _StringsTutorialFeedDe feed = _StringsTutorialFeedDe._(_root);
	@override late final _StringsTutorialRestaurantDe restaurant = _StringsTutorialRestaurantDe._(_root);
	@override late final _StringsTutorialProfileDe profile = _StringsTutorialProfileDe._(_root);
	@override late final _StringsTutorialOrdersDe orders = _StringsTutorialOrdersDe._(_root);
	@override late final _StringsTutorialLoyaltyDe loyalty = _StringsTutorialLoyaltyDe._(_root);
}

// Path: dialogs.error
class _StringsDialogsErrorDe implements _StringsDialogsErrorEn {
	_StringsDialogsErrorDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Fehler';
}

// Path: dialogs.success
class _StringsDialogsSuccessDe implements _StringsDialogsSuccessEn {
	_StringsDialogsSuccessDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Ausgezeichnet!';
	@override String get changes => 'Deine Änderungen wurden gespeichert';
}

// Path: dialogs.options
class _StringsDialogsOptionsDe implements _StringsDialogsOptionsEn {
	_StringsDialogsOptionsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get deletePost => 'Beitrag löschen';
	@override String get report => 'Melden';
	@override String get unfollow => 'Nicht mehr folgen';
	@override String get block => 'Blockieren';
	@override String get follow => 'Folgen';
}

// Path: dialogs.numberTable
class _StringsDialogsNumberTableDe implements _StringsDialogsNumberTableEn {
	_StringsDialogsNumberTableDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Gib deine Tischnummer ein.';
	@override String get hint => 'Tischnummer';
}

// Path: screens.splash
class _StringsScreensSplashDe implements _StringsScreensSplashEn {
	_StringsScreensSplashDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Mutualz';
}

// Path: screens.auth
class _StringsScreensAuthDe implements _StringsScreensAuthEn {
	_StringsScreensAuthDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get welcome => 'Willkommen\nbei Mutualz';
	@override late final _StringsScreensAuthTabsDe tabs = _StringsScreensAuthTabsDe._(_root);
	@override late final _StringsScreensAuthLoginDe login = _StringsScreensAuthLoginDe._(_root);
	@override late final _StringsScreensAuthRegistrationDe registration = _StringsScreensAuthRegistrationDe._(_root);
	@override late final _StringsScreensAuthOptDe opt = _StringsScreensAuthOptDe._(_root);
}

// Path: screens.forgot
class _StringsScreensForgotDe implements _StringsScreensForgotEn {
	_StringsScreensForgotDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Passwort vergessen';
	@override String get description => 'Bitte gib die E-Mail-Adresse ein, mit der du dich registriert hast. Ein Link zum Ändern deines Passworts wird dir umgehend zugesandt.';
	@override String get emailLabel => 'E-Mail';
	@override String get buttonLabel => 'Senden';
	@override String get backButtonLabel => 'Zurück';
	@override String get successDescription => 'Du kannst deine E-Mail überprüfen, um den Link zur Wiederherstellung des Passworts zu finden';
}

// Path: screens.reset
class _StringsScreensResetDe implements _StringsScreensResetEn {
	_StringsScreensResetDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Ändere dein Passwort';
	@override String get description => 'Füge hier dein neues Passwort ein';
	@override String get passwordLabel => 'Passwort*';
	@override String get repeatPasswordLabel => 'Passwort wiederholen*';
	@override String get buttonLabel => 'Passwort ändern';
	@override String get successMessage => 'Herzlichen Glückwunsch, dein Passwort wurde geändert';
}

// Path: screens.onboarding
class _StringsScreensOnboardingDe implements _StringsScreensOnboardingEn {
	_StringsScreensOnboardingDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsScreensOnboardingPersonalInfoDe personalInfo = _StringsScreensOnboardingPersonalInfoDe._(_root);
	@override late final _StringsScreensOnboardingLifestyleDe lifestyle = _StringsScreensOnboardingLifestyleDe._(_root);
	@override late final _StringsScreensOnboardingTasteDe taste = _StringsScreensOnboardingTasteDe._(_root);
	@override late final _StringsScreensOnboardingDrinksDe drinks = _StringsScreensOnboardingDrinksDe._(_root);
	@override late final _StringsScreensOnboardingFoodDe food = _StringsScreensOnboardingFoodDe._(_root);
	@override late final _StringsScreensOnboardingEventsDe events = _StringsScreensOnboardingEventsDe._(_root);
	@override String get nextButtonLabel => 'Weiter';
	@override String get backButtonLabel => 'Zurück';
}

// Path: screens.socialOnboarding
class _StringsScreensSocialOnboardingDe implements _StringsScreensSocialOnboardingEn {
	_StringsScreensSocialOnboardingDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Lernen wir uns besser kennen';
	@override String get description => 'Erzähle uns etwas über dich';
	@override String get nextButtonLabel => 'Weiter';
	@override String get skipButtonLabel => 'Überspringen';
}

// Path: screens.notificationPermission
class _StringsScreensNotificationPermissionDe implements _StringsScreensNotificationPermissionEn {
	_StringsScreensNotificationPermissionDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'MITTEILUNGEN';
	@override String get description => 'Stelle deine Benachrichtigungen an, damit wir dich informieren können, wenn dein Essen fertig ist.';
	@override late final _StringsScreensNotificationPermissionButtonsDe buttons = _StringsScreensNotificationPermissionButtonsDe._(_root);
}

// Path: screens.search
class _StringsScreensSearchDe implements _StringsScreensSearchEn {
	_StringsScreensSearchDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get noResults => 'Es wurden leider keine Ergebnisse gefunden';
	@override String get inputLabel => 'Suche...';
	@override String get sortByRating => 'Nach Bewertung';
	@override String get emptyCollection => 'Wir müssen Sie\nbesser kennen lernen';
}

// Path: screens.discover
class _StringsScreensDiscoverDe implements _StringsScreensDiscoverEn {
	_StringsScreensDiscoverDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsScreensDiscoverInfoDe info = _StringsScreensDiscoverInfoDe._(_root);
}

// Path: screens.restaurant
class _StringsScreensRestaurantDe implements _StringsScreensRestaurantEn {
	_StringsScreensRestaurantDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get bookTableButton => 'Tisch reservieren';
	@override String get orderPayButton => 'Hier bestellen';
	@override late final _StringsScreensRestaurantBookingDe booking = _StringsScreensRestaurantBookingDe._(_root);
	@override late final _StringsScreensRestaurantInfoDe info = _StringsScreensRestaurantInfoDe._(_root);
	@override late final _StringsScreensRestaurantMenuDe menu = _StringsScreensRestaurantMenuDe._(_root);
	@override String get emptyDietPreferences => 'Keine Angaben zur Ernährungsform oder Unverträglichkeiten';
	@override late final _StringsScreensRestaurantDealsDe deals = _StringsScreensRestaurantDealsDe._(_root);
}

// Path: screens.profile
class _StringsScreensProfileDe implements _StringsScreensProfileEn {
	_StringsScreensProfileDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get hint => 'Dieses Profil wurde gelöscht.';
	@override late final _StringsScreensProfileTabsDe tabs = _StringsScreensProfileTabsDe._(_root);
	@override String get followers => 'Follower';
	@override String get following => 'Du folgst';
	@override String get subscriptions => 'Stufen';
	@override late final _StringsScreensProfileSettingsDe settings = _StringsScreensProfileSettingsDe._(_root);
}

// Path: screens.generalSettings
class _StringsScreensGeneralSettingsDe implements _StringsScreensGeneralSettingsEn {
	_StringsScreensGeneralSettingsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get profile => 'Profil';
	@override String get password => 'Passwort ändern';
	@override String get preferences => 'Präferenzen';
	@override String get payment => 'Zahlungsmethode';
}

// Path: screens.changePasswordSettings
class _StringsScreensChangePasswordSettingsDe implements _StringsScreensChangePasswordSettingsEn {
	_StringsScreensChangePasswordSettingsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Passwort ändern';
	@override String get newPassword => 'Neues Passwort';
	@override String get confirmNewPassword => 'Neues Passwort bestätigen';
	@override String get buttonLabel => 'Passwort ändern';
}

// Path: screens.changePreferences
class _StringsScreensChangePreferencesDe implements _StringsScreensChangePreferencesEn {
	_StringsScreensChangePreferencesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get buttonLabel => 'Präferenzen speichern';
}

// Path: screens.userProfileSettings
class _StringsScreensUserProfileSettingsDe implements _StringsScreensUserProfileSettingsEn {
	_StringsScreensUserProfileSettingsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get deleteAccount => 'Konto löschen';
	@override String get deleteAccountTitle => 'Bist du sicher, dass du dein Konto löschen möchtest?';
	@override String get deleteAccountDescription => 'Du verlierst den Zugang zu allen exklusiven Rabatten von Mutualz und dein Profil und deine Beiträge werden endgültig gelöscht.';
	@override String get buttonCancel => 'Nicht löschen';
	@override String get buttonOk => 'Ich bin sicher';
}

// Path: screens.feedback
class _StringsScreensFeedbackDe implements _StringsScreensFeedbackEn {
	_StringsScreensFeedbackDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Feedback';
	@override String get messageLabel => 'Nachricht';
	@override String get design => 'Design';
	@override String get userFriendliness => 'Benutzerfreundlichkeit';
	@override String get paymentFunction => 'Zahlungsfunktion';
	@override String get orderFunction => 'Bestellfunktion';
	@override String get community => 'Community';
	@override String get couponsDiscounts => 'Gutscheine & Rabatte';
	@override String get recommendations => 'Empfehlungen';
	@override String get pushNotifications => 'Push-Benachrichtigungen';
	@override String get speed => 'Geschwindigkeit';
	@override String get otherFeatures => 'Andere Funktionen';
	@override String get successMessage => 'Vielen Dank für dein Feedback!.';
}

// Path: screens.faq
class _StringsScreensFaqDe implements _StringsScreensFaqEn {
	_StringsScreensFaqDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Häufig gestellte Fragen';
	@override String get contactUs => 'Kontaktiere uns';
	@override String get empty => 'Keine Fragen gefunden';
}

// Path: screens.contact
class _StringsScreensContactDe implements _StringsScreensContactEn {
	_StringsScreensContactDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Kontakt-Formular';
	@override String get messageLabel => 'Nachricht';
	@override String get registration => 'Registrierung';
	@override String get payment => 'Zahlungsfunktion';
	@override String get subscription => 'Abo';
	@override String get report => 'Fehler melden';
	@override String get other => 'Sonstiges';
	@override String get successMessage => 'Vielen Dank für deine Nachricht! Wir werden uns so schnell wie möglich bei dir melden.';
}

// Path: screens.subscription
class _StringsScreensSubscriptionDe implements _StringsScreensSubscriptionEn {
	_StringsScreensSubscriptionDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Stufen';
	@override String get description => 'Wähle eine Stufe aus';
	@override late final _StringsScreensSubscriptionLevelsDe levels = _StringsScreensSubscriptionLevelsDe._(_root);
	@override late final _StringsScreensSubscriptionLevelsDescriptionDe levelsDescription = _StringsScreensSubscriptionLevelsDescriptionDe._(_root);
	@override late final _StringsScreensSubscriptionButtonsDe buttons = _StringsScreensSubscriptionButtonsDe._(_root);
	@override String info({required Object date}) => 'Dein Abo läuft am ${date} ab';
	@override late final _StringsScreensSubscriptionDiscountDe discount = _StringsScreensSubscriptionDiscountDe._(_root);
	@override String daysRemaining({required num n}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('de'))(n,
		one: 'Noch 1 Tag',
		other: 'Noch ${n} Tage',
	);
	@override String get discountOnTotalBill => 'Rabatt auf die\nGesamtrechnung*';
}

// Path: screens.comments
class _StringsScreensCommentsDe implements _StringsScreensCommentsEn {
	_StringsScreensCommentsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Kommentare';
	@override String get inputLabel => 'Schreibe einen Kommentar';
	@override String get empty => 'Keine Kommentare verfügbar';
}

// Path: screens.orderProcess
class _StringsScreensOrderProcessDe implements _StringsScreensOrderProcessEn {
	_StringsScreensOrderProcessDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Speisekarte';
	@override String get empty => 'Keine Speisekarte verfügbar';
	@override String get orderButton => 'Bestellen/Bezahlen';
	@override String get orderSuccess => 'Bestellung erfolgreich. Guten Appetit!';
	@override String get paymentSuccess => 'Zahlung erfolgreich abgeschlossen';
}

// Path: screens.cart
class _StringsScreensCartDe implements _StringsScreensCartEn {
	_StringsScreensCartDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get empty => 'Keine Artikel im Warenkorb';
	@override String get total => 'Gesamt:';
	@override late final _StringsScreensCartDialogNotesDe dialogNotes = _StringsScreensCartDialogNotesDe._(_root);
	@override late final _StringsScreensCartDialogRemoveDe dialogRemove = _StringsScreensCartDialogRemoveDe._(_root);
	@override late final _StringsScreensCartDialogSpecialRemoveDe dialogSpecialRemove = _StringsScreensCartDialogSpecialRemoveDe._(_root);
	@override late final _StringsScreensCartDialogConfirmDe dialogConfirm = _StringsScreensCartDialogConfirmDe._(_root);
	@override late final _StringsScreensCartDialogShareDe dialogShare = _StringsScreensCartDialogShareDe._(_root);
	@override String get createOrderSuccess => 'Bestellung erfolgreich erstellt. Guten Appetit!';
}

// Path: screens.invoice
class _StringsScreensInvoiceDe implements _StringsScreensInvoiceEn {
	_StringsScreensInvoiceDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Danke für deinen Besuch';
	@override String get dishes => 'Gerichte';
	@override String get coupons => 'Gutscheine:';
	@override String get couponSelection => 'Gutschein-Auswahl';
	@override String get doNotUseCoupon => 'Ohne Gutschein';
	@override String get proceedToPayment => 'Zur Zahlung';
}

// Path: screens.paymentMethodSettings
class _StringsScreensPaymentMethodSettingsDe implements _StringsScreensPaymentMethodSettingsEn {
	_StringsScreensPaymentMethodSettingsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Zahlungsmethoden';
	@override String get card => 'Karte';
	@override String get applePay => 'Apple Pay';
	@override String get googlePay => 'Google Pay';
}

// Path: screens.addPost
class _StringsScreensAddPostDe implements _StringsScreensAddPostEn {
	_StringsScreensAddPostDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Teile deine Erfahrungen mit der Community';
	@override String get description => 'Erstelle einen Beitrag über deine besten Momente';
	@override String get uploadImage => 'Bild hochladen';
	@override String get restaurantHint => 'Wähle ein Restaurant aus';
	@override String get descriptionHint => 'Füge hier eine Beschreibung hinzu';
	@override String get attachImage => 'Bild anhängen';
	@override String get tagYourFriends => 'Tagge deine Freunde';
}

// Path: screens.payment
class _StringsScreensPaymentDe implements _StringsScreensPaymentEn {
	_StringsScreensPaymentDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get message => 'Danke für deinen Besuch!';
	@override String get buttonLabel => 'Punkte sammeln';
	@override String get success => 'Zahlung erfolgreich abgeschlossen';
}

// Path: screens.orders
class _StringsScreensOrdersDe implements _StringsScreensOrdersEn {
	_StringsScreensOrdersDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Bestellungen';
	@override String get empty => 'Keine Bestellungen';
	@override String get total => 'Gesamt';
	@override String get memberDiscount => 'Member Rabatt';
	@override String get couponDiscount => 'Gutschein Rabatt';
	@override String get totalWithMutualz => 'Gesamt mit Mutualz';
	@override String get summary => 'Bestellübersicht';
	@override String get sendInvoice => 'Rechnung senden';
	@override String get invoiceSent => 'Rechnung wurde erfolgreich versendet';
	@override String get sendingInvoice => 'Rechnung wird gesendet...';
}

// Path: screens.progressReward
class _StringsScreensProgressRewardDe implements _StringsScreensProgressRewardEn {
	_StringsScreensProgressRewardDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get unlockCoupons => 'Verwende, um Gutscheine freizuschalten';
}

// Path: screens.review
class _StringsScreensReviewDe implements _StringsScreensReviewEn {
	_StringsScreensReviewDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get rating => 'Bewertung';
	@override late final _StringsScreensReviewRatingItemsDe ratingItems = _StringsScreensReviewRatingItemsDe._(_root);
	@override String get commentHint => 'Schreibe einen Kommentar für den Gastronomen';
	@override String get verifyDietary => 'Bestätige die vom Gastronomen gemachten Angaben';
	@override late final _StringsScreensReviewDietPreferencesDe dietPreferences = _StringsScreensReviewDietPreferencesDe._(_root);
	@override String get uploadImage => 'Bild hochladen';
	@override String get descriptionHint => 'Beschreibung';
	@override String get rateTitle => 'Bewerte jetzt, um zusätzliche Punkte zu erhalten';
	@override String get points => 'Punkte';
	@override String get rate => 'Bewerten';
	@override String get rateDescription => 'Bewerte deinen Aufenthalt und verdiene 5 zusätzliche Punkte';
	@override String get rateAndPost => 'Bewerten & Posten';
	@override String get rateAndPostDescription => 'Bewerte und erstelle einen Post über deine Erfahrung und verdiene 10 zusätzliche Punkte';
	@override String get skip => 'Später bewerten';
}

// Path: filters.foodType
class _StringsFiltersFoodTypeDe implements _StringsFiltersFoodTypeEn {
	_StringsFiltersFoodTypeDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get american => 'Amerikanisch';
	@override String get arabic => 'Arabisch';
	@override String get asian => 'Asiatisch';
	@override String get bakery => 'Bäckerei';
	@override String get burger => 'Burger';
	@override String get chinese => 'Chinesisch';
	@override String get curry => 'Curry';
	@override String get german => 'Deutsch';
	@override String get doner => 'Döner';
	@override String get iceCream => 'Eis';
	@override String get espresso => 'Espresso';
	@override String get falafel => 'Falafel';
	@override String get fish => 'Fisch';
	@override String get breakfast => 'Frühstück';
	@override String get drinks => 'Getränke';
	@override String get chicken => 'Hähnchen';
	@override String get hotdog => 'Hotdog';
	@override String get indian => 'Indisch';
	@override String get italian => 'Italienisch';
	@override String get japanese => 'Japanisch';
	@override String get coffee => 'Kaffee';
	@override String get cake => 'Kuchen';
	@override String get seafood => 'Meeresfrüchte';
	@override String get mexican => 'Mexikanisch';
	@override String get lunch => 'Mittagessen';
	@override String get desserts => 'Desserts';
	@override String get noodles => 'Nudeln';
	@override String get austrian => 'Österreichisch';
	@override String get pakistani => 'Pakistani';
	@override String get pasta => 'Pasta';
	@override String get persian => 'Persisch';
	@override String get pizza => 'Pizza';
	@override String get pokeBowl => 'Poke Bowl';
	@override String get ramen => 'Ramen';
	@override String get frenchFries => 'Pommes Frites';
	@override String get salads => 'Salate';
	@override String get sandwiches => 'Sandwiches';
	@override String get snacks => 'Snacks';
	@override String get steaks => 'Steaks';
	@override String get soups => 'Suppen';
	@override String get sushi => 'Sushi';
	@override String get syrian => 'Syrisch';
	@override String get thai => 'Thailändisch';
	@override String get turkish => 'Türkisch';
	@override String get vegan => 'Vegan';
	@override String get vegetarian => 'Vegetarisch';
	@override String get vietnamese => 'Vietnamesisch';
	@override String get wraps => 'Wraps';
	@override String get bar => 'Bar';
	@override String get bowls => 'Bowls';
	@override String get cafe => 'Café';
	@override String get cocktails => 'Cocktails';
	@override String get greek => 'Griechisch';
	@override String get kebab => 'Kebab';
	@override String get restaurant => 'Restaurant';
	@override String get spanish => 'Spanisch';
}

// Path: filters.dietPreferences
class _StringsFiltersDietPreferencesDe implements _StringsFiltersDietPreferencesEn {
	_StringsFiltersDietPreferencesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get kosher => 'Koscher';
	@override String get halal => 'Halal';
	@override String get vegetarian => 'Vegetarisch';
	@override String get vegan => 'Vegan';
	@override String get glutenFree => 'Glutenfrei';
	@override String get lactoseFree => 'Laktosefrei';
	@override String get nutsFree => 'Nussfrei';
}

// Path: filters.food
class _StringsFiltersFoodDe implements _StringsFiltersFoodEn {
	_StringsFiltersFoodDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get breakfast => 'Frühstück';
	@override String get brunch => 'Brunch';
	@override String get lunch => 'Mittagessen';
	@override String get dinner => 'Abendessen';
	@override String get snack => 'Snack';
}

// Path: filters.sort
class _StringsFiltersSortDe implements _StringsFiltersSortEn {
	_StringsFiltersSortDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get rating => 'Nach Bewertung';
}

// Path: tools.imagePicker
class _StringsToolsImagePickerDe implements _StringsToolsImagePickerEn {
	_StringsToolsImagePickerDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Bild auswählen';
}

// Path: custom.pointProgress
class _StringsCustomPointProgressDe implements _StringsCustomPointProgressEn {
	_StringsCustomPointProgressDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get silver => 'Silber';
	@override String get gold => 'Gold';
	@override String get test => 'Test';
	@override String get member => 'Member';
	@override String points({required Object value, required Object opposite}) => '${value}/${opposite} Punkte';
	@override String pointsString({required Object value}) => '${value} Punkte';
}

// Path: reportUserPost.items
class _StringsReportUserPostItemsDe implements _StringsReportUserPostItemsEn {
	_StringsReportUserPostItemsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get nudity => 'Nacktheit oder Sexualisierung';
	@override String get violence => 'Blut oder Gewalt';
	@override String get suicide => 'Suizid, Selbstverletzung oder Essstörungen';
	@override String get hate => 'Hass oder Hetze';
	@override String get ads => 'Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors';
}

// Path: blockUserPost.items
class _StringsBlockUserPostItemsDe implements _StringsBlockUserPostItemsEn {
	_StringsBlockUserPostItemsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get nudity => 'Nacktheit oder Sexualisierung';
	@override String get violence => 'Blut oder Gewalt';
	@override String get suicide => 'Suizid, Selbstverletzung oder Essstörungen';
	@override String get hate => 'Hass oder Hetze';
	@override String get ads => 'Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors';
}

// Path: blockUser.items
class _StringsBlockUserItemsDe implements _StringsBlockUserItemsEn {
	_StringsBlockUserItemsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get insultsOrBullying => 'Beleidigung oder Mobbing';
	@override String get nudityOrSexualization => 'Nacktheit oder Sexualisierung';
	@override String get bloodOrViolence => 'Blut oder Gewalt';
	@override String get spam => 'Spam';
	@override String get falseIdentityOrIdentityStolen => 'Falsche Identität/Identität geklaut';
	@override String get accountCouldHaveBeenHacked => 'Account könnte gehackt worden sein';
	@override String get personUnder16 => 'Person ist unter 16 Jahren';
}

// Path: reportUser.items
class _StringsReportUserItemsDe implements _StringsReportUserItemsEn {
	_StringsReportUserItemsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get insultsOrBullying => 'Beleidigung oder Mobbing';
	@override String get nudityOrSexualization => 'Nacktheit oder Sexualisierung';
	@override String get bloodOrViolence => 'Blut oder Gewalt';
	@override String get spam => 'Spam';
	@override String get falseIdentityOrIdentityStolen => 'Falsche Identität/Identität geklaut';
	@override String get accountCouldHaveBeenHacked => 'Account könnte gehackt worden sein';
	@override String get personUnder16 => 'Person ist unter 16 Jahren';
}

// Path: tutorial.map
class _StringsTutorialMapDe implements _StringsTutorialMapEn {
	_StringsTutorialMapDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get qrCode => 'Beginne deine Bestellung. Hier kannst du den QR-Code scannen, um deine Bestellung aufzugeben.';
}

// Path: tutorial.filter
class _StringsTutorialFilterDe implements _StringsTutorialFilterEn {
	_StringsTutorialFilterDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get info => 'Durchs Filtern verändert sich das Ergebnis auf der Karte und in der Listenansicht. Schiebe die Filter für die Listenansicht nach oben.';
}

// Path: tutorial.feed
class _StringsTutorialFeedDe implements _StringsTutorialFeedEn {
	_StringsTutorialFeedDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get addPost => 'Hier kannst du einen Beitrag posten.';
	@override String get search => 'Du möchtest Freund*innen oder Familie folgen? Hier kannst du die Person suchen.';
}

// Path: tutorial.restaurant
class _StringsTutorialRestaurantDe implements _StringsTutorialRestaurantEn {
	_StringsTutorialRestaurantDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get rating => 'Klicke auf die Sterne oder die Uhr, um weitere Infos zu bekommen';
	@override String get favorite => 'Hier kannst du die Lokalität als Favorit markieren';
	@override String get dietPreferences => 'Ernährungsformen und Unverträglichkeiten. Alle grauen Icons sind Angaben vom Gastronom.';
	@override String get dietPreferencesConfirmed => 'Wenn es rot ist, wurde die Angabe von den Usern bestätigt.';
	@override String get deals => 'Hier siehst du alle Deals und Coupons';
	@override String get menu => 'Hier kannst du vorab schon stöbern';
}

// Path: tutorial.profile
class _StringsTutorialProfileDe implements _StringsTutorialProfileEn {
	_StringsTutorialProfileDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get subscriptions => 'Hier sind alle Stufen, die du im Rahmen des Loyalty-Programms erreichen kannst. Zusätzlich siehst du, wie viele Punkte du insgesamt hast.';
	@override String get orders => 'Hier kannst du deine Rechnungen einsehen und herunterladen';
	@override String get dietPreferences => 'Hier kannst du deine Ernährungspräferenzen anpassen';
}

// Path: tutorial.orders
class _StringsTutorialOrdersDe implements _StringsTutorialOrdersEn {
	_StringsTutorialOrdersDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get tableNumber => 'Die Tischnummer findest du bei dem QR-Code auf dem Tisch';
	@override String get cart => 'Das ist dein Warenkorb, hier siehst du alle Produkte, die du hinzugefügt hast.';
	@override String get cartInside => 'So sieht dein Warenkorb von innen aus. Du kannst hier deine Bestellungen anpassen';
	@override String get orderAndPayNow => 'Hier bestellst und bezahlst du sofort, das macht am meisten Sinn, wenn du z. B. schnell wieder los musst';
	@override String get orderAndPayLater => 'Hier kannst du bestellen und später bezahlen, wenn du die Lokalität verlässt';
	@override String get usedCoupons => 'Diese Coupons hast du schon verwendet und kannst sie mit Punkten wieder freischalten';
	@override String get loyaltyPoints => 'Diese Punkte kannst du bei dieser Lokalität verwenden, um Coupons wieder freizuschalten';
	@override String get congratulations => 'Herzlichen Glückwunsch! Du hast deine ersten Punkte im Loyalty-Programm gesammelt!';
}

// Path: tutorial.loyalty
class _StringsTutorialLoyaltyDe implements _StringsTutorialLoyaltyEn {
	_StringsTutorialLoyaltyDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get fourLevels => 'Es gibt vier Stufen im Loyalty-Programm. Du startest immer in der ersten und hast dann einen Monat Zeit, um bis in die Gold-Stufe aufzusteigen.';
	@override String get test => 'Du Sammelst Punkte mit jedem Einkauf und kommst so auf die nächste Stufe.\n\nDas Beste: Jeder Einkauf zählt egal bei welchem MUTUALZ-Partner du Einkaufst!\n\nWichtig: Am Ende des Monats wirst du wieder auf diese Stufe und 0 Punkte zurückgesetzt!*';
	@override String get member => 'In dieser Stufe erhältst du auf jede Rechnung 5 % Rabatt bei allen Partnern.\n\nJe schneller du diese Stufe erreichst desto mehr Zeit hast die nächsten Stufen zu erklimmen!\n\nGeheimtipp: Mit dem Mutualz-Abo startest in dieser Stufe zum Beginn jeden Monats! Zeitvorteil heißt Preisvorteil!';
	@override String get silver => 'Diese Stufe belohnt die Zuverlässigen und Treuen!\n\nMit Silber sparst du 10 % auf die Gesamtrechnung in Restaurants in denen du Silber erreicht hast.\n\nDiese Stufe kannst du nur erreichen, indem du genügend Punkte in einem Restaurant sammelst und Stufen aufsteigst.*';
	@override String get gold => 'Herzlichen Glückwunsch eure Hoheit!\n\nWenn man sagt: "Der Kunde ist König!" Dann spricht man ausschließlich von dir!\n\nDu sparst 15% auf die Gesamtrechnung in dem Restaurant in dem du diese Stufe erreicht hast.';
	@override String get subscription => 'Schließe jetzt das MUTUALZ-Abo ab und erhalte sofort 5 % Raåbatt zusätzlich zu deinen Coupons und starte jeden Monat mit einem Zeitvorteil !!!';
	@override late final _StringsTutorialLoyaltyButtonsDe buttons = _StringsTutorialLoyaltyButtonsDe._(_root);
	@override String get terms => '*für mehr Informationen schau in unsere AGB\'s';
	@override String get level => 'Stufe';
	@override String get oneCalendarMonth => 'Ein Kalendermonat Zeit';
}

// Path: screens.auth.tabs
class _StringsScreensAuthTabsDe implements _StringsScreensAuthTabsEn {
	_StringsScreensAuthTabsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get login => 'Einloggen';
	@override String get registration => 'Registrierung';
}

// Path: screens.auth.login
class _StringsScreensAuthLoginDe implements _StringsScreensAuthLoginEn {
	_StringsScreensAuthLoginDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get email => 'E-Mail';
	@override String get password => 'Passwort';
	@override String get buttonLabel => 'Einloggen';
	@override String get forgotPassword => 'Passwort vergessen?';
}

// Path: screens.auth.registration
class _StringsScreensAuthRegistrationDe implements _StringsScreensAuthRegistrationEn {
	_StringsScreensAuthRegistrationDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override late final _StringsScreensAuthRegistrationLabelsDe labels = _StringsScreensAuthRegistrationLabelsDe._(_root);
	@override late final _StringsScreensAuthRegistrationTermsDe terms = _StringsScreensAuthRegistrationTermsDe._(_root);
	@override String get buttonLabel => 'Registrieren';
}

// Path: screens.auth.opt
class _StringsScreensAuthOptDe implements _StringsScreensAuthOptEn {
	_StringsScreensAuthOptDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'OTP-Code eingeben';
	@override String get description => 'Wir haben den OTP-Code an deine E-Mail geschickt.';
	@override String get buttonLabel => 'OK';
	@override String get resendLabel => 'Zugangscode erneut senden';
	@override String get resendCodeAvailable => 'Code zum erneuten Senden verfügbar';
}

// Path: screens.onboarding.personalInfo
class _StringsScreensOnboardingPersonalInfoDe implements _StringsScreensOnboardingPersonalInfoEn {
	_StringsScreensOnboardingPersonalInfoDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Freiwillige Angaben,';
	@override String get description => 'teile ein paar grundlegende Details mit uns';
}

// Path: screens.onboarding.lifestyle
class _StringsScreensOnboardingLifestyleDe implements _StringsScreensOnboardingLifestyleEn {
	_StringsScreensOnboardingLifestyleDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Erzähle uns von dir,';
	@override String get description => 'damit wir dir genau die Angebote bieten können, die zu deinem Leben passen';
	@override late final _StringsScreensOnboardingLifestyleJobDe job = _StringsScreensOnboardingLifestyleJobDe._(_root);
	@override late final _StringsScreensOnboardingLifestyleAllergiesDe allergies = _StringsScreensOnboardingLifestyleAllergiesDe._(_root);
	@override late final _StringsScreensOnboardingLifestyleDietDe diet = _StringsScreensOnboardingLifestyleDietDe._(_root);
}

// Path: screens.onboarding.taste
class _StringsScreensOnboardingTasteDe implements _StringsScreensOnboardingTasteEn {
	_StringsScreensOnboardingTasteDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Zeig uns, was dir gefällt';
	@override String get description => 'Wähle deine Präferenzen aus';
}

// Path: screens.onboarding.drinks
class _StringsScreensOnboardingDrinksDe implements _StringsScreensOnboardingDrinksEn {
	_StringsScreensOnboardingDrinksDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Getränke';
	@override String get tea => 'Tee';
	@override String get softDrinks => 'Softdrinks';
	@override String get coffee => 'Kaffee';
	@override String get beer => 'Bier';
	@override String get cocktails => 'Cocktails';
	@override String get juice => 'Saft';
	@override String get smoothie => 'Smoothies';
	@override String get wine => 'Wein';
	@override String get iceTea => 'Eistee';
}

// Path: screens.onboarding.food
class _StringsScreensOnboardingFoodDe implements _StringsScreensOnboardingFoodEn {
	_StringsScreensOnboardingFoodDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Essen';
	@override String get kebab => 'Döner';
	@override String get sweets => 'Dessert';
	@override String get pizza => 'Pizza';
	@override String get burger => 'Burger';
	@override String get sushi => 'Sushi';
	@override String get pasta => 'Nudeln';
	@override String get fish => 'Fisch';
	@override String get salad => 'Salat';
	@override String get fingerFood => 'Fingerfood';
}

// Path: screens.onboarding.events
class _StringsScreensOnboardingEventsDe implements _StringsScreensOnboardingEventsEn {
	_StringsScreensOnboardingEventsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Events';
	@override String get party => 'Party';
	@override String get concert => 'Konzert';
	@override String get markets => 'Märkte';
	@override String get tastings => 'Verkostungen';
	@override String get workshops => 'Workshops';
	@override String get sports => 'Sport';
	@override String get theater => 'Theater';
	@override String get readings => 'Lesungen';
	@override String get cinema => 'Kino';
}

// Path: screens.notificationPermission.buttons
class _StringsScreensNotificationPermissionButtonsDe implements _StringsScreensNotificationPermissionButtonsEn {
	_StringsScreensNotificationPermissionButtonsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get decideLater => 'Später entscheiden';
	@override String get next => 'Weiter';
}

// Path: screens.discover.info
class _StringsScreensDiscoverInfoDe implements _StringsScreensDiscoverInfoEn {
	_StringsScreensDiscoverInfoDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get timeLabel => 'Öffnungszeiten';
}

// Path: screens.restaurant.booking
class _StringsScreensRestaurantBookingDe implements _StringsScreensRestaurantBookingEn {
	_StringsScreensRestaurantBookingDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String title({required Object name}) => 'Hi${name}, es wäre toll, dir einen Tisch anbieten zu können.';
	@override String get calendarButtonLabel => 'Weiter';
	@override String get timeButtonLabel => 'Buchen';
	@override String get resultButtonLabel => 'Alles klar';
	@override String get errorButtonLabel => 'Ok';
	@override String get availableTimeLabel => 'Verfügbare Zeiten';
	@override String get estimatedTimeLabel => 'Voraussichtliche Dauer deines Besuchs';
	@override String get numberOfDinnersLabel => 'Anzahl der Gäste';
	@override String get resultTitle => 'Du hast einen Tisch angefragt';
	@override String get resultDescription => 'Wir werden dir eine Bestätigung per Mail und Benachrichtigung schicken.';
}

// Path: screens.restaurant.info
class _StringsScreensRestaurantInfoDe implements _StringsScreensRestaurantInfoEn {
	_StringsScreensRestaurantInfoDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get scheduleTitle => 'Öffnungszeiten';
}

// Path: screens.restaurant.menu
class _StringsScreensRestaurantMenuDe implements _StringsScreensRestaurantMenuEn {
	_StringsScreensRestaurantMenuDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get empty => 'Keine Speisekarte verfügbar';
	@override String get ratingFilter => '4+';
	@override String get budgetFilter => 'Budget';
	@override String get budgetFilterHint => '9,99';
	@override String get extras => 'Extras';
	@override String get sauces => 'Saucen';
}

// Path: screens.restaurant.deals
class _StringsScreensRestaurantDealsDe implements _StringsScreensRestaurantDealsEn {
	_StringsScreensRestaurantDealsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Sonderangebote';
	@override String get empty => 'Keine Sonderangebote verfügbar';
}

// Path: screens.profile.tabs
class _StringsScreensProfileTabsDe implements _StringsScreensProfileTabsEn {
	_StringsScreensProfileTabsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get post => 'Beiträge';
	@override String get settings => 'Einstellungen';
}

// Path: screens.profile.settings
class _StringsScreensProfileSettingsDe implements _StringsScreensProfileSettingsEn {
	_StringsScreensProfileSettingsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get general => 'Allgemeine Einstellungen';
	@override String get help => 'Hilfe';
	@override String get feedback => 'Feedback';
	@override String get orders => 'Bestellungen';
	@override String get logout => 'Abmelden';
}

// Path: screens.subscription.levels
class _StringsScreensSubscriptionLevelsDe implements _StringsScreensSubscriptionLevelsEn {
	_StringsScreensSubscriptionLevelsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get test => 'Test';
	@override String get member => 'Member';
	@override String get silver => 'Silber';
	@override String get gold => 'Gold';
}

// Path: screens.subscription.levelsDescription
class _StringsScreensSubscriptionLevelsDescriptionDe implements _StringsScreensSubscriptionLevelsDescriptionEn {
	_StringsScreensSubscriptionLevelsDescriptionDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get test => 'Probiere Mutualz aus und nutze alle unsere Gutscheine kostenlos.';
	@override String get member => 'Du zahlst 5% weniger bei jeder Rechnung.';
	@override String get silver => 'Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Rabatt von 10% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde).';
	@override String get gold => 'Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Rabatt von 15% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde).';
}

// Path: screens.subscription.buttons
class _StringsScreensSubscriptionButtonsDe implements _StringsScreensSubscriptionButtonsEn {
	_StringsScreensSubscriptionButtonsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get upgrade => 'Jetzt upgraden für 9,99€ monatlich';
	@override String get recommend => 'Empfehle uns einem Freund, um ein kostenloses Upgragde für 1 Monat zu erhalten';
	@override String get cancel => 'Abbrechen';
	@override String get reactivate => 'Reaktivieren';
}

// Path: screens.subscription.discount
class _StringsScreensSubscriptionDiscountDe implements _StringsScreensSubscriptionDiscountEn {
	_StringsScreensSubscriptionDiscountDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String withPercent({required num n, required Object percent}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('de'))(n,
		one: '${percent}% für 1 Tag',
		other: '${percent}% für ${n} Tage',
	);
}

// Path: screens.cart.dialogNotes
class _StringsScreensCartDialogNotesDe implements _StringsScreensCartDialogNotesEn {
	_StringsScreensCartDialogNotesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Hast du besondere Wünsche?';
	@override String get hint => 'Notiz';
}

// Path: screens.cart.dialogRemove
class _StringsScreensCartDialogRemoveDe implements _StringsScreensCartDialogRemoveEn {
	_StringsScreensCartDialogRemoveDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Gericht aus dem Warenkorb entfernen';
	@override String get description => 'Bist du sicher, dass du dieses Gericht aus dem Warenkorb entfernen möchten?';
	@override String get buttonCancel => 'Abbrechen';
	@override String get buttonConfirm => 'Ich bin sicher';
}

// Path: screens.cart.dialogSpecialRemove
class _StringsScreensCartDialogSpecialRemoveDe implements _StringsScreensCartDialogSpecialRemoveEn {
	_StringsScreensCartDialogSpecialRemoveDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Angebot aus dem Warenkorb entfernen';
	@override String get description => 'Bist du sicher, dass du dieses Angebot aus dem Warenkorb entfernen möchtest?';
	@override String get buttonCancel => 'Abbrechen';
	@override String get buttonConfirm => 'Ich bin sicher';
}

// Path: screens.cart.dialogConfirm
class _StringsScreensCartDialogConfirmDe implements _StringsScreensCartDialogConfirmEn {
	_StringsScreensCartDialogConfirmDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Bist du sicher, dass du alles gefunden hast?';
	@override String get description => 'Dann hier bestellen';
	@override String get buttonConfirm => 'Ja, bin ich';
}

// Path: screens.cart.dialogShare
class _StringsScreensCartDialogShareDe implements _StringsScreensCartDialogShareEn {
	_StringsScreensCartDialogShareDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get description => 'Empfehle uns einem Freund, um eine 1 monatige Mitgliedschaft kostenlos zu erhalten und noch mehr zu sparen';
	@override String get buttonShare => 'Teilen';
	@override String get buttonCancel => 'Später teilen';
}

// Path: screens.review.ratingItems
class _StringsScreensReviewRatingItemsDe implements _StringsScreensReviewRatingItemsEn {
	_StringsScreensReviewRatingItemsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get service => 'Service';
	@override String get cleanliness => 'Sauberkeit';
	@override String get waitingTime => 'Wartezeit';
	@override String get foodQuality => 'Geschmack';
	@override String get athmosphere => 'Atmosphäre';
}

// Path: screens.review.dietPreferences
class _StringsScreensReviewDietPreferencesDe implements _StringsScreensReviewDietPreferencesEn {
	_StringsScreensReviewDietPreferencesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get yes => 'Ja';
	@override String get no => 'Nein';
}

// Path: tutorial.loyalty.buttons
class _StringsTutorialLoyaltyButtonsDe implements _StringsTutorialLoyaltyButtonsEn {
	_StringsTutorialLoyaltyButtonsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get later => 'Lieber später ...';
	@override String get subscribe => 'Abo abschließen';
	@override String get next => 'Weiter';
}

// Path: screens.auth.registration.labels
class _StringsScreensAuthRegistrationLabelsDe implements _StringsScreensAuthRegistrationLabelsEn {
	_StringsScreensAuthRegistrationLabelsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get gender => 'Geschlecht';
	@override String get firstName => 'Vorname';
	@override String get lastName => 'Nachname';
	@override String get username => 'Benutzername*';
	@override String get birthDate => 'Geburtsdatum*';
	@override String get email => 'E-Mail*';
	@override String get phone => 'Rufnummer';
	@override String get password => 'Passwort*';
	@override String get confirmPassword => 'Passwort bestätigen*';
}

// Path: screens.auth.registration.terms
class _StringsScreensAuthRegistrationTermsDe implements _StringsScreensAuthRegistrationTermsEn {
	_StringsScreensAuthRegistrationTermsDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get agreement => 'Ich stimme zu';
	@override String get conditions => 'Bedingungen';
	@override String get separator => ' und ';
	@override String get privacy => 'Datenschutzbestimmungen';
}

// Path: screens.onboarding.lifestyle.job
class _StringsScreensOnboardingLifestyleJobDe implements _StringsScreensOnboardingLifestyleJobEn {
	_StringsScreensOnboardingLifestyleJobDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Beruf';
	@override String get employee => 'Angestellter';
	@override String get student => 'Schüler';
	@override String get collegeStudent => 'Student';
	@override String get selfEmployed => 'Selbstständig';
	@override String get housewife => 'Hausmann/Hausfrau';
	@override String get retired => 'Rentner';
}

// Path: screens.onboarding.lifestyle.allergies
class _StringsScreensOnboardingLifestyleAllergiesDe implements _StringsScreensOnboardingLifestyleAllergiesEn {
	_StringsScreensOnboardingLifestyleAllergiesDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Unverträglichkeiten';
	@override String get gluten => 'Gluten';
	@override String get lactose => 'Laktose';
	@override String get nutAllergy => 'Nüsse';
}

// Path: screens.onboarding.lifestyle.diet
class _StringsScreensOnboardingLifestyleDietDe implements _StringsScreensOnboardingLifestyleDietEn {
	_StringsScreensOnboardingLifestyleDietDe._(this._root);

	@override final _StringsDe _root; // ignore: unused_field

	// Translations
	@override String get title => 'Ernährungsform';
	@override String get halal => 'Halal';
	@override String get kosher => 'Koscher';
	@override String get vegan => 'Vegan';
	@override String get vegetarian => 'Vegetarisch';
}

/// Flat map(s) containing all translations.
/// Only for edge cases! For simple maps, use the map function of this library.

extension on Translations {
	dynamic _flatMapFunction(String path) {
		switch (path) {
			case 'language': return 'English';
			case 'title': return 'Mutualz';
			case 'titleDev': return 'Mutualz Dev';
			case 'errors.fieldEmpty': return 'This field shouldn\'t be empty';
			case 'errors.emailInvalid': return 'Invalid email';
			case 'errors.passwordInvalid': return 'Password should be at least 8 long and less than 60 characters';
			case 'errors.userNameInvalid': return 'Username should be at least 2 long and less than 60 characters';
			case 'errors.passwordNotMatch': return 'Passwords don\'t match';
			case 'errors.otpEmpty': return 'OTP cannot be empty';
			case 'errors.unknown': return 'Unknown error';
			case 'errors.errorAuthConflict': return 'Authentication conflict';
			case 'errors.errorAuthTokenExpired': return 'Authentication token expired';
			case 'errors.errorSocialAuthFailed': return 'Social authentication failed';
			case 'errors.errorAuthOperationInProgress': return 'Authentication operation in progress';
			case 'errors.errorAuthCancelled': return 'Authentication cancelled';
			case 'errors.privacyNotAccepted': return 'Privacy policy and Terms & Conditions not accepted';
			case 'errors.baseTextLength': return 'Length should be at between 2 and 60 characters';
			case 'errors.phoneNumberInvalid': return 'Invalid phone number';
			case 'errors.doesNotContainNumber': return 'Password should contain at least one number';
			case 'errors.doesNotContainSpecialCharacter': return 'Password should contain at least one special character';
			case 'errors.doesNotContainUppercase': return 'Password should contain at least one uppercase letter';
			case 'errors.doesNotContainLowercase': return 'Password should contain at least one lowercase letter';
			case 'errors.disabledLocationService': return 'Location services are disabled, please enable it to continue';
			case 'errors.locationPermissionDenied': return 'Location permissions are denied, please enable it to continue';
			case 'errors.locationPermissionPermanentlyDenied': return 'Location permissions are permanently denied, we cannot request permissions';
			case 'errors.noRestaurantImage': return 'No Restaurant Image';
			case 'errors.emptyRating': return 'All ratings should be filled';
			case 'errors.defaultPaymentMethod': return 'You must have at least one payment method';
			case 'errors.restartError': return 'There is problem with the payment method. Please restart your app. it might help you.';
			case 'buttons.save': return 'Save';
			case 'buttons.send': return 'Send';
			case 'buttons.ok': return 'Ok';
			case 'buttons.add': return 'Add';
			case 'buttons.edit': return 'Edit';
			case 'buttons.order': return 'Order';
			case 'buttons.pay': return 'Pay';
			case 'buttons.orderAndPay': return 'Order & Pay';
			case 'buttons.seeAll': return 'See all';
			case 'buttons.back': return 'Go Back';
			case 'buttons.savePoints': return 'Gain even more points';
			case 'buttons.tryAgain': return 'Try again';
			case 'buttons.tryAgainLater': return 'Try again later';
			case 'buttons.skip': return 'Skip';
			case 'dialogs.error.title': return 'Ups!!!';
			case 'dialogs.success.title': return 'Excellent!';
			case 'dialogs.success.changes': return 'Your changes have been saved';
			case 'dialogs.buttonLabel': return 'Ok';
			case 'dialogs.options.deletePost': return 'Delete Post';
			case 'dialogs.options.report': return 'Report';
			case 'dialogs.options.unfollow': return 'Unfollow';
			case 'dialogs.options.block': return 'Block';
			case 'dialogs.options.follow': return 'Follow';
			case 'dialogs.numberTable.title': return 'Put in your table number.';
			case 'dialogs.numberTable.hint': return 'Tablenumber';
			case 'screens.splash.title': return 'Mutualz';
			case 'screens.auth.welcome': return 'Welcome\nto Mutualz';
			case 'screens.auth.tabs.login': return 'Login';
			case 'screens.auth.tabs.registration': return 'Registration';
			case 'screens.auth.login.email': return 'Mail';
			case 'screens.auth.login.password': return 'Password';
			case 'screens.auth.login.buttonLabel': return 'Login';
			case 'screens.auth.login.forgotPassword': return 'Forgot Password?';
			case 'screens.auth.registration.labels.gender': return 'Gender';
			case 'screens.auth.registration.labels.firstName': return 'First name';
			case 'screens.auth.registration.labels.lastName': return 'Last name';
			case 'screens.auth.registration.labels.username': return 'Username*';
			case 'screens.auth.registration.labels.birthDate': return 'Birth date*';
			case 'screens.auth.registration.labels.email': return 'Mail*';
			case 'screens.auth.registration.labels.phone': return 'Phone number';
			case 'screens.auth.registration.labels.password': return 'Password*';
			case 'screens.auth.registration.labels.confirmPassword': return 'Repeat password*';
			case 'screens.auth.registration.terms.agreement': return 'I agree to ';
			case 'screens.auth.registration.terms.conditions': return 'Terms & Conditions';
			case 'screens.auth.registration.terms.separator': return ' and ';
			case 'screens.auth.registration.terms.privacy': return 'Privacy Policy';
			case 'screens.auth.registration.buttonLabel': return 'Sign up';
			case 'screens.auth.opt.title': return 'Insert OTP code';
			case 'screens.auth.opt.description': return 'We have sent the OTP code to your email.';
			case 'screens.auth.opt.buttonLabel': return 'Ok';
			case 'screens.auth.opt.resendLabel': return 'Resend access code';
			case 'screens.auth.opt.resendCodeAvailable': return 'Resend code available';
			case 'screens.forgot.title': return 'Forgot Password';
			case 'screens.forgot.description': return 'Please enter your email with which you\'re registered. A link for changing your password will be send immediately.';
			case 'screens.forgot.emailLabel': return 'Mail';
			case 'screens.forgot.buttonLabel': return 'Send';
			case 'screens.forgot.backButtonLabel': return 'Go Back';
			case 'screens.forgot.successDescription': return 'You can check your email to enter the password recovery link';
			case 'screens.reset.title': return 'Change your password';
			case 'screens.reset.description': return 'Add your new password here';
			case 'screens.reset.passwordLabel': return 'Password*';
			case 'screens.reset.repeatPasswordLabel': return 'Repeat password*';
			case 'screens.reset.buttonLabel': return 'Change Password';
			case 'screens.reset.successMessage': return 'Congratulations, your password has been changed';
			case 'screens.onboarding.personalInfo.title': return 'Voluntary information,';
			case 'screens.onboarding.personalInfo.description': return 'share some basic details with us';
			case 'screens.onboarding.lifestyle.title': return 'Tell us about you,';
			case 'screens.onboarding.lifestyle.description': return 'so we can offer you deals that fit your life';
			case 'screens.onboarding.lifestyle.job.title': return 'Occupation/Job';
			case 'screens.onboarding.lifestyle.job.employee': return 'Employee';
			case 'screens.onboarding.lifestyle.job.student': return 'Student';
			case 'screens.onboarding.lifestyle.job.collegeStudent': return 'College Student';
			case 'screens.onboarding.lifestyle.job.selfEmployed': return 'Self-employed';
			case 'screens.onboarding.lifestyle.job.housewife': return 'Househusband/housewife';
			case 'screens.onboarding.lifestyle.job.retired': return 'Retired';
			case 'screens.onboarding.lifestyle.allergies.title': return 'Intolerances/allergies';
			case 'screens.onboarding.lifestyle.allergies.gluten': return 'Gluten Free';
			case 'screens.onboarding.lifestyle.allergies.lactose': return 'Lactose Free';
			case 'screens.onboarding.lifestyle.allergies.nutAllergy': return 'Nut allergy';
			case 'screens.onboarding.lifestyle.diet.title': return 'Dietary preferences';
			case 'screens.onboarding.lifestyle.diet.halal': return '100% Halal';
			case 'screens.onboarding.lifestyle.diet.kosher': return 'Kosher';
			case 'screens.onboarding.lifestyle.diet.vegan': return 'Vegan';
			case 'screens.onboarding.lifestyle.diet.vegetarian': return 'Vegetarian';
			case 'screens.onboarding.taste.title': return 'Show us what you like';
			case 'screens.onboarding.taste.description': return 'Select your preferences';
			case 'screens.onboarding.drinks.title': return 'Drinks';
			case 'screens.onboarding.drinks.tea': return 'Tea';
			case 'screens.onboarding.drinks.softDrinks': return 'Soft drinks';
			case 'screens.onboarding.drinks.coffee': return 'Coffee';
			case 'screens.onboarding.drinks.beer': return 'Beer';
			case 'screens.onboarding.drinks.cocktails': return 'Cocktails';
			case 'screens.onboarding.drinks.juice': return 'Juice';
			case 'screens.onboarding.drinks.smoothie': return 'Smoothie';
			case 'screens.onboarding.drinks.wine': return 'Wine';
			case 'screens.onboarding.drinks.iceTea': return 'Ice Tea';
			case 'screens.onboarding.food.title': return 'Food';
			case 'screens.onboarding.food.kebab': return 'Kebab';
			case 'screens.onboarding.food.sweets': return 'Sweets';
			case 'screens.onboarding.food.pizza': return 'Pizza';
			case 'screens.onboarding.food.burger': return 'Burger';
			case 'screens.onboarding.food.sushi': return 'Sushi';
			case 'screens.onboarding.food.pasta': return 'Pasta';
			case 'screens.onboarding.food.fish': return 'Fish';
			case 'screens.onboarding.food.salad': return 'Salad';
			case 'screens.onboarding.food.fingerFood': return 'Finger Food';
			case 'screens.onboarding.events.title': return 'Events';
			case 'screens.onboarding.events.party': return 'Party';
			case 'screens.onboarding.events.concert': return 'Concert';
			case 'screens.onboarding.events.markets': return 'Markets';
			case 'screens.onboarding.events.tastings': return 'Tastings';
			case 'screens.onboarding.events.workshops': return 'Workshops';
			case 'screens.onboarding.events.sports': return 'Sports';
			case 'screens.onboarding.events.theater': return 'Theater';
			case 'screens.onboarding.events.readings': return 'Readings';
			case 'screens.onboarding.events.cinema': return 'Cinema';
			case 'screens.onboarding.nextButtonLabel': return 'Next';
			case 'screens.onboarding.backButtonLabel': return 'Go back';
			case 'screens.socialOnboarding.title': return 'Let\'s get acquainted';
			case 'screens.socialOnboarding.description': return 'Share information about yourself with us';
			case 'screens.socialOnboarding.nextButtonLabel': return 'Next';
			case 'screens.socialOnboarding.skipButtonLabel': return 'Skip';
			case 'screens.notificationPermission.title': return 'NOTIFICATIONS';
			case 'screens.notificationPermission.description': return 'Turn on your notifications so we can inform you when your food is ready.';
			case 'screens.notificationPermission.buttons.decideLater': return 'Decide later';
			case 'screens.notificationPermission.buttons.next': return 'Continue';
			case 'screens.search.noResults': return 'Sorry, no results were found';
			case 'screens.search.inputLabel': return 'Search...';
			case 'screens.search.sortByRating': return 'Sort by rating';
			case 'screens.search.emptyCollection': return 'We need to get to\nknow you better';
			case 'screens.discover.info.timeLabel': return 'Opening Hours';
			case 'screens.restaurant.bookTableButton': return 'Book a table';
			case 'screens.restaurant.orderPayButton': return 'Order here';
			case 'screens.restaurant.booking.title': return ({required Object name}) => 'Hi${name}, it would be great to offer you a table.';
			case 'screens.restaurant.booking.calendarButtonLabel': return 'Next';
			case 'screens.restaurant.booking.timeButtonLabel': return 'Book';
			case 'screens.restaurant.booking.resultButtonLabel': return 'Finish';
			case 'screens.restaurant.booking.errorButtonLabel': return 'Ok';
			case 'screens.restaurant.booking.availableTimeLabel': return 'Available times';
			case 'screens.restaurant.booking.estimatedTimeLabel': return 'Estimated time of your visit';
			case 'screens.restaurant.booking.numberOfDinnersLabel': return 'Number of diners';
			case 'screens.restaurant.booking.resultTitle': return 'You reserved a\ntable at the';
			case 'screens.restaurant.booking.resultDescription': return 'We\'re gonna send you a confirmation via Mail and Notification.';
			case 'screens.restaurant.info.scheduleTitle': return 'Opening Hours';
			case 'screens.restaurant.menu.empty': return 'No menu available';
			case 'screens.restaurant.menu.ratingFilter': return '4+';
			case 'screens.restaurant.menu.budgetFilter': return 'Budget';
			case 'screens.restaurant.menu.budgetFilterHint': return '15.99';
			case 'screens.restaurant.menu.extras': return 'Extras';
			case 'screens.restaurant.menu.sauces': return 'Sauces';
			case 'screens.restaurant.emptyDietPreferences': return 'No diet preferences';
			case 'screens.restaurant.deals.title': return 'Specials';
			case 'screens.restaurant.deals.empty': return 'No specials available';
			case 'screens.profile.hint': return 'This profile has been deleted.';
			case 'screens.profile.tabs.post': return 'Posts';
			case 'screens.profile.tabs.settings': return 'Settings';
			case 'screens.profile.followers': return 'Followers';
			case 'screens.profile.following': return 'Following';
			case 'screens.profile.subscriptions': return 'Subscriptions';
			case 'screens.profile.settings.general': return 'General Settings';
			case 'screens.profile.settings.help': return 'Help';
			case 'screens.profile.settings.feedback': return 'Feedback';
			case 'screens.profile.settings.orders': return 'Previous Orders';
			case 'screens.profile.settings.logout': return 'Log out';
			case 'screens.generalSettings.profile': return 'User Profile';
			case 'screens.generalSettings.password': return 'Change Password';
			case 'screens.generalSettings.preferences': return 'Preferences';
			case 'screens.generalSettings.payment': return 'Payment Method';
			case 'screens.changePasswordSettings.title': return 'Change Password';
			case 'screens.changePasswordSettings.newPassword': return 'New Password';
			case 'screens.changePasswordSettings.confirmNewPassword': return 'Confirm New Password';
			case 'screens.changePasswordSettings.buttonLabel': return 'Change Password';
			case 'screens.changePreferences.buttonLabel': return 'Save';
			case 'screens.userProfileSettings.deleteAccount': return 'Delete account';
			case 'screens.userProfileSettings.deleteAccountTitle': return 'Are you sure you want to delete your account?';
			case 'screens.userProfileSettings.deleteAccountDescription': return 'You will loose access to all of Mutualz exclusive  discounts and your profile and posts will be deleted  permanently.';
			case 'screens.userProfileSettings.buttonCancel': return 'I changed my mind';
			case 'screens.userProfileSettings.buttonOk': return 'I\'m sure';
			case 'screens.feedback.title': return 'Feedback';
			case 'screens.feedback.messageLabel': return 'Message';
			case 'screens.feedback.design': return 'Design';
			case 'screens.feedback.userFriendliness': return 'User friendliness';
			case 'screens.feedback.paymentFunction': return 'Payment function';
			case 'screens.feedback.orderFunction': return 'Order function';
			case 'screens.feedback.community': return 'Community';
			case 'screens.feedback.couponsDiscounts': return 'Coupons & Discounts';
			case 'screens.feedback.recommendations': return 'Recommendations';
			case 'screens.feedback.pushNotifications': return 'Push Notifications';
			case 'screens.feedback.speed': return 'Speed';
			case 'screens.feedback.otherFeatures': return 'Other features';
			case 'screens.feedback.successMessage': return 'Thank you for your feedback! Our team strives to deliver the best for you.';
			case 'screens.faq.title': return 'FAQ\'s';
			case 'screens.faq.contactUs': return 'Contact us';
			case 'screens.faq.empty': return 'No questions/answers found';
			case 'screens.contact.title': return 'Contact Form';
			case 'screens.contact.messageLabel': return 'Write your message';
			case 'screens.contact.registration': return 'Registration process';
			case 'screens.contact.payment': return 'Payment function';
			case 'screens.contact.subscription': return 'Subscription';
			case 'screens.contact.report': return 'Report error';
			case 'screens.contact.other': return 'Other categories';
			case 'screens.contact.successMessage': return 'Thank you for your message! We will get back to you as soon as possible.';
			case 'screens.subscription.title': return 'Subscriptions';
			case 'screens.subscription.description': return 'You`ll advance the subscription tiers by\ngathering points on every restaurant visit';
			case 'screens.subscription.levels.test': return 'Just Test';
			case 'screens.subscription.levels.member': return 'Member';
			case 'screens.subscription.levels.silver': return 'Silver';
			case 'screens.subscription.levels.gold': return 'Gold';
			case 'screens.subscription.levelsDescription.test': return 'Try MUTUALZ and use all our coupons for free';
			case 'screens.subscription.levelsDescription.member': return 'You pay 5% less on your whole bill.';
			case 'screens.subscription.levelsDescription.silver': return 'You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 10% on your whole bill (only in the restaurants in which the subscription was reached)';
			case 'screens.subscription.levelsDescription.gold': return 'You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 15% on your whole bill (only in the restaurants in which the subscription was reached)';
			case 'screens.subscription.buttons.upgrade': return 'Upgrade now for 9,99€ monthly';
			case 'screens.subscription.buttons.recommend': return 'Recommend us to a friend to get 1 month for free';
			case 'screens.subscription.buttons.cancel': return 'Unsubscribe and loose your benefits';
			case 'screens.subscription.buttons.reactivate': return 'Reactivate your subscription';
			case 'screens.subscription.info': return ({required Object date}) => 'Your discount is still active till ${date}';
			case 'screens.subscription.discount.withPercent': return ({required num n, required Object percent}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('en'))(n,
				one: '${percent}% for 1 day',
				other: '${percent}% for ${n} days',
			);
			case 'screens.subscription.daysRemaining': return ({required num n}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('en'))(n,
				one: '1 day left',
				other: '${n} days left',
			);
			case 'screens.subscription.discountOnTotalBill': return 'Discount on\nthe total bill*';
			case 'screens.comments.title': return 'Comments';
			case 'screens.comments.inputLabel': return 'Add a comment';
			case 'screens.comments.empty': return 'No comments available';
			case 'screens.orderProcess.title': return 'Menu';
			case 'screens.orderProcess.empty': return 'No menu available';
			case 'screens.orderProcess.orderButton': return 'Order/Pay';
			case 'screens.orderProcess.orderSuccess': return 'Order created successfully. Please enjoy your meal!';
			case 'screens.orderProcess.paymentSuccess': return 'Payment successfully completed';
			case 'screens.cart.empty': return 'No elements in the cart';
			case 'screens.cart.total': return 'Total:';
			case 'screens.cart.dialogNotes.title': return 'Do you have any special requirements?';
			case 'screens.cart.dialogNotes.hint': return 'Special request';
			case 'screens.cart.dialogRemove.title': return 'Remove dish from the cart';
			case 'screens.cart.dialogRemove.description': return 'Are you sure you want to remove this dish from the cart?';
			case 'screens.cart.dialogRemove.buttonCancel': return 'Cancel';
			case 'screens.cart.dialogRemove.buttonConfirm': return 'I\'m sure';
			case 'screens.cart.dialogSpecialRemove.title': return 'Remove special from the cart';
			case 'screens.cart.dialogSpecialRemove.description': return 'Are you sure you want to remove this special from the cart?';
			case 'screens.cart.dialogSpecialRemove.buttonCancel': return 'Cancel';
			case 'screens.cart.dialogSpecialRemove.buttonConfirm': return 'I\'m sure';
			case 'screens.cart.dialogConfirm.title': return 'Are you sure you\'ve found everything?';
			case 'screens.cart.dialogConfirm.description': return 'Then order here';
			case 'screens.cart.dialogConfirm.buttonConfirm': return 'Confirm Selection';
			case 'screens.cart.dialogShare.description': return 'Recommend us to your friends to get a 1 month membership for free and save more money on your bill';
			case 'screens.cart.dialogShare.buttonShare': return 'To Share';
			case 'screens.cart.dialogShare.buttonCancel': return 'Share later';
			case 'screens.cart.createOrderSuccess': return 'Order created successfully. Please enjoy your meal!';
			case 'screens.invoice.title': return 'Thanks\nfor joining us';
			case 'screens.invoice.dishes': return 'Dishes';
			case 'screens.invoice.coupons': return 'Coupons:';
			case 'screens.invoice.couponSelection': return 'Coupon Selection';
			case 'screens.invoice.doNotUseCoupon': return 'Do not use a coupon';
			case 'screens.invoice.proceedToPayment': return 'Proceed to payment';
			case 'screens.paymentMethodSettings.title': return 'Payment Methods';
			case 'screens.paymentMethodSettings.card': return 'Card';
			case 'screens.paymentMethodSettings.applePay': return 'Apple Pay';
			case 'screens.paymentMethodSettings.googlePay': return 'Google Pay';
			case 'screens.addPost.title': return 'Share your experience with everyone';
			case 'screens.addPost.description': return 'Create a post of your best moments';
			case 'screens.addPost.uploadImage': return 'Upload Image';
			case 'screens.addPost.restaurantHint': return 'Select a restaurant';
			case 'screens.addPost.descriptionHint': return 'Add a description here';
			case 'screens.addPost.attachImage': return 'Attach Image';
			case 'screens.addPost.tagYourFriends': return 'Tag your friends';
			case 'screens.payment.message': return 'Thank you for your visit!';
			case 'screens.payment.buttonLabel': return 'Collect your points';
			case 'screens.payment.success': return 'Payment successfully completed';
			case 'screens.orders.title': return 'Previous Orders';
			case 'screens.orders.empty': return 'No previous orders';
			case 'screens.orders.total': return 'Total';
			case 'screens.orders.memberDiscount': return 'Member discount';
			case 'screens.orders.couponDiscount': return 'Coupon discount';
			case 'screens.orders.totalWithMutualz': return 'Total with Mutualz';
			case 'screens.orders.summary': return 'Order summary';
			case 'screens.orders.sendInvoice': return 'Send Invoice';
			case 'screens.orders.invoiceSent': return 'Invoice has been successfully sent';
			case 'screens.orders.sendingInvoice': return 'Sending invoice...';
			case 'screens.progressReward.unlockCoupons': return 'Spend them to unlock\nCoupons';
			case 'screens.review.rating': return 'Rating';
			case 'screens.review.ratingItems.service': return 'Service';
			case 'screens.review.ratingItems.cleanliness': return 'Cleanliness';
			case 'screens.review.ratingItems.waitingTime': return 'Waiting time';
			case 'screens.review.ratingItems.foodQuality': return 'Food quality';
			case 'screens.review.ratingItems.athmosphere': return 'Athmosphere';
			case 'screens.review.commentHint': return 'Write your comment here';
			case 'screens.review.verifyDietary': return 'Verify the dietary claims';
			case 'screens.review.dietPreferences.yes': return 'Yes';
			case 'screens.review.dietPreferences.no': return 'No';
			case 'screens.review.uploadImage': return 'Upload Image';
			case 'screens.review.descriptionHint': return 'Add a description here';
			case 'screens.review.rateTitle': return 'Rate now to get extra Points';
			case 'screens.review.points': return 'Points';
			case 'screens.review.rate': return 'Rate';
			case 'screens.review.rateDescription': return 'Rate the service and earn 5 extra points';
			case 'screens.review.rateAndPost': return 'Rate & Post';
			case 'screens.review.rateAndPostDescription': return 'Rate the service and earn 10 extra points';
			case 'screens.review.skip': return 'Rate later';
			case 'bnb.discover': return 'Discover';
			case 'bnb.feed': return 'Feed';
			case 'bnb.search': return 'Search';
			case 'bnb.profile': return 'Profile';
			case 'filters.foodTypeTitle': return 'Types of food';
			case 'filters.dietPreferencesTitle': return 'Diet preferences';
			case 'filters.foodTitle': return 'Food';
			case 'filters.sortTitle': return 'Sort';
			case 'filters.foodType.american': return 'American';
			case 'filters.foodType.arabic': return 'Arabic';
			case 'filters.foodType.asian': return 'Asian';
			case 'filters.foodType.bakery': return 'Bakery';
			case 'filters.foodType.burger': return 'Burger';
			case 'filters.foodType.chinese': return 'Chinese';
			case 'filters.foodType.curry': return 'Curry';
			case 'filters.foodType.german': return 'German';
			case 'filters.foodType.doner': return 'Doner';
			case 'filters.foodType.iceCream': return 'Ice Cream';
			case 'filters.foodType.espresso': return 'Espresso';
			case 'filters.foodType.falafel': return 'Falafel';
			case 'filters.foodType.fish': return 'Fish';
			case 'filters.foodType.breakfast': return 'Breakfast';
			case 'filters.foodType.drinks': return 'Drinks';
			case 'filters.foodType.chicken': return 'Chicken';
			case 'filters.foodType.hotdog': return 'Hotdog';
			case 'filters.foodType.indian': return 'Indian';
			case 'filters.foodType.italian': return 'Italian';
			case 'filters.foodType.japanese': return 'Japanese';
			case 'filters.foodType.coffee': return 'Coffee';
			case 'filters.foodType.cake': return 'Cake';
			case 'filters.foodType.seafood': return 'Seafood';
			case 'filters.foodType.mexican': return 'Mexican';
			case 'filters.foodType.lunch': return 'Lunch';
			case 'filters.foodType.desserts': return 'Desserts';
			case 'filters.foodType.noodles': return 'Noodles';
			case 'filters.foodType.austrian': return 'Austrian';
			case 'filters.foodType.pakistani': return 'Pakistani';
			case 'filters.foodType.pasta': return 'Pasta';
			case 'filters.foodType.persian': return 'Persian';
			case 'filters.foodType.pizza': return 'Pizza';
			case 'filters.foodType.pokeBowl': return 'Poke Bowl';
			case 'filters.foodType.ramen': return 'Ramen';
			case 'filters.foodType.frenchFries': return 'French Fries';
			case 'filters.foodType.salads': return 'Salads';
			case 'filters.foodType.sandwiches': return 'Sandwiches';
			case 'filters.foodType.snacks': return 'Snacks';
			case 'filters.foodType.steaks': return 'Steaks';
			case 'filters.foodType.soups': return 'Soups';
			case 'filters.foodType.sushi': return 'Sushi';
			case 'filters.foodType.syrian': return 'Syrian';
			case 'filters.foodType.thai': return 'Thai';
			case 'filters.foodType.turkish': return 'Turkish';
			case 'filters.foodType.vegan': return 'Vegan';
			case 'filters.foodType.vegetarian': return 'Vegetarian';
			case 'filters.foodType.vietnamese': return 'Vietnamese';
			case 'filters.foodType.wraps': return 'Wraps';
			case 'filters.foodType.bar': return 'Bar';
			case 'filters.foodType.bowls': return 'Bowls';
			case 'filters.foodType.cafe': return 'Café';
			case 'filters.foodType.cocktails': return 'Cocktails';
			case 'filters.foodType.greek': return 'Greek';
			case 'filters.foodType.kebab': return 'Kebab';
			case 'filters.foodType.restaurant': return 'Restaurant';
			case 'filters.foodType.spanish': return 'Spanish';
			case 'filters.dietPreferences.kosher': return 'Kosher';
			case 'filters.dietPreferences.halal': return 'Halal';
			case 'filters.dietPreferences.vegetarian': return 'Vegetarian';
			case 'filters.dietPreferences.vegan': return 'Vegan';
			case 'filters.dietPreferences.glutenFree': return 'Gluten Free';
			case 'filters.dietPreferences.lactoseFree': return 'Lactose Free';
			case 'filters.dietPreferences.nutsFree': return 'Nuts Free';
			case 'filters.food.breakfast': return 'Breakfast';
			case 'filters.food.brunch': return 'Brunch';
			case 'filters.food.lunch': return 'Lunch';
			case 'filters.food.dinner': return 'Dinner';
			case 'filters.food.snack': return 'Snack';
			case 'filters.sort.rating': return 'by rating';
			case 'tools.imagePicker.title': return 'Cropper';
			case 'custom.pointProgress.silver': return 'Silver';
			case 'custom.pointProgress.gold': return 'Gold';
			case 'custom.pointProgress.test': return 'Test';
			case 'custom.pointProgress.member': return 'Member';
			case 'custom.pointProgress.points': return ({required Object value, required Object opposite}) => '${value}/${opposite} Points';
			case 'custom.pointProgress.pointsString': return ({required Object value}) => '${value} Points';
			case 'custom.isReached': return 'is reached';
			case 'custom.youMadeIt': return 'You made it';
			case 'empty.image': return 'No Image';
			case 'coupons.available': return 'Available Coupons';
			case 'coupons.activatable': return 'Activatable Coupons';
			case 'coupons.noCoupons': return 'No available coupons';
			case 'coupons.appliable': return 'Appliable';
			case 'coupons.canBeReactivated': return 'Can be reactivated';
			case 'coupons.nonAppliable': return 'Non appliable';
			case 'coupon.title': return 'Mutualz';
			case 'coupon.off': return 'off';
			case 'coupon.percentage': return ({required Object value}) => '${value}%';
			case 'coupon.separator': return 'for';
			case 'cities.berlin': return 'Berlin';
			case 'cities.oldenburg': return 'Oldenburg';
			case 'cities.osnaburg': return 'Osnabrück';
			case 'specials.title': return 'Specials';
			case 'specials.single': return 'Special';
			case 'specials.availableTitle': return 'Special available';
			case 'specials.availableAt': return 'Available at';
			case 'genders.male': return 'Male';
			case 'genders.female': return 'Female';
			case 'genders.diverse': return 'Diverse';
			case 'restaurantInfoTab.deals': return 'Deals';
			case 'restaurantInfoTab.menu': return 'Menu';
			case 'restaurantInfoTab.feed': return 'Feed';
			case 'scanner.label': return 'Scan the QR code\nto access the restaurant';
			case 'scanner.errorDifferentRestaurant': return 'You are scanning a QR code from a different restaurant';
			case 'scanner.errorRestaurantClosed': return ({required Object name}) => '${name} is closed. Try again during the opening hours.';
			case 'postCard.likes': return 'Likes';
			case 'postCard.comments': return 'Comments';
			case 'weekdays.monday': return 'Monday';
			case 'weekdays.tuesday': return 'Tuesday';
			case 'weekdays.wednesday': return 'Wednesday';
			case 'weekdays.thursday': return 'Thursday';
			case 'weekdays.friday': return 'Friday';
			case 'weekdays.saturday': return 'Saturday';
			case 'weekdays.sunday': return 'Sunday';
			case 'paymentInfo.total': return 'Total';
			case 'paymentInfo.discount': return 'Discount';
			case 'paymentInfo.totalWithMutualz': return 'Total with Mutualz';
			case 'loyalty.test': return 'Test';
			case 'loyalty.member': return 'Member';
			case 'loyalty.silver': return 'Silver';
			case 'loyalty.gold': return 'Gold';
			case 'earnWidget.label': return 'You\'ve earned';
			case 'earnWidget.points': return 'points';
			case 'reportUserPost.title': return 'Report User Post';
			case 'reportUserPost.hint': return 'Please describe the reason for your report';
			case 'reportUserPost.items.nudity': return 'Nudity or sexualization';
			case 'reportUserPost.items.violence': return 'Blood or violence';
			case 'reportUserPost.items.suicide': return 'Suicide, self-harm or eating disorders';
			case 'reportUserPost.items.hate': return 'Hate or incitement to hatred';
			case 'reportUserPost.items.ads': return 'Sale or advertising of products/services outside the catering sector';
			case 'blockUserPost.title': return 'Block User Post';
			case 'blockUserPost.hint': return 'Please describe the reason for your report';
			case 'blockUserPost.items.nudity': return 'Nudity or sexualization';
			case 'blockUserPost.items.violence': return 'Blood or violence';
			case 'blockUserPost.items.suicide': return 'Suicide, self-harm or eating disorders';
			case 'blockUserPost.items.hate': return 'Hate or incitement to hatred';
			case 'blockUserPost.items.ads': return 'Sale or advertising of products/services outside the catering sector';
			case 'blockUser.title': return 'Block User';
			case 'blockUser.hint': return 'Please describe the block for your report';
			case 'blockUser.items.insultsOrBullying': return 'Insults or bullying';
			case 'blockUser.items.nudityOrSexualization': return 'Nudity or sexualization';
			case 'blockUser.items.bloodOrViolence': return 'Blood or violence';
			case 'blockUser.items.spam': return 'Spam';
			case 'blockUser.items.falseIdentityOrIdentityStolen': return 'False identity/identity stolen';
			case 'blockUser.items.accountCouldHaveBeenHacked': return 'Account could have been hacked';
			case 'blockUser.items.personUnder16': return 'Person is under 16';
			case 'reportUser.title': return 'Report User';
			case 'reportUser.hint': return 'Please describe the reason for your report';
			case 'reportUser.items.insultsOrBullying': return 'Insults or bullying';
			case 'reportUser.items.nudityOrSexualization': return 'Nudity or sexualization';
			case 'reportUser.items.bloodOrViolence': return 'Blood or violence';
			case 'reportUser.items.spam': return 'Spam';
			case 'reportUser.items.falseIdentityOrIdentityStolen': return 'False identity/identity stolen';
			case 'reportUser.items.accountCouldHaveBeenHacked': return 'Account could have been hacked';
			case 'reportUser.items.personUnder16': return 'Person is under 16';
			case 'strangerProfile.followers': return 'Followers';
			case 'strangerProfile.following': return 'Following';
			case 'strangerProfile.friendsInCommon': return 'friends in common';
			case 'tutorial.map.qrCode': return 'Start your order. Here you can scan the QR code to place your order.';
			case 'tutorial.filter.info': return 'Filtering changes the results on the map and in the list view. Slide the filters up for the list view.';
			case 'tutorial.feed.addPost': return 'Here you can post a contribution.';
			case 'tutorial.feed.search': return 'Do you want to follow friends or family? Here you can search for the person.';
			case 'tutorial.restaurant.rating': return 'Click on the stars or the clock to get more information';
			case 'tutorial.restaurant.favorite': return 'Here you can mark the location as a favorite';
			case 'tutorial.restaurant.dietPreferences': return 'Dietary forms and intolerances. All gray icons are information from the restaurateur.';
			case 'tutorial.restaurant.dietPreferencesConfirmed': return 'If it\'s red, the information has been confirmed by users.';
			case 'tutorial.restaurant.deals': return 'Here you can see all deals and coupons';
			case 'tutorial.restaurant.menu': return 'Here you can browse in advance';
			case 'tutorial.profile.subscriptions': return 'Here are all the levels you can reach within the loyalty program. Additionally, you can see how many points you have in total.';
			case 'tutorial.profile.orders': return 'Here you can view and download your bills';
			case 'tutorial.profile.dietPreferences': return 'Here you can adjust your dietary preferences';
			case 'tutorial.orders.tableNumber': return 'You can find the table number next to the QR code on the table';
			case 'tutorial.orders.cart': return 'This is your shopping cart, here you can see all the products you have added.';
			case 'tutorial.orders.cartInside': return 'This is what your shopping cart looks like inside. You can adjust your orders here';
			case 'tutorial.orders.orderAndPayNow': return 'Here you order and pay immediately, this makes the most sense if you need to leave quickly, for example';
			case 'tutorial.orders.orderAndPayLater': return 'Here you can order and pay later when you leave the venue';
			case 'tutorial.orders.usedCoupons': return 'You have already used these coupons and can unlock them again with points';
			case 'tutorial.orders.loyaltyPoints': return 'You can use these points at this venue to unlock coupons again';
			case 'tutorial.orders.congratulations': return 'Congratulations! You have collected your first points in the loyalty program!';
			case 'tutorial.loyalty.fourLevels': return 'There are four levels in the loyalty program. You always start at the first level and then have one month to advance to the Gold level.';
			case 'tutorial.loyalty.test': return 'You collect points with every purchase and thus advance to the next level.\n\nThe best part: Every purchase counts regardless of which MUTUALZ partner you shop with!\n\nImportant: At the end of the month you will be reset back to this level and 0 points!*';
			case 'tutorial.loyalty.member': return 'At this level you receive 5% discount on every bill with all partners.\n\nThe faster you reach this level, the more time you have to climb the next levels!\n\nSecret tip: With the Mutualz subscription you start at this level at the beginning of each month! Time advantage means price advantage!';
			case 'tutorial.loyalty.silver': return 'This level rewards the reliable and loyal!\n\nWith Silver you save 10% on the total bill in restaurants where you have reached Silver.\n\nYou can only reach this level by collecting enough points in one restaurant and advancing through levels.*';
			case 'tutorial.loyalty.gold': return 'Congratulations Your Majesty!\n\nWhen they say: "The customer is king!" Then they speak exclusively about you!\n\nYou save 15% on the total bill in the restaurant where you achieved this level.';
			case 'tutorial.loyalty.subscription': return 'Subscribe to MUTUALZ now and get 5% discount immediately in addition to your coupons and start every month with a time advantage !!!';
			case 'tutorial.loyalty.buttons.later': return 'Rather later...';
			case 'tutorial.loyalty.buttons.subscribe': return 'Subscribe';
			case 'tutorial.loyalty.buttons.next': return 'Continue';
			case 'tutorial.loyalty.terms': return '*for more information check our Terms and Conditions';
			case 'tutorial.loyalty.level': return 'Level';
			case 'tutorial.loyalty.oneCalendarMonth': return 'One calendar month time';
			default: return null;
		}
	}
}

extension on _StringsDe {
	dynamic _flatMapFunction(String path) {
		switch (path) {
			case 'language': return 'Deutsch';
			case 'title': return 'Mutualz';
			case 'titleDev': return 'Mutualz Dev';
			case 'errors.fieldEmpty': return 'Dieses Feld sollte nicht leer sein';
			case 'errors.emailInvalid': return 'Ungültige E-Mail-Adresse';
			case 'errors.passwordInvalid': return 'Das Passwort sollte mindestens 8 Zeichen lang sein.';
			case 'errors.userNameInvalid': return 'Der Benutzername sollte mindestens 2 Zeichen lang sein.';
			case 'errors.passwordNotMatch': return 'Die Passwörter stimmen nicht überein';
			case 'errors.otpEmpty': return 'OTP sollte nicht leer sein';
			case 'errors.unknown': return 'Unbekannter Fehler';
			case 'errors.errorAuthConflict': return 'Authentifizierungskonflikt';
			case 'errors.errorAuthTokenExpired': return 'Authentifizierungstoken abgelaufen';
			case 'errors.errorSocialAuthFailed': return 'Soziale Authentifizierung fehlgeschlagen';
			case 'errors.errorAuthOperationInProgress': return 'Authentifizierungsvorgang läuft';
			case 'errors.errorAuthCancelled': return 'Authentifizierung abgebrochen';
			case 'errors.privacyNotAccepted': return 'Datenschutzerklärung und Allgemeine Geschäftsbedingungen nicht akzeptiert';
			case 'errors.baseTextLength': return 'Der Text sollte zwischen 2 und 60 Zeichen lang sein.';
			case 'errors.phoneNumberInvalid': return 'Ungültige Telefonnummer';
			case 'errors.doesNotContainNumber': return 'Das Passwort sollte mindestens eine Zahl enthalten';
			case 'errors.doesNotContainSpecialCharacter': return 'Das Passwort sollte mindestens ein Sonderzeichen enthalten';
			case 'errors.doesNotContainUppercase': return 'Das Passwort muss mindestens einen Großbuchstaben enthalten';
			case 'errors.doesNotContainLowercase': return 'Das Passwort muss mindestens einen Kleinbuchstaben enthalten';
			case 'errors.disabledLocationService': return 'Standortdienste sind deaktiviert. Bitte aktiviere diese, um fortzufahren.';
			case 'errors.locationPermissionDenied': return 'Standortberechtigungen sind verweigert. Bitte aktiviere diese, um fortzufahren.';
			case 'errors.locationPermissionPermanentlyDenied': return 'Standortberechtigungen sind dauerhaft verweigert. Wir können keine Berechtigungen anfordern.';
			case 'errors.noRestaurantImage': return 'Kein Restaurantbild verfügbar';
			case 'errors.emptyRating': return 'Bitte vergib Sterne für alle Bewertungskriterien.';
			case 'errors.defaultPaymentMethod': return 'Bitte wähle eine Zahlungsmethode aus';
			case 'errors.restartError': return 'Ein Fehler ist aufgetreten. Bitte starte die App neu.';
			case 'buttons.save': return 'Speichern';
			case 'buttons.send': return 'Senden';
			case 'buttons.ok': return 'OK';
			case 'buttons.add': return '';
			case 'buttons.edit': return 'Bearbeiten';
			case 'buttons.order': return 'Bestellen';
			case 'buttons.pay': return 'Bezahlen';
			case 'buttons.orderAndPay': return 'Bestellen & Zahlen';
			case 'buttons.seeAll': return 'Alle anzeigen';
			case 'buttons.back': return 'Zurück';
			case 'buttons.savePoints': return 'Mehr Punkte sammeln';
			case 'buttons.tryAgain': return 'Nochmals versuchen';
			case 'buttons.tryAgainLater': return 'Später erneut versuchen';
			case 'buttons.skip': return 'Überspringen';
			case 'dialogs.error.title': return 'Fehler';
			case 'dialogs.success.title': return 'Ausgezeichnet!';
			case 'dialogs.success.changes': return 'Deine Änderungen wurden gespeichert';
			case 'dialogs.buttonLabel': return 'OK';
			case 'dialogs.options.deletePost': return 'Beitrag löschen';
			case 'dialogs.options.report': return 'Melden';
			case 'dialogs.options.unfollow': return 'Nicht mehr folgen';
			case 'dialogs.options.block': return 'Blockieren';
			case 'dialogs.options.follow': return 'Folgen';
			case 'dialogs.numberTable.title': return 'Gib deine Tischnummer ein.';
			case 'dialogs.numberTable.hint': return 'Tischnummer';
			case 'screens.splash.title': return 'Mutualz';
			case 'screens.auth.welcome': return 'Willkommen\nbei Mutualz';
			case 'screens.auth.tabs.login': return 'Einloggen';
			case 'screens.auth.tabs.registration': return 'Registrierung';
			case 'screens.auth.login.email': return 'E-Mail';
			case 'screens.auth.login.password': return 'Passwort';
			case 'screens.auth.login.buttonLabel': return 'Einloggen';
			case 'screens.auth.login.forgotPassword': return 'Passwort vergessen?';
			case 'screens.auth.registration.labels.gender': return 'Geschlecht';
			case 'screens.auth.registration.labels.firstName': return 'Vorname';
			case 'screens.auth.registration.labels.lastName': return 'Nachname';
			case 'screens.auth.registration.labels.username': return 'Benutzername*';
			case 'screens.auth.registration.labels.birthDate': return 'Geburtsdatum*';
			case 'screens.auth.registration.labels.email': return 'E-Mail*';
			case 'screens.auth.registration.labels.phone': return 'Rufnummer';
			case 'screens.auth.registration.labels.password': return 'Passwort*';
			case 'screens.auth.registration.labels.confirmPassword': return 'Passwort bestätigen*';
			case 'screens.auth.registration.terms.agreement': return 'Ich stimme zu';
			case 'screens.auth.registration.terms.conditions': return 'Bedingungen';
			case 'screens.auth.registration.terms.separator': return ' und ';
			case 'screens.auth.registration.terms.privacy': return 'Datenschutzbestimmungen';
			case 'screens.auth.registration.buttonLabel': return 'Registrieren';
			case 'screens.auth.opt.title': return 'OTP-Code eingeben';
			case 'screens.auth.opt.description': return 'Wir haben den OTP-Code an deine E-Mail geschickt.';
			case 'screens.auth.opt.buttonLabel': return 'OK';
			case 'screens.auth.opt.resendLabel': return 'Zugangscode erneut senden';
			case 'screens.auth.opt.resendCodeAvailable': return 'Code zum erneuten Senden verfügbar';
			case 'screens.forgot.title': return 'Passwort vergessen';
			case 'screens.forgot.description': return 'Bitte gib die E-Mail-Adresse ein, mit der du dich registriert hast. Ein Link zum Ändern deines Passworts wird dir umgehend zugesandt.';
			case 'screens.forgot.emailLabel': return 'E-Mail';
			case 'screens.forgot.buttonLabel': return 'Senden';
			case 'screens.forgot.backButtonLabel': return 'Zurück';
			case 'screens.forgot.successDescription': return 'Du kannst deine E-Mail überprüfen, um den Link zur Wiederherstellung des Passworts zu finden';
			case 'screens.reset.title': return 'Ändere dein Passwort';
			case 'screens.reset.description': return 'Füge hier dein neues Passwort ein';
			case 'screens.reset.passwordLabel': return 'Passwort*';
			case 'screens.reset.repeatPasswordLabel': return 'Passwort wiederholen*';
			case 'screens.reset.buttonLabel': return 'Passwort ändern';
			case 'screens.reset.successMessage': return 'Herzlichen Glückwunsch, dein Passwort wurde geändert';
			case 'screens.onboarding.personalInfo.title': return 'Freiwillige Angaben,';
			case 'screens.onboarding.personalInfo.description': return 'teile ein paar grundlegende Details mit uns';
			case 'screens.onboarding.lifestyle.title': return 'Erzähle uns von dir,';
			case 'screens.onboarding.lifestyle.description': return 'damit wir dir genau die Angebote bieten können, die zu deinem Leben passen';
			case 'screens.onboarding.lifestyle.job.title': return 'Beruf';
			case 'screens.onboarding.lifestyle.job.employee': return 'Angestellter';
			case 'screens.onboarding.lifestyle.job.student': return 'Schüler';
			case 'screens.onboarding.lifestyle.job.collegeStudent': return 'Student';
			case 'screens.onboarding.lifestyle.job.selfEmployed': return 'Selbstständig';
			case 'screens.onboarding.lifestyle.job.housewife': return 'Hausmann/Hausfrau';
			case 'screens.onboarding.lifestyle.job.retired': return 'Rentner';
			case 'screens.onboarding.lifestyle.allergies.title': return 'Unverträglichkeiten';
			case 'screens.onboarding.lifestyle.allergies.gluten': return 'Gluten';
			case 'screens.onboarding.lifestyle.allergies.lactose': return 'Laktose';
			case 'screens.onboarding.lifestyle.allergies.nutAllergy': return 'Nüsse';
			case 'screens.onboarding.lifestyle.diet.title': return 'Ernährungsform';
			case 'screens.onboarding.lifestyle.diet.halal': return 'Halal';
			case 'screens.onboarding.lifestyle.diet.kosher': return 'Koscher';
			case 'screens.onboarding.lifestyle.diet.vegan': return 'Vegan';
			case 'screens.onboarding.lifestyle.diet.vegetarian': return 'Vegetarisch';
			case 'screens.onboarding.taste.title': return 'Zeig uns, was dir gefällt';
			case 'screens.onboarding.taste.description': return 'Wähle deine Präferenzen aus';
			case 'screens.onboarding.drinks.title': return 'Getränke';
			case 'screens.onboarding.drinks.tea': return 'Tee';
			case 'screens.onboarding.drinks.softDrinks': return 'Softdrinks';
			case 'screens.onboarding.drinks.coffee': return 'Kaffee';
			case 'screens.onboarding.drinks.beer': return 'Bier';
			case 'screens.onboarding.drinks.cocktails': return 'Cocktails';
			case 'screens.onboarding.drinks.juice': return 'Saft';
			case 'screens.onboarding.drinks.smoothie': return 'Smoothies';
			case 'screens.onboarding.drinks.wine': return 'Wein';
			case 'screens.onboarding.drinks.iceTea': return 'Eistee';
			case 'screens.onboarding.food.title': return 'Essen';
			case 'screens.onboarding.food.kebab': return 'Döner';
			case 'screens.onboarding.food.sweets': return 'Dessert';
			case 'screens.onboarding.food.pizza': return 'Pizza';
			case 'screens.onboarding.food.burger': return 'Burger';
			case 'screens.onboarding.food.sushi': return 'Sushi';
			case 'screens.onboarding.food.pasta': return 'Nudeln';
			case 'screens.onboarding.food.fish': return 'Fisch';
			case 'screens.onboarding.food.salad': return 'Salat';
			case 'screens.onboarding.food.fingerFood': return 'Fingerfood';
			case 'screens.onboarding.events.title': return 'Events';
			case 'screens.onboarding.events.party': return 'Party';
			case 'screens.onboarding.events.concert': return 'Konzert';
			case 'screens.onboarding.events.markets': return 'Märkte';
			case 'screens.onboarding.events.tastings': return 'Verkostungen';
			case 'screens.onboarding.events.workshops': return 'Workshops';
			case 'screens.onboarding.events.sports': return 'Sport';
			case 'screens.onboarding.events.theater': return 'Theater';
			case 'screens.onboarding.events.readings': return 'Lesungen';
			case 'screens.onboarding.events.cinema': return 'Kino';
			case 'screens.onboarding.nextButtonLabel': return 'Weiter';
			case 'screens.onboarding.backButtonLabel': return 'Zurück';
			case 'screens.socialOnboarding.title': return 'Lernen wir uns besser kennen';
			case 'screens.socialOnboarding.description': return 'Erzähle uns etwas über dich';
			case 'screens.socialOnboarding.nextButtonLabel': return 'Weiter';
			case 'screens.socialOnboarding.skipButtonLabel': return 'Überspringen';
			case 'screens.notificationPermission.title': return 'MITTEILUNGEN';
			case 'screens.notificationPermission.description': return 'Stelle deine Benachrichtigungen an, damit wir dich informieren können, wenn dein Essen fertig ist.';
			case 'screens.notificationPermission.buttons.decideLater': return 'Später entscheiden';
			case 'screens.notificationPermission.buttons.next': return 'Weiter';
			case 'screens.search.noResults': return 'Es wurden leider keine Ergebnisse gefunden';
			case 'screens.search.inputLabel': return 'Suche...';
			case 'screens.search.sortByRating': return 'Nach Bewertung';
			case 'screens.search.emptyCollection': return 'Wir müssen Sie\nbesser kennen lernen';
			case 'screens.discover.info.timeLabel': return 'Öffnungszeiten';
			case 'screens.restaurant.bookTableButton': return 'Tisch reservieren';
			case 'screens.restaurant.orderPayButton': return 'Hier bestellen';
			case 'screens.restaurant.booking.title': return ({required Object name}) => 'Hi${name}, es wäre toll, dir einen Tisch anbieten zu können.';
			case 'screens.restaurant.booking.calendarButtonLabel': return 'Weiter';
			case 'screens.restaurant.booking.timeButtonLabel': return 'Buchen';
			case 'screens.restaurant.booking.resultButtonLabel': return 'Alles klar';
			case 'screens.restaurant.booking.errorButtonLabel': return 'Ok';
			case 'screens.restaurant.booking.availableTimeLabel': return 'Verfügbare Zeiten';
			case 'screens.restaurant.booking.estimatedTimeLabel': return 'Voraussichtliche Dauer deines Besuchs';
			case 'screens.restaurant.booking.numberOfDinnersLabel': return 'Anzahl der Gäste';
			case 'screens.restaurant.booking.resultTitle': return 'Du hast einen Tisch angefragt';
			case 'screens.restaurant.booking.resultDescription': return 'Wir werden dir eine Bestätigung per Mail und Benachrichtigung schicken.';
			case 'screens.restaurant.info.scheduleTitle': return 'Öffnungszeiten';
			case 'screens.restaurant.menu.empty': return 'Keine Speisekarte verfügbar';
			case 'screens.restaurant.menu.ratingFilter': return '4+';
			case 'screens.restaurant.menu.budgetFilter': return 'Budget';
			case 'screens.restaurant.menu.budgetFilterHint': return '9,99';
			case 'screens.restaurant.menu.extras': return 'Extras';
			case 'screens.restaurant.menu.sauces': return 'Saucen';
			case 'screens.restaurant.emptyDietPreferences': return 'Keine Angaben zur Ernährungsform oder Unverträglichkeiten';
			case 'screens.restaurant.deals.title': return 'Sonderangebote';
			case 'screens.restaurant.deals.empty': return 'Keine Sonderangebote verfügbar';
			case 'screens.profile.hint': return 'Dieses Profil wurde gelöscht.';
			case 'screens.profile.tabs.post': return 'Beiträge';
			case 'screens.profile.tabs.settings': return 'Einstellungen';
			case 'screens.profile.followers': return 'Follower';
			case 'screens.profile.following': return 'Du folgst';
			case 'screens.profile.subscriptions': return 'Stufen';
			case 'screens.profile.settings.general': return 'Allgemeine Einstellungen';
			case 'screens.profile.settings.help': return 'Hilfe';
			case 'screens.profile.settings.feedback': return 'Feedback';
			case 'screens.profile.settings.orders': return 'Bestellungen';
			case 'screens.profile.settings.logout': return 'Abmelden';
			case 'screens.generalSettings.profile': return 'Profil';
			case 'screens.generalSettings.password': return 'Passwort ändern';
			case 'screens.generalSettings.preferences': return 'Präferenzen';
			case 'screens.generalSettings.payment': return 'Zahlungsmethode';
			case 'screens.changePasswordSettings.title': return 'Passwort ändern';
			case 'screens.changePasswordSettings.newPassword': return 'Neues Passwort';
			case 'screens.changePasswordSettings.confirmNewPassword': return 'Neues Passwort bestätigen';
			case 'screens.changePasswordSettings.buttonLabel': return 'Passwort ändern';
			case 'screens.changePreferences.buttonLabel': return 'Präferenzen speichern';
			case 'screens.userProfileSettings.deleteAccount': return 'Konto löschen';
			case 'screens.userProfileSettings.deleteAccountTitle': return 'Bist du sicher, dass du dein Konto löschen möchtest?';
			case 'screens.userProfileSettings.deleteAccountDescription': return 'Du verlierst den Zugang zu allen exklusiven Rabatten von Mutualz und dein Profil und deine Beiträge werden endgültig gelöscht.';
			case 'screens.userProfileSettings.buttonCancel': return 'Nicht löschen';
			case 'screens.userProfileSettings.buttonOk': return 'Ich bin sicher';
			case 'screens.feedback.title': return 'Feedback';
			case 'screens.feedback.messageLabel': return 'Nachricht';
			case 'screens.feedback.design': return 'Design';
			case 'screens.feedback.userFriendliness': return 'Benutzerfreundlichkeit';
			case 'screens.feedback.paymentFunction': return 'Zahlungsfunktion';
			case 'screens.feedback.orderFunction': return 'Bestellfunktion';
			case 'screens.feedback.community': return 'Community';
			case 'screens.feedback.couponsDiscounts': return 'Gutscheine & Rabatte';
			case 'screens.feedback.recommendations': return 'Empfehlungen';
			case 'screens.feedback.pushNotifications': return 'Push-Benachrichtigungen';
			case 'screens.feedback.speed': return 'Geschwindigkeit';
			case 'screens.feedback.otherFeatures': return 'Andere Funktionen';
			case 'screens.feedback.successMessage': return 'Vielen Dank für dein Feedback!.';
			case 'screens.faq.title': return 'Häufig gestellte Fragen';
			case 'screens.faq.contactUs': return 'Kontaktiere uns';
			case 'screens.faq.empty': return 'Keine Fragen gefunden';
			case 'screens.contact.title': return 'Kontakt-Formular';
			case 'screens.contact.messageLabel': return 'Nachricht';
			case 'screens.contact.registration': return 'Registrierung';
			case 'screens.contact.payment': return 'Zahlungsfunktion';
			case 'screens.contact.subscription': return 'Abo';
			case 'screens.contact.report': return 'Fehler melden';
			case 'screens.contact.other': return 'Sonstiges';
			case 'screens.contact.successMessage': return 'Vielen Dank für deine Nachricht! Wir werden uns so schnell wie möglich bei dir melden.';
			case 'screens.subscription.title': return 'Stufen';
			case 'screens.subscription.description': return 'Wähle eine Stufe aus';
			case 'screens.subscription.levels.test': return 'Test';
			case 'screens.subscription.levels.member': return 'Member';
			case 'screens.subscription.levels.silver': return 'Silber';
			case 'screens.subscription.levels.gold': return 'Gold';
			case 'screens.subscription.levelsDescription.test': return 'Probiere Mutualz aus und nutze alle unsere Gutscheine kostenlos.';
			case 'screens.subscription.levelsDescription.member': return 'Du zahlst 5% weniger bei jeder Rechnung.';
			case 'screens.subscription.levelsDescription.silver': return 'Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Rabatt von 10% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde).';
			case 'screens.subscription.levelsDescription.gold': return 'Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Rabatt von 15% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde).';
			case 'screens.subscription.buttons.upgrade': return 'Jetzt upgraden für 9,99€ monatlich';
			case 'screens.subscription.buttons.recommend': return 'Empfehle uns einem Freund, um ein kostenloses Upgragde für 1 Monat zu erhalten';
			case 'screens.subscription.buttons.cancel': return 'Abbrechen';
			case 'screens.subscription.buttons.reactivate': return 'Reaktivieren';
			case 'screens.subscription.info': return ({required Object date}) => 'Dein Abo läuft am ${date} ab';
			case 'screens.subscription.discount.withPercent': return ({required num n, required Object percent}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('de'))(n,
				one: '${percent}% für 1 Tag',
				other: '${percent}% für ${n} Tage',
			);
			case 'screens.subscription.daysRemaining': return ({required num n}) => (_root.$meta.cardinalResolver ?? PluralResolvers.cardinal('de'))(n,
				one: 'Noch 1 Tag',
				other: 'Noch ${n} Tage',
			);
			case 'screens.subscription.discountOnTotalBill': return 'Rabatt auf die\nGesamtrechnung*';
			case 'screens.comments.title': return 'Kommentare';
			case 'screens.comments.inputLabel': return 'Schreibe einen Kommentar';
			case 'screens.comments.empty': return 'Keine Kommentare verfügbar';
			case 'screens.orderProcess.title': return 'Speisekarte';
			case 'screens.orderProcess.empty': return 'Keine Speisekarte verfügbar';
			case 'screens.orderProcess.orderButton': return 'Bestellen/Bezahlen';
			case 'screens.orderProcess.orderSuccess': return 'Bestellung erfolgreich. Guten Appetit!';
			case 'screens.orderProcess.paymentSuccess': return 'Zahlung erfolgreich abgeschlossen';
			case 'screens.cart.empty': return 'Keine Artikel im Warenkorb';
			case 'screens.cart.total': return 'Gesamt:';
			case 'screens.cart.dialogNotes.title': return 'Hast du besondere Wünsche?';
			case 'screens.cart.dialogNotes.hint': return 'Notiz';
			case 'screens.cart.dialogRemove.title': return 'Gericht aus dem Warenkorb entfernen';
			case 'screens.cart.dialogRemove.description': return 'Bist du sicher, dass du dieses Gericht aus dem Warenkorb entfernen möchten?';
			case 'screens.cart.dialogRemove.buttonCancel': return 'Abbrechen';
			case 'screens.cart.dialogRemove.buttonConfirm': return 'Ich bin sicher';
			case 'screens.cart.dialogSpecialRemove.title': return 'Angebot aus dem Warenkorb entfernen';
			case 'screens.cart.dialogSpecialRemove.description': return 'Bist du sicher, dass du dieses Angebot aus dem Warenkorb entfernen möchtest?';
			case 'screens.cart.dialogSpecialRemove.buttonCancel': return 'Abbrechen';
			case 'screens.cart.dialogSpecialRemove.buttonConfirm': return 'Ich bin sicher';
			case 'screens.cart.dialogConfirm.title': return 'Bist du sicher, dass du alles gefunden hast?';
			case 'screens.cart.dialogConfirm.description': return 'Dann hier bestellen';
			case 'screens.cart.dialogConfirm.buttonConfirm': return 'Ja, bin ich';
			case 'screens.cart.dialogShare.description': return 'Empfehle uns einem Freund, um eine 1 monatige Mitgliedschaft kostenlos zu erhalten und noch mehr zu sparen';
			case 'screens.cart.dialogShare.buttonShare': return 'Teilen';
			case 'screens.cart.dialogShare.buttonCancel': return 'Später teilen';
			case 'screens.cart.createOrderSuccess': return 'Bestellung erfolgreich erstellt. Guten Appetit!';
			case 'screens.invoice.title': return 'Danke für deinen Besuch';
			case 'screens.invoice.dishes': return 'Gerichte';
			case 'screens.invoice.coupons': return 'Gutscheine:';
			case 'screens.invoice.couponSelection': return 'Gutschein-Auswahl';
			case 'screens.invoice.doNotUseCoupon': return 'Ohne Gutschein';
			case 'screens.invoice.proceedToPayment': return 'Zur Zahlung';
			case 'screens.paymentMethodSettings.title': return 'Zahlungsmethoden';
			case 'screens.paymentMethodSettings.card': return 'Karte';
			case 'screens.paymentMethodSettings.applePay': return 'Apple Pay';
			case 'screens.paymentMethodSettings.googlePay': return 'Google Pay';
			case 'screens.addPost.title': return 'Teile deine Erfahrungen mit der Community';
			case 'screens.addPost.description': return 'Erstelle einen Beitrag über deine besten Momente';
			case 'screens.addPost.uploadImage': return 'Bild hochladen';
			case 'screens.addPost.restaurantHint': return 'Wähle ein Restaurant aus';
			case 'screens.addPost.descriptionHint': return 'Füge hier eine Beschreibung hinzu';
			case 'screens.addPost.attachImage': return 'Bild anhängen';
			case 'screens.addPost.tagYourFriends': return 'Tagge deine Freunde';
			case 'screens.payment.message': return 'Danke für deinen Besuch!';
			case 'screens.payment.buttonLabel': return 'Punkte sammeln';
			case 'screens.payment.success': return 'Zahlung erfolgreich abgeschlossen';
			case 'screens.orders.title': return 'Bestellungen';
			case 'screens.orders.empty': return 'Keine Bestellungen';
			case 'screens.orders.total': return 'Gesamt';
			case 'screens.orders.memberDiscount': return 'Member Rabatt';
			case 'screens.orders.couponDiscount': return 'Gutschein Rabatt';
			case 'screens.orders.totalWithMutualz': return 'Gesamt mit Mutualz';
			case 'screens.orders.summary': return 'Bestellübersicht';
			case 'screens.orders.sendInvoice': return 'Rechnung senden';
			case 'screens.orders.invoiceSent': return 'Rechnung wurde erfolgreich versendet';
			case 'screens.orders.sendingInvoice': return 'Rechnung wird gesendet...';
			case 'screens.progressReward.unlockCoupons': return 'Verwende, um Gutscheine freizuschalten';
			case 'screens.review.rating': return 'Bewertung';
			case 'screens.review.ratingItems.service': return 'Service';
			case 'screens.review.ratingItems.cleanliness': return 'Sauberkeit';
			case 'screens.review.ratingItems.waitingTime': return 'Wartezeit';
			case 'screens.review.ratingItems.foodQuality': return 'Geschmack';
			case 'screens.review.ratingItems.athmosphere': return 'Atmosphäre';
			case 'screens.review.commentHint': return 'Schreibe einen Kommentar für den Gastronomen';
			case 'screens.review.verifyDietary': return 'Bestätige die vom Gastronomen gemachten Angaben';
			case 'screens.review.dietPreferences.yes': return 'Ja';
			case 'screens.review.dietPreferences.no': return 'Nein';
			case 'screens.review.uploadImage': return 'Bild hochladen';
			case 'screens.review.descriptionHint': return 'Beschreibung';
			case 'screens.review.rateTitle': return 'Bewerte jetzt, um zusätzliche Punkte zu erhalten';
			case 'screens.review.points': return 'Punkte';
			case 'screens.review.rate': return 'Bewerten';
			case 'screens.review.rateDescription': return 'Bewerte deinen Aufenthalt und verdiene 5 zusätzliche Punkte';
			case 'screens.review.rateAndPost': return 'Bewerten & Posten';
			case 'screens.review.rateAndPostDescription': return 'Bewerte und erstelle einen Post über deine Erfahrung und verdiene 10 zusätzliche Punkte';
			case 'screens.review.skip': return 'Später bewerten';
			case 'bnb.discover': return 'Entdecken';
			case 'bnb.feed': return 'Feed';
			case 'bnb.search': return 'Suche';
			case 'bnb.profile': return 'Profil';
			case 'filters.foodTypeTitle': return 'Kulinarische Ausrichtung';
			case 'filters.dietPreferencesTitle': return 'Ernährungsform';
			case 'filters.foodTitle': return 'Essenszeiten';
			case 'filters.sortTitle': return 'Sortieren';
			case 'filters.foodType.american': return 'Amerikanisch';
			case 'filters.foodType.arabic': return 'Arabisch';
			case 'filters.foodType.asian': return 'Asiatisch';
			case 'filters.foodType.bakery': return 'Bäckerei';
			case 'filters.foodType.burger': return 'Burger';
			case 'filters.foodType.chinese': return 'Chinesisch';
			case 'filters.foodType.curry': return 'Curry';
			case 'filters.foodType.german': return 'Deutsch';
			case 'filters.foodType.doner': return 'Döner';
			case 'filters.foodType.iceCream': return 'Eis';
			case 'filters.foodType.espresso': return 'Espresso';
			case 'filters.foodType.falafel': return 'Falafel';
			case 'filters.foodType.fish': return 'Fisch';
			case 'filters.foodType.breakfast': return 'Frühstück';
			case 'filters.foodType.drinks': return 'Getränke';
			case 'filters.foodType.chicken': return 'Hähnchen';
			case 'filters.foodType.hotdog': return 'Hotdog';
			case 'filters.foodType.indian': return 'Indisch';
			case 'filters.foodType.italian': return 'Italienisch';
			case 'filters.foodType.japanese': return 'Japanisch';
			case 'filters.foodType.coffee': return 'Kaffee';
			case 'filters.foodType.cake': return 'Kuchen';
			case 'filters.foodType.seafood': return 'Meeresfrüchte';
			case 'filters.foodType.mexican': return 'Mexikanisch';
			case 'filters.foodType.lunch': return 'Mittagessen';
			case 'filters.foodType.desserts': return 'Desserts';
			case 'filters.foodType.noodles': return 'Nudeln';
			case 'filters.foodType.austrian': return 'Österreichisch';
			case 'filters.foodType.pakistani': return 'Pakistani';
			case 'filters.foodType.pasta': return 'Pasta';
			case 'filters.foodType.persian': return 'Persisch';
			case 'filters.foodType.pizza': return 'Pizza';
			case 'filters.foodType.pokeBowl': return 'Poke Bowl';
			case 'filters.foodType.ramen': return 'Ramen';
			case 'filters.foodType.frenchFries': return 'Pommes Frites';
			case 'filters.foodType.salads': return 'Salate';
			case 'filters.foodType.sandwiches': return 'Sandwiches';
			case 'filters.foodType.snacks': return 'Snacks';
			case 'filters.foodType.steaks': return 'Steaks';
			case 'filters.foodType.soups': return 'Suppen';
			case 'filters.foodType.sushi': return 'Sushi';
			case 'filters.foodType.syrian': return 'Syrisch';
			case 'filters.foodType.thai': return 'Thailändisch';
			case 'filters.foodType.turkish': return 'Türkisch';
			case 'filters.foodType.vegan': return 'Vegan';
			case 'filters.foodType.vegetarian': return 'Vegetarisch';
			case 'filters.foodType.vietnamese': return 'Vietnamesisch';
			case 'filters.foodType.wraps': return 'Wraps';
			case 'filters.foodType.bar': return 'Bar';
			case 'filters.foodType.bowls': return 'Bowls';
			case 'filters.foodType.cafe': return 'Café';
			case 'filters.foodType.cocktails': return 'Cocktails';
			case 'filters.foodType.greek': return 'Griechisch';
			case 'filters.foodType.kebab': return 'Kebab';
			case 'filters.foodType.restaurant': return 'Restaurant';
			case 'filters.foodType.spanish': return 'Spanisch';
			case 'filters.dietPreferences.kosher': return 'Koscher';
			case 'filters.dietPreferences.halal': return 'Halal';
			case 'filters.dietPreferences.vegetarian': return 'Vegetarisch';
			case 'filters.dietPreferences.vegan': return 'Vegan';
			case 'filters.dietPreferences.glutenFree': return 'Glutenfrei';
			case 'filters.dietPreferences.lactoseFree': return 'Laktosefrei';
			case 'filters.dietPreferences.nutsFree': return 'Nussfrei';
			case 'filters.food.breakfast': return 'Frühstück';
			case 'filters.food.brunch': return 'Brunch';
			case 'filters.food.lunch': return 'Mittagessen';
			case 'filters.food.dinner': return 'Abendessen';
			case 'filters.food.snack': return 'Snack';
			case 'filters.sort.rating': return 'Nach Bewertung';
			case 'tools.imagePicker.title': return 'Bild auswählen';
			case 'custom.pointProgress.silver': return 'Silber';
			case 'custom.pointProgress.gold': return 'Gold';
			case 'custom.pointProgress.test': return 'Test';
			case 'custom.pointProgress.member': return 'Member';
			case 'custom.pointProgress.points': return ({required Object value, required Object opposite}) => '${value}/${opposite} Punkte';
			case 'custom.pointProgress.pointsString': return ({required Object value}) => '${value} Punkte';
			case 'custom.isReached': return 'erreicht';
			case 'custom.youMadeIt': return 'Du hast es geschafft';
			case 'empty.image': return 'Keine Ergebnisse';
			case 'coupons.available': return 'Verfügbare Gutscheine';
			case 'coupons.activatable': return 'Aktivierbare Gutscheine';
			case 'coupons.noCoupons': return 'No available coupons';
			case 'coupons.appliable': return 'Anwendbar';
			case 'coupons.canBeReactivated': return 'Kann reaktiviert werden';
			case 'coupons.nonAppliable': return 'Nicht zutreffend';
			case 'coupon.title': return 'Mutualz';
			case 'coupon.off': return 'Rabatt';
			case 'coupon.percentage': return ({required Object value}) => '${value}%';
			case 'coupon.separator': return 'für';
			case 'cities.berlin': return 'Berlin';
			case 'cities.oldenburg': return 'Oldenburg';
			case 'cities.osnaburg': return 'Osnabrück';
			case 'specials.title': return 'Sonderangebote';
			case 'specials.single': return 'Sonderangebot';
			case 'specials.availableTitle': return 'Sonderangebot erhältlich';
			case 'specials.availableAt': return 'Genießen um';
			case 'genders.male': return 'Männlich';
			case 'genders.female': return 'Weiblich';
			case 'genders.diverse': return 'Divers';
			case 'restaurantInfoTab.deals': return 'Angebote';
			case 'restaurantInfoTab.menu': return 'Speisekarte';
			case 'restaurantInfoTab.feed': return 'Feed';
			case 'scanner.label': return 'Scanne den QR-Code um zu bestellen';
			case 'scanner.errorDifferentRestaurant': return 'Du scannst einen QR-Code von einem anderen Restaurant';
			case 'scanner.errorRestaurantClosed': return ({required Object name}) => '${name} ist geschlossen. Versuche es während der Öffnungszeiten erneut.';
			case 'postCard.likes': return 'Likes';
			case 'postCard.comments': return 'Kommentare';
			case 'weekdays.monday': return 'Montag';
			case 'weekdays.tuesday': return 'Dienstag';
			case 'weekdays.wednesday': return 'Mittwoch';
			case 'weekdays.thursday': return 'Donnerstag';
			case 'weekdays.friday': return 'Freitag';
			case 'weekdays.saturday': return 'Samstag';
			case 'weekdays.sunday': return 'Sonntag';
			case 'paymentInfo.total': return 'Gesamt';
			case 'paymentInfo.discount': return 'Rabatt';
			case 'paymentInfo.totalWithMutualz': return 'Gesamt mit Mutualz';
			case 'loyalty.test': return 'Test';
			case 'loyalty.member': return 'Member';
			case 'loyalty.silver': return 'Silber';
			case 'loyalty.gold': return 'Gold';
			case 'earnWidget.label': return 'Punkte sammeln';
			case 'earnWidget.points': return 'Punkte';
			case 'reportUserPost.title': return 'Beitrag melden';
			case 'reportUserPost.hint': return 'Gib den Grund für die Meldung an';
			case 'reportUserPost.items.nudity': return 'Nacktheit oder Sexualisierung';
			case 'reportUserPost.items.violence': return 'Blut oder Gewalt';
			case 'reportUserPost.items.suicide': return 'Suizid, Selbstverletzung oder Essstörungen';
			case 'reportUserPost.items.hate': return 'Hass oder Hetze';
			case 'reportUserPost.items.ads': return 'Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors';
			case 'blockUserPost.title': return 'Beitrag melden';
			case 'blockUserPost.hint': return 'Gib den Grund für die Meldung an';
			case 'blockUserPost.items.nudity': return 'Nacktheit oder Sexualisierung';
			case 'blockUserPost.items.violence': return 'Blut oder Gewalt';
			case 'blockUserPost.items.suicide': return 'Suizid, Selbstverletzung oder Essstörungen';
			case 'blockUserPost.items.hate': return 'Hass oder Hetze';
			case 'blockUserPost.items.ads': return 'Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors';
			case 'blockUser.title': return 'Benutzer melden';
			case 'blockUser.hint': return 'Gib den Grund für die Meldung an';
			case 'blockUser.items.insultsOrBullying': return 'Beleidigung oder Mobbing';
			case 'blockUser.items.nudityOrSexualization': return 'Nacktheit oder Sexualisierung';
			case 'blockUser.items.bloodOrViolence': return 'Blut oder Gewalt';
			case 'blockUser.items.spam': return 'Spam';
			case 'blockUser.items.falseIdentityOrIdentityStolen': return 'Falsche Identität/Identität geklaut';
			case 'blockUser.items.accountCouldHaveBeenHacked': return 'Account könnte gehackt worden sein';
			case 'blockUser.items.personUnder16': return 'Person ist unter 16 Jahren';
			case 'reportUser.title': return 'Benutzer melden';
			case 'reportUser.hint': return 'Gib den Grund für die Meldung an';
			case 'reportUser.items.insultsOrBullying': return 'Beleidigung oder Mobbing';
			case 'reportUser.items.nudityOrSexualization': return 'Nacktheit oder Sexualisierung';
			case 'reportUser.items.bloodOrViolence': return 'Blut oder Gewalt';
			case 'reportUser.items.spam': return 'Spam';
			case 'reportUser.items.falseIdentityOrIdentityStolen': return 'Falsche Identität/Identität geklaut';
			case 'reportUser.items.accountCouldHaveBeenHacked': return 'Account könnte gehackt worden sein';
			case 'reportUser.items.personUnder16': return 'Person ist unter 16 Jahren';
			case 'strangerProfile.followers': return 'Follower';
			case 'strangerProfile.following': return 'Folgt';
			case 'strangerProfile.friendsInCommon': return 'Gemeinsame Freunde';
			case 'tutorial.map.qrCode': return 'Beginne deine Bestellung. Hier kannst du den QR-Code scannen, um deine Bestellung aufzugeben.';
			case 'tutorial.filter.info': return 'Durchs Filtern verändert sich das Ergebnis auf der Karte und in der Listenansicht. Schiebe die Filter für die Listenansicht nach oben.';
			case 'tutorial.feed.addPost': return 'Hier kannst du einen Beitrag posten.';
			case 'tutorial.feed.search': return 'Du möchtest Freund*innen oder Familie folgen? Hier kannst du die Person suchen.';
			case 'tutorial.restaurant.rating': return 'Klicke auf die Sterne oder die Uhr, um weitere Infos zu bekommen';
			case 'tutorial.restaurant.favorite': return 'Hier kannst du die Lokalität als Favorit markieren';
			case 'tutorial.restaurant.dietPreferences': return 'Ernährungsformen und Unverträglichkeiten. Alle grauen Icons sind Angaben vom Gastronom.';
			case 'tutorial.restaurant.dietPreferencesConfirmed': return 'Wenn es rot ist, wurde die Angabe von den Usern bestätigt.';
			case 'tutorial.restaurant.deals': return 'Hier siehst du alle Deals und Coupons';
			case 'tutorial.restaurant.menu': return 'Hier kannst du vorab schon stöbern';
			case 'tutorial.profile.subscriptions': return 'Hier sind alle Stufen, die du im Rahmen des Loyalty-Programms erreichen kannst. Zusätzlich siehst du, wie viele Punkte du insgesamt hast.';
			case 'tutorial.profile.orders': return 'Hier kannst du deine Rechnungen einsehen und herunterladen';
			case 'tutorial.profile.dietPreferences': return 'Hier kannst du deine Ernährungspräferenzen anpassen';
			case 'tutorial.orders.tableNumber': return 'Die Tischnummer findest du bei dem QR-Code auf dem Tisch';
			case 'tutorial.orders.cart': return 'Das ist dein Warenkorb, hier siehst du alle Produkte, die du hinzugefügt hast.';
			case 'tutorial.orders.cartInside': return 'So sieht dein Warenkorb von innen aus. Du kannst hier deine Bestellungen anpassen';
			case 'tutorial.orders.orderAndPayNow': return 'Hier bestellst und bezahlst du sofort, das macht am meisten Sinn, wenn du z. B. schnell wieder los musst';
			case 'tutorial.orders.orderAndPayLater': return 'Hier kannst du bestellen und später bezahlen, wenn du die Lokalität verlässt';
			case 'tutorial.orders.usedCoupons': return 'Diese Coupons hast du schon verwendet und kannst sie mit Punkten wieder freischalten';
			case 'tutorial.orders.loyaltyPoints': return 'Diese Punkte kannst du bei dieser Lokalität verwenden, um Coupons wieder freizuschalten';
			case 'tutorial.orders.congratulations': return 'Herzlichen Glückwunsch! Du hast deine ersten Punkte im Loyalty-Programm gesammelt!';
			case 'tutorial.loyalty.fourLevels': return 'Es gibt vier Stufen im Loyalty-Programm. Du startest immer in der ersten und hast dann einen Monat Zeit, um bis in die Gold-Stufe aufzusteigen.';
			case 'tutorial.loyalty.test': return 'Du Sammelst Punkte mit jedem Einkauf und kommst so auf die nächste Stufe.\n\nDas Beste: Jeder Einkauf zählt egal bei welchem MUTUALZ-Partner du Einkaufst!\n\nWichtig: Am Ende des Monats wirst du wieder auf diese Stufe und 0 Punkte zurückgesetzt!*';
			case 'tutorial.loyalty.member': return 'In dieser Stufe erhältst du auf jede Rechnung 5 % Rabatt bei allen Partnern.\n\nJe schneller du diese Stufe erreichst desto mehr Zeit hast die nächsten Stufen zu erklimmen!\n\nGeheimtipp: Mit dem Mutualz-Abo startest in dieser Stufe zum Beginn jeden Monats! Zeitvorteil heißt Preisvorteil!';
			case 'tutorial.loyalty.silver': return 'Diese Stufe belohnt die Zuverlässigen und Treuen!\n\nMit Silber sparst du 10 % auf die Gesamtrechnung in Restaurants in denen du Silber erreicht hast.\n\nDiese Stufe kannst du nur erreichen, indem du genügend Punkte in einem Restaurant sammelst und Stufen aufsteigst.*';
			case 'tutorial.loyalty.gold': return 'Herzlichen Glückwunsch eure Hoheit!\n\nWenn man sagt: "Der Kunde ist König!" Dann spricht man ausschließlich von dir!\n\nDu sparst 15% auf die Gesamtrechnung in dem Restaurant in dem du diese Stufe erreicht hast.';
			case 'tutorial.loyalty.subscription': return 'Schließe jetzt das MUTUALZ-Abo ab und erhalte sofort 5 % Raåbatt zusätzlich zu deinen Coupons und starte jeden Monat mit einem Zeitvorteil !!!';
			case 'tutorial.loyalty.buttons.later': return 'Lieber später ...';
			case 'tutorial.loyalty.buttons.subscribe': return 'Abo abschließen';
			case 'tutorial.loyalty.buttons.next': return 'Weiter';
			case 'tutorial.loyalty.terms': return '*für mehr Informationen schau in unsere AGB\'s';
			case 'tutorial.loyalty.level': return 'Stufe';
			case 'tutorial.loyalty.oneCalendarMonth': return 'Ein Kalendermonat Zeit';
			default: return null;
		}
	}
}
