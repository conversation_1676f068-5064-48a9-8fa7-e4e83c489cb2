{"@@locale": "de", "language": "De<PERSON>ch", "title": "Mutualz", "titleDev": "<PERSON><PERSON>", "errors": {"fieldEmpty": "<PERSON><PERSON>ld sollte nicht leer sein", "emailInvalid": "Ungültige E-Mail-Adresse", "passwordInvalid": "Das Passwort sollte mindestens 8 Zeichen lang sein.", "userNameInvalid": "Der Benutzername sollte mindestens 2 Zeichen lang sein.", "passwordNotMatch": "Die Passwörter stimmen nicht überein", "otpEmpty": "OTP sollte nicht leer sein", "unknown": "Unbekannter Fehler", "errorAuthConflict": "Authentifizierungskonflikt", "errorAuthTokenExpired": "Authentifizierungstoken abgelaufen", "errorSocialAuthFailed": "Soziale Authentifizierung fehlgeschlagen", "errorAuthOperationInProgress": "Authentifizierungsvorgang läuft", "errorAuthCancelled": "Authentifizierung abgebrochen", "privacyNotAccepted": "Datenschutzerklärung und Allgemeine Geschäftsbedingungen nicht akzeptiert", "baseTextLength": "Der Text sollte zwischen 2 und 60 Zeichen lang sein.", "phoneNumberInvalid": "Ungültige Telefonnummer", "doesNotContainNumber": "Das Passwort sollte mindestens eine Zahl enthalten", "doesNotContainSpecialCharacter": "Das Passwort sollte mindestens ein Sonderzeichen enthalten", "doesNotContainUppercase": "Das Passwort muss mindestens einen Großbuchstaben enthalten", "doesNotContainLowercase": "Das Passwort muss mindestens einen Kleinbuchstaben enthalten", "disabledLocationService": "Standortdienste sind deaktiviert. Bitte aktiviere diese, um fortzufahren.", "locationPermissionDenied": "Standortberechtigungen sind verweigert. Bitte aktiviere diese, um fortzufahren.", "locationPermissionPermanentlyDenied": "Standortberechtigungen sind dauerhaft verweigert. Wir können keine Berechtigungen anfordern.", "noRestaurantImage": "Kein Restaurantbild verfügbar", "emptyRating": "Bitte vergib Sterne für alle Bewertungskriterien.", "defaultPaymentMethod": "Bitte wähle eine Zahlungsmethode aus", "restartError": "Ein Fehler ist aufgetreten. Bitte starte die App neu."}, "buttons": {"save": "Speichern", "send": "Senden", "ok": "OK", "add": "", "edit": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON>", "pay": "<PERSON><PERSON><PERSON><PERSON>", "orderAndPay": "Bestellen & Zahlen", "seeAll": "Alle anzeigen", "back": "Zurück", "savePoints": "Mehr Punkte sammeln", "tryAgain": "Nochmals versuchen", "tryAgainLater": "Später erneut versuchen", "skip": "Überspringen"}, "dialogs": {"error": {"title": "<PERSON><PERSON>"}, "success": {"title": "Ausgezeichnet!", "changes": "Deine Änderungen wurden gespeichert"}, "buttonLabel": "OK", "options": {"deletePost": "Beitrag löschen", "report": "Melden", "unfollow": "Nicht mehr folgen", "block": "Blockieren", "follow": "Folgen"}, "numberTable": {"title": "Gib deine Tischnummer ein.", "hint": "Tischnummer"}}, "screens": {"splash": {"title": "Mutualz"}, "auth": {"welcome": "Willkommen\nbei Mutualz", "tabs": {"login": "Einloggen", "registration": "Registrierung"}, "login": {"email": "E-Mail", "password": "Passwort", "buttonLabel": "Einloggen", "forgotPassword": "Passwort vergessen?"}, "registration": {"labels": {"gender": "Geschlecht", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "username": "Benutzername*", "birthDate": "Geburtsdatum*", "email": "E-Mail*", "phone": "Rufnummer", "password": "Passwort*", "confirmPassword": "Passwort bestätigen*"}, "terms": {"agreement": "Ich stimme zu", "conditions": "Bedingungen", "separator": " und ", "privacy": "Datenschutzbestimmungen"}, "buttonLabel": "Registrieren"}, "opt": {"title": "OTP-Code eingeben", "description": "Wir haben den OTP-Code an deine E-Mail geschickt.", "buttonLabel": "OK", "resendLabel": "Zugangscode erneut senden", "resendCodeAvailable": "Code zum erneuten Senden verfügbar"}}, "forgot": {"title": "Passwort vergessen", "description": "Bitte gib die E-Mail-Adresse ein, mit der du dich registriert hast. Ein Link zum Ändern deines Passworts wird dir umgehend zugesandt.", "emailLabel": "E-Mail", "buttonLabel": "Senden", "backButtonLabel": "Zurück", "successDescription": "Du kannst deine E-Mail überprüfen, um den Link zur Wiederherstellung des Passworts zu finden"}, "reset": {"title": "Ändere dein Passwort", "description": "<PERSON>üge hier dein neues Passwort ein", "passwordLabel": "Passwort*", "repeatPasswordLabel": "Passwort wiederholen*", "buttonLabel": "Passwort ändern", "successMessage": "Herzlichen Glückwunsch, dein Passwort wurde geändert"}, "onboarding": {"personalInfo": {"title": "<PERSON><PERSON><PERSON><PERSON>,", "description": "teile ein paar grundlegende Details mit uns"}, "lifestyle": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> uns von dir,", "description": "damit wir dir genau die Angebote bieten können, die zu deinem Leben passen", "job": {"title": "<PERSON><PERSON><PERSON>", "employee": "<PERSON><PERSON><PERSON><PERSON>", "student": "<PERSON><PERSON><PERSON><PERSON>", "collegeStudent": "Student", "selfEmployed": "Selbstständig", "housewife": "<PERSON><PERSON><PERSON>/Hausfrau", "retired": "<PERSON><PERSON><PERSON>"}, "allergies": {"title": "Unverträglichkeiten", "gluten": "Gluten", "lactose": "Laktose", "nutAllergy": "<PERSON><PERSON><PERSON>"}, "diet": {"title": "Ernährungsform", "halal": "<PERSON><PERSON>", "kosher": "<PERSON><PERSON>", "vegan": "Vegan", "vegetarian": "Vegetarisch"}}, "taste": {"title": "<PERSON><PERSON><PERSON> uns, was dir gef<PERSON><PERSON>t", "description": "Wähle deine Präferenzen aus"}, "drinks": {"title": "Getränke", "tea": "<PERSON><PERSON>", "softDrinks": "Softdrinks", "coffee": "<PERSON><PERSON><PERSON>", "beer": "<PERSON><PERSON>", "cocktails": "Cocktails", "juice": "Saft", "smoothie": "Smoothies", "wine": "<PERSON><PERSON>", "iceTea": "<PERSON><PERSON><PERSON>"}, "food": {"title": "Essen", "kebab": "<PERSON><PERSON><PERSON>", "sweets": "Dessert", "pizza": "Pizza", "burger": "Burger", "sushi": "<PERSON><PERSON>", "pasta": "<PERSON><PERSON><PERSON>", "fish": "<PERSON><PERSON>", "salad": "Salat", "fingerFood": "Fingerfood"}, "events": {"title": "Events", "party": "Party", "concert": "<PERSON><PERSON><PERSON>", "markets": "Märkte", "tastings": "Verkostungen", "workshops": "Workshops", "sports": "Sport", "theater": "Theater", "readings": "Lesungen", "cinema": "<PERSON><PERSON>"}, "nextButtonLabel": "<PERSON><PERSON>", "backButtonLabel": "Zurück"}, "socialOnboarding": {"title": "<PERSON><PERSON><PERSON> wir uns besser kennen", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> uns etwas über dich", "nextButtonLabel": "<PERSON><PERSON>", "skipButtonLabel": "Überspringen"}, "notificationPermission": {"title": "MITTEILUNGEN", "description": "Stelle deine Benachrichtigungen an, damit wir dich informieren können, wenn dein E<PERSON> fertig ist.", "buttons": {"decideLater": "Später entscheiden", "next": "<PERSON><PERSON>"}}, "search": {"noResults": "<PERSON><PERSON> wurden leider keine Ergebnisse gefunden", "inputLabel": "Suche...", "sortByRating": "<PERSON><PERSON>", "emptyCollection": "Wir müssen Si<PERSON>\nbesser kennen lernen"}, "discover": {"info": {"timeLabel": "Öffnungszeiten"}}, "restaurant": {"bookTableButton": "Tisch reservieren", "orderPayButton": "<PERSON><PERSON> bestellen", "booking": {"title(name)": "Hi${name}, es wäre toll, dir einen Tisch anbieten zu können.", "calendarButtonLabel": "<PERSON><PERSON>", "timeButtonLabel": "Buchen", "resultButtonLabel": "<PERSON><PERSON> klar", "errorButtonLabel": "Ok", "availableTimeLabel": "Verfügbare Zeiten", "estimatedTimeLabel": "Voraussichtliche Dauer deines Besuchs", "numberOfDinnersLabel": "Anzahl der Gäste", "resultTitle": "Du hast einen Tisch angefragt", "resultDescription": "Wir werden dir eine Bestätigung per Mail und Benachrichtigung schicken."}, "info": {"scheduleTitle": "Öffnungszeiten"}, "menu": {"empty": "<PERSON><PERSON> verfügbar", "ratingFilter": "4+", "budgetFilter": "Budget", "budgetFilterHint": "9,99", "extras": "Extras", "sauces": "Saucen"}, "emptyDietPreferences": "<PERSON><PERSON>aben zur Ernährungsform oder Unverträglichkeiten", "deals": {"title": "Sonderangebote", "empty": "<PERSON><PERSON>angebote verfügbar"}}, "profile": {"hint": "Dieses Profil wurde gelö<PERSON>t.", "tabs": {"post": "Beiträge", "settings": "Einstellungen"}, "followers": "Follower", "following": "<PERSON> folgst", "subscriptions": "<PERSON><PERSON><PERSON>", "settings": {"general": "Allgemeine Einstellungen", "help": "<PERSON><PERSON><PERSON>", "feedback": "<PERSON><PERSON><PERSON>", "orders": "Bestellungen", "logout": "Abmelden"}}, "generalSettings": {"profile": "Profil", "password": "Passwort ändern", "preferences": "Präferenzen", "payment": "Zahlungsmethode"}, "changePasswordSettings": {"title": "Passwort ändern", "newPassword": "Neues Passwort", "confirmNewPassword": "Neues Passwort bestätigen", "buttonLabel": "Passwort ändern"}, "changePreferences": {"buttonLabel": "Präferenzen speichern"}, "userProfileSettings": {"deleteAccount": "Konto löschen", "deleteAccountTitle": "B<PERSON> du sicher, dass du dein Konto löschen möchtest?", "deleteAccountDescription": "<PERSON> verlierst den Zugang zu allen exklusiven Rabatten von <PERSON>z und dein Profil und deine Beiträge werden endgültig gelöscht.", "buttonCancel": "Nicht löschen", "buttonOk": "<PERSON>ch bin sicher"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "messageLabel": "Nachricht", "design": "Design", "userFriendliness": "Benutzerfreundlichkeit", "paymentFunction": "Zahlungsfunktion", "orderFunction": "Bestellfunktion", "community": "Community", "couponsDiscounts": "Gutscheine & Rabatte", "recommendations": "Empfehlungen", "pushNotifications": "Push-Benachrichtigungen", "speed": "Geschwindigkeit", "otherFeatures": "Andere Funktionen", "successMessage": "Vielen Dank für dein Feedback!."}, "faq": {"title": "Häufig gestellte Fragen", "contactUs": "Kontaktiere uns", "empty": "<PERSON><PERSON> gefunden"}, "contact": {"title": "Kontakt-Formular", "messageLabel": "Nachricht", "registration": "Registrierung", "payment": "Zahlungsfunktion", "subscription": "Abo", "report": "<PERSON><PERSON> melden", "other": "Sonstiges", "successMessage": "Vielen Dank für deine Nachricht! Wir werden uns so schnell wie möglich bei dir melden."}, "subscription": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>hle eine Stufe aus", "levels": {"test": "Test", "member": "Member", "silver": "<PERSON><PERSON><PERSON>", "gold": "Gold"}, "levelsDescription": {"test": "Probiere Mutualz aus und nutze alle unsere Gutscheine kostenlos.", "member": "<PERSON> zahlst 5% weniger bei jeder Rechnung.", "silver": "Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Ra<PERSON> von 10% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde).", "gold": "Diese Stufe kannst du nur erreichen, indem du eine bestimmte Anzahl von Punkten in einem Restaurant sammelst. Diese Stufe gewährt dir einen Ra<PERSON> von 15% auf die gesamte Rechnung (nur in den Restaurants, in denen die Stufe erreicht wurde)."}, "buttons": {"upgrade": "Jetzt upgraden für 9,99€ monatlich", "recommend": "Empfehle uns einem Freund, um ein kostenloses Upgragde für 1 Monat zu erhalten", "cancel": "Abbrechen", "reactivate": "Reaktivieren"}, "info(date)": "Dein Abo läuft am ${date} ab", "discount": {"withPercent": {"one": "${percent}% für 1 Tag", "other": "${percent}% für $n Tage"}}, "daysRemaining": {"one": "Noch 1 Tag", "other": "Noch $n Tage"}, "discountOnTotalBill": "<PERSON><PERSON><PERSON> auf die\nGesamtrechnung*"}, "comments": {"title": "Kommentare", "inputLabel": "Schreibe einen Kommentar", "empty": "<PERSON><PERSON> ve<PERSON>"}, "orderProcess": {"title": "Speisekarte", "empty": "<PERSON><PERSON> verfügbar", "orderButton": "Bestellen/Bezahlen", "orderSuccess": "Bestellung erfolgreich. Guten Appetit!", "paymentSuccess": "Zahlung erfolgreich abgeschlossen"}, "cart": {"empty": "<PERSON>ine Artikel im Warenkorb", "total": "Gesamt:", "dialogNotes": {"title": "Hast du besondere Wünsche?", "hint": "Notiz"}, "dialogRemove": {"title": "Gericht aus dem Warenkorb entfernen", "description": "Bist du sicher, dass du dieses Gericht aus dem Warenkorb entfernen möchten?", "buttonCancel": "Abbrechen", "buttonConfirm": "<PERSON>ch bin sicher"}, "dialogSpecialRemove": {"title": "Angebot aus dem Warenkorb entfernen", "description": "Bist du sicher, dass du dieses Angebot aus dem Warenkorb entfernen möchtest?", "buttonCancel": "Abbrechen", "buttonConfirm": "<PERSON>ch bin sicher"}, "dialogConfirm": {"title": "Bist du sicher, dass du alles gefunden hast?", "description": "<PERSON>n hier bestellen", "buttonConfirm": "<PERSON><PERSON>, bin ich"}, "dialogShare": {"description": "Empfehle uns einem Freund, um eine 1 monatige Mitgliedschaft kostenlos zu erhalten und noch mehr zu sparen", "buttonShare": "Teilen", "buttonCancel": "Später teilen"}, "createOrderSuccess": "Bestellung erfolgreich erstellt. Guten Appetit!"}, "invoice": {"title": "Danke für deinen Besuch", "dishes": "<PERSON><PERSON><PERSON><PERSON>", "coupons": "Gutscheine:", "couponSelection": "Gutschein-Auswahl", "doNotUseCoupon": "<PERSON><PERSON> Gutschein", "proceedToPayment": "<PERSON><PERSON>"}, "paymentMethodSettings": {"title": "Zahlungsmethoden", "card": "<PERSON><PERSON>", "applePay": "Apple Pay", "googlePay": "Google Pay"}, "addPost": {"title": "Teile deine Erfahrungen mit der Community", "description": "<PERSON><PERSON><PERSON> einen Beitrag über deine besten Momente", "uploadImage": "Bild hochladen", "restaurantHint": "W<PERSON>hle ein Restaurant aus", "descriptionHint": "Füge hier eine Beschreibung hinzu", "attachImage": "Bild an<PERSON>ä<PERSON>n", "tagYourFriends": "Tagge deine Freunde"}, "payment": {"message": "Danke für deinen Besuch!", "buttonLabel": "<PERSON><PERSON> sammeln", "success": "Zahlung erfolgreich abgeschlossen"}, "orders": {"title": "Bestellungen", "empty": "<PERSON><PERSON>", "total": "Gesamt", "memberDiscount": "Member <PERSON><PERSON><PERSON>", "couponDiscount": "<PERSON>uts<PERSON><PERSON>", "totalWithMutualz": "Gesamt mit Mutualz", "summary": "Bestellübersicht", "sendInvoice": "Rechnung senden", "invoiceSent": "Rechnung wurde erfolgreich versendet", "sendingInvoice": "Rechnung wird gesendet..."}, "progressReward": {"unlockCoupons": "Verwende, um Gutscheine freizuschalten"}, "review": {"rating": "Bewertung", "ratingItems": {"service": "Service", "cleanliness": "Sauberkeit", "waitingTime": "Wartezeit", "foodQuality": "Geschmack", "athmosphere": "Atmosphäre"}, "commentHint": "Schreibe einen Kommentar für den Gastronomen", "verifyDietary": "Bestätige die vom Gastronomen gemachten Angaben", "dietPreferences": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "uploadImage": "Bild hochladen", "descriptionHint": "Beschreibung", "rateTitle": "Bewerte jetzt, um zusätzliche Punkte zu erhalten", "points": "Punkte", "rate": "Bewerten", "rateDescription": "Bewerte deinen Aufenthalt und verdiene 5 zusätzliche Punkte", "rateAndPost": "Bewerten & Posten", "rateAndPostDescription": "Bewerte und erstelle einen Post über deine Erfahrung und verdiene 10 zusätzliche Punkte", "skip": "Später bewerten"}}, "bnb": {"discover": "Entdecken", "feed": "Feed", "search": "<PERSON><PERSON>", "profile": "Profil"}, "filters": {"foodTypeTitle": "Kulinarische Ausrichtung", "dietPreferencesTitle": "Ernährungsform", "foodTitle": "Essenszeiten", "sortTitle": "<PERSON><PERSON><PERSON><PERSON>", "foodType": {"american": "Amerikanisch", "arabic": "Arabisch", "asian": "Asiatisch", "bakery": "Bä<PERSON><PERSON>", "burger": "Burger", "chinese": "<PERSON><PERSON><PERSON>", "curry": "<PERSON>", "german": "De<PERSON>ch", "doner": "<PERSON><PERSON><PERSON>", "iceCream": "<PERSON><PERSON>", "espresso": "Espresso", "falafel": "Falafel", "fish": "<PERSON><PERSON>", "breakfast": "Frühstück", "drinks": "Getränke", "chicken": "<PERSON><PERSON><PERSON><PERSON>", "hotdog": "Hotdog", "indian": "Indisch", "italian": "Italienisch", "japanese": "Japanisch", "coffee": "<PERSON><PERSON><PERSON>", "cake": "<PERSON><PERSON>", "seafood": "Meeresfrüchte", "mexican": "Mexikanisch", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "desserts": "Desserts", "noodles": "<PERSON><PERSON><PERSON>", "austrian": "Österreichisch", "pakistani": "Pakistani", "pasta": "Pasta", "persian": "<PERSON><PERSON><PERSON>", "pizza": "Pizza", "pokeBowl": "Poke Bowl", "ramen": "<PERSON><PERSON>", "frenchFries": "Pommes Frites", "salads": "Salate", "sandwiches": "Sandwiches", "snacks": "Snacks", "steaks": "Steaks", "soups": "<PERSON><PERSON><PERSON>", "sushi": "<PERSON><PERSON>", "syrian": "Syrisch", "thai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turkish": "Türkisch", "vegan": "Vegan", "vegetarian": "Vegetarisch", "vietnamese": "Vietnamesisch", "wraps": "Wraps", "bar": "Bar", "bowls": "Bowls", "cafe": "Café", "cocktails": "Cocktails", "greek": "Griechisch", "kebab": "Kebab", "restaurant": "Restaurant", "spanish": "Spanisch"}, "dietPreferences": {"kosher": "<PERSON><PERSON>", "halal": "<PERSON><PERSON>", "vegetarian": "Vegetarisch", "vegan": "Vegan", "glutenFree": "Glutenfrei", "lactoseFree": "Laktosef<PERSON>i", "nutsFree": "Nussfrei"}, "food": {"breakfast": "Frühstück", "brunch": "Brunch", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON><PERSON>", "snack": "Snack"}, "sort": {"rating": "<PERSON><PERSON>"}}, "tools": {"imagePicker": {"title": "Bild auswählen"}}, "custom": {"pointProgress": {"silver": "<PERSON><PERSON><PERSON>", "gold": "Gold", "test": "Test", "member": "Member", "points(value, opposite)": "${value}/${opposite} Punkte", "pointsString(value)": "${value} Punkte"}, "isReached": "<PERSON><PERSON><PERSON>t", "youMadeIt": "Du hast es geschafft"}, "empty": {"image": "<PERSON><PERSON>"}, "coupons": {"available": "Verfügbare Gutscheine", "activatable": "Aktivierbare Gutscheine", "noCoupons": "No available coupons", "appliable": "<PERSON><PERSON><PERSON><PERSON>", "canBeReactivated": "<PERSON>nn reaktiviert werden", "nonAppliable": "<PERSON>cht zu<PERSON>nd"}, "coupon": {"title": "Mutualz", "off": "<PERSON><PERSON><PERSON>", "percentage(value)": "${value}%", "separator": "für"}, "cities": {"berlin": "Berlin", "oldenburg": "Oldenburg", "osnaburg": "Osnabrück"}, "specials": {"title": "Sonderangebote", "single": "Sonderangebot", "availableTitle": "Sonderangebot erh<PERSON><PERSON>lich", "availableAt": "Genießen um"}, "genders": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "diverse": "Divers"}, "restaurantInfoTab": {"deals": "<PERSON><PERSON><PERSON>", "menu": "Speisekarte", "feed": "Feed"}, "scanner": {"label": "<PERSON>anne den QR-Code um zu bestellen", "errorDifferentRestaurant": "Du scannst einen QR-Code von einem anderen Restaurant", "errorRestaurantClosed(name)": "${name} ist geschlossen. Versuche es während der Öffnungszeiten erneut."}, "postCard": {"likes": "<PERSON>s", "comments": "Kommentare"}, "weekdays": {"monday": "Montag", "tuesday": "Dienstag", "wednesday": "Mittwoch", "thursday": "Don<PERSON><PERSON>", "friday": "Freitag", "saturday": "Samstag", "sunday": "Sonntag"}, "paymentInfo": {"total": "Gesamt", "discount": "<PERSON><PERSON><PERSON>", "totalWithMutualz": "Gesamt mit Mutualz"}, "loyalty": {"test": "Test", "member": "Member", "silver": "<PERSON><PERSON><PERSON>", "gold": "Gold"}, "earnWidget": {"label": "<PERSON><PERSON> sammeln", "points": "Punkte"}, "reportUserPost": {"title": "<PERSON><PERSON><PERSON>lden", "hint": "<PERSON><PERSON> den Grund für die Meldung an", "items": {"nudity": "Nacktheit oder Sexualisierung", "violence": "Blut oder Gewalt", "suicide": "<PERSON><PERSON><PERSON>, Selbstverletzung oder Essstörungen", "hate": "<PERSON><PERSON> oder <PERSON>", "ads": "Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors"}}, "blockUserPost": {"title": "<PERSON><PERSON><PERSON>lden", "hint": "<PERSON><PERSON> den Grund für die Meldung an", "items": {"nudity": "Nacktheit oder Sexualisierung", "violence": "Blut oder Gewalt", "suicide": "<PERSON><PERSON><PERSON>, Selbstverletzung oder Essstörungen", "hate": "<PERSON><PERSON> oder <PERSON>", "ads": "Verkauf oder Bewerbung von Produkten/Dienstleistungen außerhalb des Gastronomie-Sektors"}}, "blockUser": {"title": "<PERSON><PERSON><PERSON> melden", "hint": "<PERSON><PERSON> den Grund für die Meldung an", "items": {"insultsOrBullying": "Beleidigung oder Mobbing", "nudityOrSexualization": "Nacktheit oder Sexualisierung", "bloodOrViolence": "Blut oder Gewalt", "spam": "Spam", "falseIdentityOrIdentityStolen": "Falsche Identität/Identität geklaut", "accountCouldHaveBeenHacked": "Account könnte gehackt worden sein", "personUnder16": "Person ist unter 16 Jahren"}}, "reportUser": {"title": "<PERSON><PERSON><PERSON> melden", "hint": "<PERSON><PERSON> den Grund für die Meldung an", "items": {"insultsOrBullying": "Beleidigung oder Mobbing", "nudityOrSexualization": "Nacktheit oder Sexualisierung", "bloodOrViolence": "Blut oder Gewalt", "spam": "Spam", "falseIdentityOrIdentityStolen": "Falsche Identität/Identität geklaut", "accountCouldHaveBeenHacked": "Account könnte gehackt worden sein", "personUnder16": "Person ist unter 16 Jahren"}}, "strangerProfile": {"followers": "Follower", "following": "Fol<PERSON>", "friendsInCommon": "Gemeinsame Freunde"}, "tutorial": {"map": {"qrCode": "Beginne deine Bestellung. Hier kannst du den QR-Code scannen, um deine Bestellung aufzugeben."}, "filter": {"info": "Durchs Filtern verändert sich das Ergebnis auf der Karte und in der Listenansicht. Schiebe die Filter für die Listenansicht nach oben."}, "feed": {"addPost": "<PERSON>er kannst du einen Beitrag posten.", "search": "Du möchtest Freund*innen oder Familie folgen? Hier kannst du die Person suchen."}, "restaurant": {"rating": "<PERSON>licke auf die Sterne oder die Uhr, um weitere Infos zu bekommen", "favorite": "Hier kannst du die Lokalität als Favorit markieren", "dietPreferences": "Ernährungsformen und Unverträglichkeiten. Alle grauen Icons sind Angaben vom Gastronom.", "dietPreferencesConfirmed": "Wenn es rot ist, wurde die Angabe von den Usern bestätigt.", "deals": "Hier siehst du alle Deals und Coupons", "menu": "<PERSON><PERSON> kannst du vorab schon stöbern"}, "profile": {"subscriptions": "Hier sind alle Stufen, die du im Rahmen des Loyalty-Programms erreichen kannst. Zusätzlich siehst du, wie viele Punkte du insgesamt hast.", "orders": "Hier kannst du deine Rechnungen einsehen und herunterladen", "dietPreferences": "Hier kannst du deine Ernährungspräferenzen anpassen"}, "orders": {"tableNumber": "Die Tischnummer findest du bei dem QR-Code auf dem Tisch", "cart": "Das ist dein Ware<PERSON>, hier siehst du alle Produkte, die du hinzugefügt hast.", "cartInside": "So sieht dein Warenkorb von innen aus. Du kannst hier deine Bestellungen anpassen", "orderAndPayNow": "<PERSON>er bestellst und bezahlst du sofort, das macht am meisten Sinn, wenn du z. B. schnell wieder los musst", "orderAndPayLater": "Hier kannst du bestellen und später bezahlen, wenn du die Lokalität verlässt", "usedCoupons": "<PERSON><PERSON>upon<PERSON> hast du schon verwendet und kannst sie mit Punkten wieder freischalten", "loyaltyPoints": "Diese Punk<PERSON> kannst du bei dieser Lokalität verwenden, um Coupons wieder freizuschalten", "congratulations": "Herzlichen Glückwunsch! Du hast deine ersten Punkte im Loyalty-Programm gesammelt!"}, "loyalty": {"fourLevels": "Es gibt vier Stufen im Loyalty-Programm. Du startest immer in der ersten und hast dann einen Monat Zeit, um bis in die Gold-Stufe aufzusteigen.", "test": "Du Sammelst Punkte mit jedem Einkauf und kommst so auf die nächste Stufe.\n\nDas Beste: Jeder Einkauf zählt egal bei welchem MUTUALZ-Partner du Einkaufst!\n\nWichtig: Am Ende des Monats wirst du wieder auf diese Stufe und 0 Punkte zurückgesetzt!*", "member": "In dieser Stufe erhältst du auf jede Rechnung 5 % Rabatt bei allen Partnern.\n\nJe schneller du diese Stufe erreichst desto mehr Zeit hast die nächsten Stufen zu erklimmen!\n\nGeheimtipp: Mit dem Mutualz-Abo startest in dieser Stufe zum Beginn jeden Monats! Zeitvorteil heißt Preisvorteil!", "silver": "Diese Stufe belohnt die Zuverlässigen und Treuen!\n\nMit Silber sparst du 10 % auf die Gesamtrechnung in Restaurants in denen du Silber erreicht hast.\n\nDiese Stufe kannst du nur erreichen, indem du genügend Punkte in einem Restaurant sammelst und Stufen aufsteigst.*", "gold": "Herzlichen Glückwunsch eure Hoheit!\n\nWenn man sagt: \"Der Kunde ist König!\" Dann spricht man ausschließlich von dir!\n\nDu sparst 15% auf die Gesamtrechnung in dem Restaurant in dem du diese Stufe erreicht hast.", "subscription": "Schließe jetzt das MUTUALZ-Abo ab und erhalte sofort 5 % Raå<PERSON><PERSON> zusätzlich zu deinen Coupons und starte jeden Monat mit einem Zeitvorteil !!!", "buttons": {"later": "<PERSON><PERSON> später ...", "subscribe": "Abo abschließen", "next": "<PERSON><PERSON>"}, "terms": "*für mehr Informationen schau in unsere AGB's", "level": "Stuf<PERSON>", "oneCalendarMonth": "Ein Kalendermonat Zeit"}}}