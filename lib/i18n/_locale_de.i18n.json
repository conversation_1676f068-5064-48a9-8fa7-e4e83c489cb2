{"@@locale": "de", "language": "De<PERSON>ch", "title": "Mutualz", "titleDev": "<PERSON><PERSON>", "errors": {"fieldEmpty": "<PERSON><PERSON>ld sollte nicht leer sein", "emailInvalid": "Ungültige E-Mail-Adresse", "passwordInvalid": "Das Passwort sollte mindestens 8 und weniger als 60 <PERSON>eichen lang sein.", "userNameInvalid": "Der Benutzername sollte mindestens 2 und weniger als 60 Zeichen lang sein.", "passwordNotMatch": "Die Passwörter stimmen nicht überein", "otpEmpty": "O<PERSON> kann nicht leer sein", "unknown": "Unbekannter Fehler", "errorAuthConflict": "Authentifizierungskonflikt", "errorAuthTokenExpired": "Authentifizierungstoken abgelaufen", "errorSocialAuthFailed": "Soziale Authentifizierung fehlgeschlagen", "errorAuthOperationInProgress": "Authentifizierungsvorgang läuft", "errorAuthCancelled": "Authentifizierung abgebrochen", "privacyNotAccepted": "Datenschutzerklärung und Allgemeine Geschäftsbedingungen nicht akzeptiert", "baseTextLength": "Die Länge sollte zwischen 2 und 60 Zeichen liegen.", "phoneNumberInvalid": "Ungültige Telefonnummer", "doesNotContainNumber": "Das Passwort sollte mindestens eine Zahl enthalten", "doesNotContainSpecialCharacter": "Das Passwort sollte mindestens ein Sonderzeichen enthalten", "doesNotContainUppercase": "Das Passwort muss mindestens einen Großbuchstaben enthalten", "doesNotContainLowercase": "Das Passwort muss mindestens einen Kleinbuchstaben enthalten", "disabledLocationService": "Standortdienste sind deaktiviert. Bitte aktivieren Sie diese, um fortzufahren.", "locationPermissionDenied": "Standortberechtigungen sind verweigert. Bitte aktivieren Sie diese, um fortzufahren.", "locationPermissionPermanentlyDenied": "Standortberechtigungen sind dauerhaft verweigert. Wir können keine Berechtigungen anfordern.", "noRestaurantImage": "Kein Restaurantbild verfügbar", "emptyRating": "Alle Bewertungen sollten ausgefüllt werden", "defaultPaymentMethod": "Bitte wählen Sie eine Zahlungsmethode aus", "restartError": "Ein Fehler ist aufgetreten. Bitte starten Sie die App neu."}, "buttons": {"save": "Speichern", "send": "Senden", "ok": "OK", "add": "Hinzufügen", "edit": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON>", "pay": "<PERSON><PERSON><PERSON><PERSON>", "orderAndPay": "Bestellen & Bezahlen", "seeAll": "Alle anzeigen", "back": "Zurück", "savePoints": "Noch mehr Punkte sammeln", "tryAgain": "Nochmals versuchen", "tryAgainLater": "Später erneut versuchen", "skip": "Überspringen"}, "dialogs": {"error": {"title": "<PERSON><PERSON>"}, "success": {"title": "Ausgezeichnet!", "changes": "Ihre Änderungen wurden gespeichert"}, "buttonLabel": "OK", "options": {"deletePost": "Beitrag löschen", "report": "Melden", "unfollow": "Nicht mehr folgen", "block": "Blockieren", "follow": "Folgen"}, "numberTable": {"title": "Geben Sie Ihre Tischnummer ein.", "hint": "Tischnummer"}}, "screens": {"splash": {"title": "Mutualz"}, "auth": {"welcome": "Willkommen\nbei Mutualz", "tabs": {"login": "Einloggen", "registration": "Registrierung"}, "login": {"email": "E-Mail", "password": "Passwort", "buttonLabel": "Einloggen", "forgotPassword": "Passwort vergessen?"}, "registration": {"labels": {"gender": "Geschlecht", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "username": "Benutzername*", "birthDate": "Geburtsdatum*", "email": "E-Mail*", "phone": "Rufnummer", "password": "Passwort*", "confirmPassword": "Passwort bestätigen*"}, "terms": {"agreement": "Ich stimme zu ", "conditions": "Bedingungen", "separator": " und ", "privacy": "Datenschutzbestimmungen"}, "buttonLabel": "Registrieren"}, "opt": {"title": "OTP-Code eingeben", "description": "Wir haben den OTP-Code an Ihre E-Mail geschickt.", "buttonLabel": "OK", "resendLabel": "Zugangscode erneut senden", "resendCodeAvailable": "Code zum erneuten Senden verfügbar"}}, "forgot": {"title": "Passwort vergessen", "description": "Bitte geben Sie Ihre E-Mail-Adresse ein, mit der Si<PERSON> sich registriert haben. Ein Link zum Ändern Ihres Passworts wird Ihnen umgehend zugesandt.", "emailLabel": "E-Mail", "buttonLabel": "Senden", "backButtonLabel": "Zurück", "successDescription": "Sie können Ihre E-Mail überprüfen, um den Link zur Wiederherstellung des Passworts zu finden"}, "reset": {"title": "Ändern Sie Ihr Passwort", "description": "<PERSON><PERSON>gen Si<PERSON> hier Ihr neues Passwort ein", "passwordLabel": "Passwort*", "repeatPasswordLabel": "Passwort wiederholen*", "buttonLabel": "Passwort ändern", "successMessage": "Herzlichen Glückwunsch, Ihr Passwort wurde geändert"}, "onboarding": {"lifestyle": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> uns von dir,", "description": "damit wir dir genau das bieten können, was zu deinem Leben passt", "job": {"title": "<PERSON><PERSON><PERSON>", "employee": "<PERSON><PERSON><PERSON><PERSON>", "student": "<PERSON><PERSON><PERSON><PERSON>", "collegeStudent": "Student", "selfEmployed": "Selbstständig", "housewife": "<PERSON><PERSON><PERSON>/Hausfrau", "retired": "<PERSON><PERSON><PERSON>"}, "allergies": {"title": "Unverträglichkeiten", "gluten": "Glutenfrei", "lactose": "Laktosef<PERSON>i", "nutAllergy": "Nussallergie"}, "diet": {"title": "Diätetische Präferenzen", "halal": "100% Halal", "kosher": "<PERSON><PERSON>", "vegan": "Vegan", "vegetarian": "Vegetarisch"}}, "taste": {"title": "<PERSON><PERSON><PERSON> uns, was dir gef<PERSON><PERSON>t", "description": "Wähle deine Präferenzen aus"}, "drinks": {"title": "Getränke", "tea": "<PERSON><PERSON>", "softDrinks": "Erfrischungsgetränke", "coffee": "<PERSON><PERSON><PERSON>", "beer": "<PERSON><PERSON>", "cocktails": "Cocktails", "juice": "Saft", "smoothie": "<PERSON><PERSON><PERSON><PERSON>", "wine": "<PERSON><PERSON>", "iceTea": "<PERSON><PERSON><PERSON>"}, "food": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kebab": "Kebab", "sweets": "Süßigkeiten", "pizza": "Pizza", "burger": "Burger", "sushi": "<PERSON><PERSON>", "pasta": "<PERSON><PERSON><PERSON>", "fish": "<PERSON><PERSON>", "salad": "Salat", "fingerFood": "Fingerfood"}, "events": {"title": "Veranstaltungen", "party": "Party", "concert": "<PERSON><PERSON><PERSON>", "markets": "Märkte", "tastings": "Verkostungen", "workshops": "Workshops", "sports": "Sport", "theater": "Theater", "readings": "Lesungen", "cinema": "<PERSON><PERSON>"}, "nextButtonLabel": "<PERSON><PERSON>", "backButtonLabel": "Zurück"}, "socialOnboarding": {"title": "<PERSON><PERSON>n wir uns kennen", "description": "<PERSON><PERSON>n Si<PERSON> uns Informationen über sich mit", "nextButtonLabel": "<PERSON><PERSON>", "skipButtonLabel": "Überspringen"}, "search": {"noResults": "<PERSON><PERSON> wurden leider keine Ergebnisse gefunden", "inputLabel": "Suche...", "sortByRating": "Sort by rating"}, "discover": {"info": {"timeLabel": "Zeit"}}, "restaurant": {"bookTableButton": "Einen Tisch reservieren", "orderPayButton": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON> von", "booking": {"title(name)": "Hi${name}, es wäre toll, <PERSON>hnen einen Tisch anbieten zu können.", "calendarButtonLabel": "<PERSON><PERSON>", "timeButtonLabel": "Buchen Sie", "resultButtonLabel": "Oberfläche", "errorButtonLabel": "Ok", "availableTimeLabel": "Verfügbare Uhrzeiten", "estimatedTimeLabel": "Voraussichtliche Dauer Ihres Besuchs", "numberOfDinnersLabel": "<PERSON><PERSON><PERSON> der Abendessen", "resultTitle": "Sie haben einen Tisch im", "resultDescription": "Wir werden Ihnen eine Bestätigung per Mail und Benachrichtigung schicken."}, "info": {"scheduleTitle": "Öffnungszeiten"}, "menu": {"empty": "<PERSON><PERSON> ve<PERSON>", "ratingFilter": "4+", "budgetFilter": "Budget", "budgetFilterHint": "15,99", "toppings": "Beläge", "sauces": "Saucen"}, "emptyDietPreferences": "Keine diätetischen Präferenzen", "deals": {"title": "Sonderangebote", "empty": "<PERSON><PERSON>angebote verfügbar"}}, "profile": {"tabs": {"post": "Beiträge", "settings": "Einstellungen"}, "followers": "Follower", "following": "Following", "subscriptions": "Abonnements", "settings": {"general": "Allgemeine Einstellungen", "help": "<PERSON><PERSON><PERSON>", "feedback": "Rückmeldung", "orders": "<PERSON><PERSON><PERSON>", "logout": "Abmelden"}}, "generalSettings": {"profile": "Benutzerprofil", "password": "Passwort ändern", "preferences": "Präferenzen", "payment": "Zahlungsmethode"}, "changePasswordSettings": {"title": "Passwort ändern", "newPassword": "Neues Passwort", "confirmNewPassword": "Neues Passwort bestätigen", "buttonLabel": "Passwort ändern"}, "changePreferences": {"buttonLabel": "Präferenzen speichern"}, "userProfileSettings": {"deleteAccount": "Konto löschen", "deleteAccountTitle": "Sind <PERSON> sicher, dass Si<PERSON> Ihr Konto löschen möchten?", "deleteAccountDescription": "Sie verlieren den Zugang zu allen exklusiven Rabatten von <PERSON>z und Ihr Profil und Ihre Beiträge werden endgültig gelöscht.", "buttonCancel": "Ich habe meine Meinung geändert", "buttonOk": "<PERSON>ch bin sicher, dass"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "messageLabel": "Nachricht", "design": "Design", "userFriendliness": "Benutzerfreundlichkeit", "paymentFunction": "Zahlungsfunktion", "orderFunction": "Bestellfunktion", "community": "Gemeinschaft", "couponsDiscounts": "Gutscheine & Rabatte", "recommendations": "Empfehlungen", "pushNotifications": "Push-Benachrichtigungen", "speed": "Geschwindigkeit", "otherFeatures": "Andere Funktionen", "successMessage": "Vielen Dank für Ihr Feedback! Unser Team ist bestrebt, das Beste für Sie zu erreichen."}, "faq": {"title": "Häufig gestellte Fragen", "contactUs": "Kontaktiere uns", "empty": "<PERSON><PERSON> gefunden"}, "contact": {"title": "Kontakt-Formular", "messageLabel": "Schreiben Sie Ihre Nachricht", "registration": "Verfahren der Registrierung", "payment": "Zahlungsfunktion", "subscription": "Abonnement", "report": "<PERSON><PERSON> melden", "other": "Andere Kategorien", "successMessage": "Vielen Dank für Ihre Nachricht! Wir werden uns so schnell wie möglich bei Ihnen melden."}, "subscription": {"title": "Abonnement", "description": "Wählen Sie Ihr Abonnement", "levels": {"test": "Test", "member": "<PERSON><PERSON><PERSON><PERSON>", "silver": "<PERSON><PERSON><PERSON>", "gold": "Gold"}, "levelsDescription": {"test": "Probieren Sie MUTUALZ aus und nutzen Sie alle unsere Gutscheine kostenlos.", "member": "Sie zahlen 5% weniger auf Ihre gesamte Rechnung.", "silver": "Dieses Abonnement können Si<PERSON> nur erreichen, indem Sie eine bestimmte Anzahl von Punkten in einem Restaurant sammeln. Dieses Abonnement gewährt Ihnen einen Rabatt von 10% auf Ihre gesamte Rechnung (nur in den Restaurants, in denen das Abonnement erreicht wurde).", "gold": "Dieses Abonnement können Si<PERSON> nur erreichen, indem Sie eine bestimmte Anzahl von Punkten in einem Restaurant sammeln. Dieses Abonnement gewährt Ihnen einen Rabatt von 15% auf Ihre gesamte Rechnung (nur in den Restaurants, in denen das Abonnement erreicht wurde)."}, "buttons": {"upgrade": "Jetzt upgraden für 9,99€ monatlich", "recommend": "Empfehlen Sie uns einem Freund, um 1 Monat kostenlos zu erhalten", "cancel": "Abbrechen", "reactivate": "Reaktivieren"}, "info(date)": "Ihr Abonnement läuft am ${date} ab"}, "comments": {"title": "Kommentare", "inputLabel": "Schreiben Sie einen Kommentar", "empty": "<PERSON><PERSON> ve<PERSON>"}, "orderProcess": {"title": "<PERSON><PERSON>", "empty": "<PERSON><PERSON> ve<PERSON>", "orderButton": "Bestellen/Bezahlen", "orderSuccess": "Bestellung erfolgreich erstellt. Guten Appetit!", "paymentSuccess": "Zahlung erfolgreich abgeschlossen"}, "cart": {"empty": "<PERSON>ine Artikel im Warenkorb", "total": "Gesamt:", "dialogNotes": {"title": "Haben Sie besondere Anforderungen?", "hint": "Besondere Anfrage"}, "dialogRemove": {"title": "Gericht aus dem Warenkorb entfernen", "description": "Sind <PERSON> sicher, dass Sie dieses Gericht aus dem Warenkorb entfernen möchten?", "buttonCancel": "Abbrechen", "buttonConfirm": "<PERSON>ch bin sicher"}, "dialogSpecialRemove": {"title": "Spezialität aus dem Warenkorb entfernen", "description": "Sind <PERSON> sic<PERSON>, dass Sie dieses Spezial aus dem Warenkorb entfernen möchten?", "buttonCancel": "Abbrechen", "buttonConfirm": "<PERSON>ch bin sicher"}, "dialogConfirm": {"title": "Sind Si<PERSON> sicher, dass Sie alles gefunden haben?", "description": "<PERSON>n hier bestellen", "buttonConfirm": "Auswahl bestätigen"}, "dialogShare": {"description": "Empfehlen Sie uns Ihren Freunden, um 1 Monat Mitgliedschaft kostenlos zu erhalten und mehr Geld auf Ihrer Rechnung zu sparen", "buttonShare": "Teilen", "buttonCancel": "Später teilen"}, "createOrderSuccess": "Bestellung erfolgreich erstellt. Guten Appetit!"}, "invoice": {"title": "Danke\nfür Ihren Besuch", "dishes": "<PERSON><PERSON><PERSON><PERSON>", "coupons": "Gutscheine:", "couponSelection": "Gutschein-Auswahl", "doNotUseCoupon": "Gutschein nicht verwenden", "proceedToPayment": "Zur Zahlung übergehen"}, "paymentMethodSettings": {"title": "Zahlungsmethoden", "card": "<PERSON><PERSON>", "applePay": "Apple Pay", "googlePay": "Google Pay"}, "addPost": {"title": "<PERSON><PERSON>n Sie Ihre Erfahrung mit allen", "description": "<PERSON><PERSON><PERSON>n Si<PERSON> einen Beitrag über Ihre besten Momente", "uploadImage": "Bild hochladen", "restaurantHint": "Wählen Sie ein Restaurant aus", "descriptionHint": "Fügen Sie hier eine Beschreibung hinzu", "attachImage": "Bild an<PERSON>ä<PERSON>n", "tagYourFriends": "Taggen Sie Ihre Freunde"}, "payment": {"message": "Danke für Ihren Besuch!", "buttonLabel": "<PERSON><PERSON> sammeln", "success": "Zahlung erfolgreich abgeschlossen"}, "orders": {"title": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON> frü<PERSON>n Bestellungen", "total": "Gesamt", "memberDiscount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couponDiscount": "Gutsche Rabat", "totalWithMutualz": "Gesamt mit Mutualz", "summary": "Bestellübersicht"}, "progressReward": {"unlockCoupons": "Verwenden Si<PERSON> sie, um\nGutscheine freizuschalten"}, "review": {"rating": "Bewertung", "ratingItems": {"service": "Service", "cleanliness": "Sauberkeit", "waitingTime": "Wartezeit", "foodQuality": "Essenqualität", "athmosphere": "Atmosphäre"}, "commentHint": "Schreiben Sie hier Ihren Kommentar", "verifyDietary": "Überprüfen Sie die diätetischen Angaben", "dietPreferences": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "uploadImage": "Bild hochladen", "descriptionHint": "Fügen Sie hier eine Beschreibung hinzu", "rateTitle": "Bewerten Sie jetzt, um zusätzliche Punkte zu erhalten", "points": "Punkte", "rate": "Bewerten", "rateDescription": "Bewerten Sie den Service und verdienen Sie 5 zusätzliche Punkte", "rateAndPost": "Bewerten & Posten", "rateAndPostDescription": "Bewerten Sie den Service und verdienen Sie 10 zusätzliche Punkte", "skip": "Später bewerten"}}, "bnb": {"discover": "Entdecken Sie", "feed": "Futtermittel", "search": "<PERSON><PERSON>", "profile": "Profil"}, "filters": {"foodTypeTitle": "<PERSON><PERSON>", "dietPreferencesTitle": "Diätetische Präferenzen", "foodTitle": "Essen", "sortTitle": "<PERSON><PERSON><PERSON><PERSON>", "foodType": {"american": "Amerikanisch", "arabic": "Arabisch", "asian": "Asiatisch", "bakery": "Bä<PERSON><PERSON>", "burger": "Burger", "chinese": "<PERSON><PERSON><PERSON>", "curry": "<PERSON>", "german": "De<PERSON>ch", "doner": "<PERSON><PERSON><PERSON>", "iceCream": "<PERSON><PERSON>", "espresso": "Espresso", "falafel": "Falafel", "fish": "<PERSON><PERSON>", "breakfast": "Frühstück", "drinks": "Getränke", "chicken": "<PERSON><PERSON><PERSON><PERSON>", "hotdog": "Hotdog", "indian": "Indisch", "italian": "Italienisch", "japanese": "Japanisch", "coffee": "<PERSON><PERSON><PERSON>", "cake": "<PERSON><PERSON>", "seafood": "Meeresfrüchte", "mexican": "Mexikanisch", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "desserts": "Desserts", "noodles": "<PERSON><PERSON><PERSON>", "austrian": "Österreichisch", "pakistani": "Pakistani", "pasta": "Pasta", "persian": "<PERSON><PERSON><PERSON>", "pizza": "Pizza", "pokeBowl": "Poke Bowl", "ramen": "<PERSON><PERSON>", "frenchFries": "Pommes Frites", "salads": "Salate", "sandwiches": "Sandwiches", "snacks": "Snacks", "steaks": "Steaks", "soups": "<PERSON><PERSON><PERSON>", "sushi": "<PERSON><PERSON>", "syrian": "Syrisch", "thai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turkish": "Türkisch", "vegan": "Vegan", "vegetarian": "Vegetarisch", "vietnamese": "Vietnamesisch", "wraps": "Wraps", "bar": "Bar", "bowls": "Schüsseln", "cafe": "Café", "cocktails": "Cocktails", "greek": "Griechisch", "kebab": "Kebab", "restaurant": "Restaurant", "spanish": "Spanisch"}, "dietPreferences": {"kosher": "<PERSON><PERSON>", "halal": "<PERSON><PERSON>", "vegetarian": "Vegetarisch", "vegan": "Vegan", "glutenFree": "Glutenfrei", "lactoseFree": "Laktosef<PERSON>i", "nutsFree": "Nussfrei"}, "food": {"breakfast": "Frühstück", "brunch": "Brunch", "lunch": "<PERSON><PERSON><PERSON><PERSON>", "dinner": "<PERSON><PERSON><PERSON>", "snack": "Snack"}, "sort": {"rating": "<PERSON><PERSON>"}}, "tools": {"imagePicker": {"title": "Bild auswählen"}}, "custom": {"pointProgress": {"silver": "<PERSON><PERSON><PERSON>", "gold": "Gold", "test": "Test", "member": "<PERSON><PERSON><PERSON><PERSON>", "points(value, opposite)": "${value}/${opposite} Punkte", "pointsString(value)": "${value} Punkte"}, "isReached": "<PERSON><PERSON><PERSON>t", "youMadeIt": "Du hast es geschafft"}, "empty": {"image": "<PERSON><PERSON>"}, "coupons": {"available": "Verfügbare Gutscheine", "activatable": "Aktivierbare Gutscheine"}, "coupon": {"title": "Mutualz", "off": "<PERSON><PERSON><PERSON>", "percentage(value)": "${value}%"}, "cities": {"berlin": "Berlin", "oldenburg": "Oldenburg", "osnaburg": "Osnabrück"}, "specials": {"title": "Sonderangebote", "single": "Sonderangebot", "availableTitle": "Sonderangebot verfügbar", "availableAt": "Verfügbar bei"}, "genders": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "diverse": "Vielfältig"}, "restaurantInfoTab": {"deals": "<PERSON><PERSON><PERSON>", "menu": "Speisekarte", "feed": "Feed"}, "scanner": {"label": "Scannen Sie den QR-Code,\\num auf das Restaurant zuzugreifen", "errorDifferentRestaurant": "Sie scannen einen QR-Code von einem anderen Restaurant"}, "postCard": {"likes": "<PERSON>s", "comments": "Kommentare"}, "weekdays": {"monday": "Montag", "tuesday": "Dienstag", "wednesday": "Mittwoch", "thursday": "Don<PERSON><PERSON>", "friday": "Freitag", "saturday": "Samstag", "sunday": "Sonntag"}, "paymentInfo": {"total": "Gesamt", "discount": "<PERSON><PERSON><PERSON>", "totalWithMutualz": "Gesamt mit Mutualz"}, "loyalty": {"test": "Test", "member": "<PERSON><PERSON><PERSON><PERSON>", "silver": "<PERSON><PERSON><PERSON>", "gold": "Gold"}, "earnWidget": {"label": "<PERSON><PERSON> sammeln", "points": "punkte"}, "reportUserPost": {"title": "<PERSON><PERSON><PERSON>lden", "hint": "Geben Sie den Grund für die Meldung an", "items": {"nudity": "Nacktheit oder Sexualisierung", "violence": "Blut oder Gewalt", "suicide": "<PERSON><PERSON><PERSON>, Selbstverletzung oder Essstörungen", "hate": "<PERSON><PERSON> oder <PERSON>", "ads": "Verkauf oder Bewerbung von Produkten/Dienstleistungen\naußerhalb des Gastronomie-Sektors"}}, "reportUser": {"title": "<PERSON><PERSON><PERSON> melden", "hint": "Bitte beschreiben Sie den Grund für Ihre Meldung", "items": {"insultsOrBullying": "Beleidigung oder Mobbing", "nudityOrSexualization": "Nacktheit oder Sexualisierung", "bloodOrViolence": "Blut oder Gewalt", "spam": "Spam", "falseIdentityOrIdentityStolen": "Falsche Identität/Identität geklaut", "accountCouldHaveBeenHacked": "Account könnte gehackt worden sein", "personUnder16": "Person ist unter 16 Jahren"}}, "strangerProfile": {"followers": "Followers", "following": "Following", "friendsInCommon": "friends in common"}}