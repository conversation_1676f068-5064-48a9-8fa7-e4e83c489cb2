{"@@locale": "en", "language": "English", "title": "Mutualz", "titleDev": "<PERSON><PERSON>", "errors": {"fieldEmpty": "This field shouldn't be empty", "emailInvalid": "Invalid email", "passwordInvalid": "Password should be at least 8 long and less than 60 characters", "userNameInvalid": "Username should be at least 2 long and less than 60 characters", "passwordNotMatch": "Passwords don't match", "otpEmpty": "OTP cannot be empty", "unknown": "Unknown error", "errorAuthConflict": "Authentication conflict", "errorAuthTokenExpired": "Authentication token expired", "errorSocialAuthFailed": "Social authentication failed", "errorAuthOperationInProgress": "Authentication operation in progress", "errorAuthCancelled": "Authentication cancelled", "privacyNotAccepted": "Privacy policy and Terms & Conditions not accepted", "baseTextLength": "Length should be at between 2 and 60 characters", "phoneNumberInvalid": "Invalid phone number", "doesNotContainNumber": "Password should contain at least one number", "doesNotContainSpecialCharacter": "Password should contain at least one special character", "doesNotContainUppercase": "Password should contain at least one uppercase letter", "doesNotContainLowercase": "Password should contain at least one lowercase letter", "disabledLocationService": "Location services are disabled, please enable it to continue", "locationPermissionDenied": "Location permissions are denied, please enable it to continue", "locationPermissionPermanentlyDenied": "Location permissions are permanently denied, we cannot request permissions", "noRestaurantImage": "No Restaurant Image", "emptyRating": "All ratings should be filled", "defaultPaymentMethod": "You must have at least one payment method", "restartError": "There is problem with the payment method. Please restart your app. it might help you."}, "buttons": {"save": "Save", "send": "Send", "ok": "Ok", "add": "Add", "edit": "Edit", "order": "Order", "pay": "Pay", "orderAndPay": "Order & Pay", "seeAll": "See all", "back": "Go Back", "savePoints": "<PERSON><PERSON> even more points", "tryAgain": "Try again", "tryAgainLater": "Try again later", "skip": "<PERSON><PERSON>"}, "dialogs": {"error": {"title": "Ups!!!"}, "success": {"title": "Excellent!", "changes": "Your changes have been saved"}, "buttonLabel": "Ok", "options": {"deletePost": "Delete Post", "report": "Report", "unfollow": "Unfollow", "block": "Block", "follow": "Follow"}, "numberTable": {"title": "Put in your table number.", "hint": "Tablenumber"}}, "screens": {"splash": {"title": "Mutualz"}, "auth": {"welcome": "Welcome\nto Mutualz", "tabs": {"login": "<PERSON><PERSON>", "registration": "Registration"}, "login": {"email": "Mail", "password": "Password", "buttonLabel": "<PERSON><PERSON>", "forgotPassword": "Forgot Password?"}, "registration": {"labels": {"gender": "Gender", "firstName": "First name", "lastName": "Last name", "username": "Username*", "birthDate": "Birth date*", "email": "Mail*", "phone": "Phone number", "password": "Password*", "confirmPassword": "Repeat password*"}, "terms": {"agreement": "I agree to ", "conditions": "Terms & Conditions", "separator": " and ", "privacy": "Privacy Policy"}, "buttonLabel": "Sign up"}, "opt": {"title": "Insert OTP code", "description": "We have sent the OTP code to your email.", "buttonLabel": "Ok", "resendLabel": "Resend access code", "resendCodeAvailable": "Resend code available"}}, "forgot": {"title": "Forgot Password", "description": "Please enter your email with which you're registered. A link for changing your password will be send immediately.", "emailLabel": "Mail", "buttonLabel": "Send", "backButtonLabel": "Go Back", "successDescription": "You can check your email to enter the password recovery link"}, "reset": {"title": "Change your password", "description": "Add your new password here", "passwordLabel": "Password*", "repeatPasswordLabel": "Repeat password*", "buttonLabel": "Change Password", "successMessage": "Congratulations, your password has been changed"}, "onboarding": {"personalInfo": {"title": "Voluntary information,", "description": "share some basic details with us"}, "lifestyle": {"title": "Tell us about you,", "description": "so we can offer you deals that fit your life", "job": {"title": "Occupation/Job", "employee": "Employee", "student": "Student", "collegeStudent": "College Student", "selfEmployed": "Self-employed", "housewife": "Househusband/housewife", "retired": "Retired"}, "allergies": {"title": "Intolerances/allergies", "gluten": "Gluten Free", "lactose": "Lactose Free", "nutAllergy": "Nut allergy"}, "diet": {"title": "Dietary preferences", "halal": "100% Halal", "kosher": "<PERSON><PERSON>", "vegan": "Vegan", "vegetarian": "Vegetarian"}}, "taste": {"title": "Show us what you like", "description": "Select your preferences"}, "drinks": {"title": "Drinks", "tea": "Tea", "softDrinks": "Soft drinks", "coffee": "Coffee", "beer": "Beer", "cocktails": "Cocktails", "juice": "Juice", "smoothie": "<PERSON><PERSON><PERSON><PERSON>", "wine": "Wine", "iceTea": "Ice Tea"}, "food": {"title": "Food", "kebab": "Kebab", "sweets": "Sweets", "pizza": "Pizza", "burger": "Burger", "sushi": "<PERSON><PERSON>", "pasta": "Pasta", "fish": "Fish", "salad": "Salad", "fingerFood": "Finger Food"}, "events": {"title": "Events", "party": "Party", "concert": "Concert", "markets": "Markets", "tastings": "Tastings", "workshops": "Workshops", "sports": "Sports", "theater": "Theater", "readings": "Readings", "cinema": "Cinema"}, "nextButtonLabel": "Next", "backButtonLabel": "Go back"}, "socialOnboarding": {"title": "Let's get acquainted", "description": "Share information about yourself with us", "nextButtonLabel": "Next", "skipButtonLabel": "<PERSON><PERSON>"}, "notificationPermission": {"title": "NOTIFICATIONS", "description": "Turn on your notifications so we can inform you when your food is ready.", "buttons": {"decideLater": "<PERSON>ide later", "next": "Continue"}}, "search": {"noResults": "Sorry, no results were found", "inputLabel": "Search...", "sortByRating": "Sort by rating", "emptyCollection": "We need to get to\nknow you better"}, "discover": {"info": {"timeLabel": "Opening Hours"}}, "restaurant": {"bookTableButton": "Book a table", "orderPayButton": "Order here", "booking": {"title(name)": "Hi${name}, it would be great to offer you a table.", "calendarButtonLabel": "Next", "timeButtonLabel": "Book", "resultButtonLabel": "Finish", "errorButtonLabel": "Ok", "availableTimeLabel": "Available times", "estimatedTimeLabel": "Estimated time of your visit", "numberOfDinnersLabel": "Number of diners", "resultTitle": "You reserved a\ntable at the", "resultDescription": "We're gonna send you a confirmation via Mail and Notification."}, "info": {"scheduleTitle": "Opening Hours"}, "menu": {"empty": "No menu available", "ratingFilter": "4+", "budgetFilter": "Budget", "budgetFilterHint": "15.99", "extras": "Extras", "sauces": "<PERSON><PERSON><PERSON>"}, "emptyDietPreferences": "No diet preferences", "deals": {"title": "Specials", "empty": "No specials available"}}, "profile": {"hint": "This profile has been deleted.", "tabs": {"post": "Posts", "settings": "Settings"}, "followers": "Followers", "following": "Following", "subscriptions": "Subscriptions", "settings": {"general": "General Settings", "help": "Help", "feedback": "<PERSON><PERSON><PERSON>", "orders": "Previous Orders", "logout": "Log out"}}, "generalSettings": {"profile": "User Profile", "password": "Change Password", "preferences": "Preferences", "payment": "Payment Method"}, "changePasswordSettings": {"title": "Change Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "buttonLabel": "Change Password"}, "changePreferences": {"buttonLabel": "Save"}, "userProfileSettings": {"deleteAccount": "Delete account", "deleteAccountTitle": "Are you sure you want to delete your account?", "deleteAccountDescription": "You will loose access to all of Mutualz exclusive  discounts and your profile and posts will be deleted  permanently.", "buttonCancel": "I changed my mind", "buttonOk": "I'm sure"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "messageLabel": "Message", "design": "Design", "userFriendliness": "User friendliness", "paymentFunction": "Payment function", "orderFunction": "Order function", "community": "Community", "couponsDiscounts": "Coupons & Discounts", "recommendations": "Recommendations", "pushNotifications": "Push Notifications", "speed": "Speed", "otherFeatures": "Other features", "successMessage": "Thank you for your feedback! Our team strives to deliver the best for you."}, "faq": {"title": "FAQ's", "contactUs": "Contact us", "empty": "No questions/answers found"}, "contact": {"title": "Contact Form", "messageLabel": "Write your message", "registration": "Registration process", "payment": "Payment function", "subscription": "Subscription", "report": "Report error", "other": "Other categories", "successMessage": "Thank you for your message! We will get back to you as soon as possible."}, "subscription": {"title": "Subscriptions", "description": "You`ll advance the subscription tiers by\ngathering points on every restaurant visit", "levels": {"test": "Just Test", "member": "Member", "silver": "Silver", "gold": "Gold"}, "levelsDescription": {"test": "Try MUTUALZ and use all our coupons for free", "member": "You pay 5% less on your whole bill.", "silver": "You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 10% on your whole bill (only in the restaurants in which the subscription was reached)", "gold": "You can only reach this subscription by accumalating a certain number of points in one restaurant. This subscription will give you a discount of 15% on your whole bill (only in the restaurants in which the subscription was reached)"}, "buttons": {"upgrade": "Upgrade now for 9,99€ monthly", "recommend": "Recommend us to a friend to get 1 month for free", "cancel": "Unsubscribe and loose your benefits", "reactivate": "Reactivate your subscription"}, "info(date)": "Your discount is still active till ${date}", "discount": {"withPercent": {"one": "${percent}% for 1 day", "other": "${percent}% for $n days"}}, "daysRemaining": {"one": "1 day left", "other": "$n days left"}, "discountOnTotalBill": "Discount on\nthe total bill*"}, "comments": {"title": "Comments", "inputLabel": "Add a comment", "empty": "No comments available"}, "orderProcess": {"title": "<PERSON><PERSON>", "empty": "No menu available", "orderButton": "Order/Pay", "orderSuccess": "Order created successfully. Please enjoy your meal!", "paymentSuccess": "Payment successfully completed"}, "cart": {"empty": "No elements in the cart", "total": "Total:", "dialogNotes": {"title": "Do you have any special requirements?", "hint": "Special request"}, "dialogRemove": {"title": "Remove dish from the cart", "description": "Are you sure you want to remove this dish from the cart?", "buttonCancel": "Cancel", "buttonConfirm": "I'm sure"}, "dialogSpecialRemove": {"title": "Remove special from the cart", "description": "Are you sure you want to remove this special from the cart?", "buttonCancel": "Cancel", "buttonConfirm": "I'm sure"}, "dialogConfirm": {"title": "Are you sure you've found everything?", "description": "Then order here", "buttonConfirm": "Confirm Selection"}, "dialogShare": {"description": "Recommend us to your friends to get a 1 month membership for free and save more money on your bill", "buttonShare": "To Share", "buttonCancel": "Share later"}, "createOrderSuccess": "Order created successfully. Please enjoy your meal!"}, "invoice": {"title": "Thanks\nfor joining us", "dishes": "Dishes", "coupons": "Coupons:", "couponSelection": "Coupon Selection", "doNotUseCoupon": "Do not use a coupon", "proceedToPayment": "Proceed to payment"}, "paymentMethodSettings": {"title": "Payment Methods", "card": "Card", "applePay": "Apple Pay", "googlePay": "Google Pay"}, "addPost": {"title": "Share your experience with everyone", "description": "Create a post of your best moments", "uploadImage": "Upload Image", "restaurantHint": "Select a restaurant", "descriptionHint": "Add a description here", "attachImage": "Attach Image", "tagYourFriends": "Tag your friends"}, "payment": {"message": "Thank you for your visit!", "buttonLabel": "Collect your points", "success": "Payment successfully completed"}, "orders": {"title": "Previous Orders", "empty": "No previous orders", "total": "Total", "memberDiscount": "Member discount", "couponDiscount": "Coupon discount", "totalWithMutualz": "Total with Mutualz", "summary": "Order summary", "sendInvoice": "Send Invoice", "invoiceSent": "Invoice has been successfully sent", "sendingInvoice": "Sending invoice..."}, "progressReward": {"unlockCoupons": "Spend them to unlock\nCoupons"}, "review": {"rating": "Rating", "ratingItems": {"service": "Service", "cleanliness": "Cleanliness", "waitingTime": "Waiting time", "foodQuality": "Food quality", "athmosphere": "Athmosphere"}, "commentHint": "Write your comment here", "verifyDietary": "Verify the dietary claims", "dietPreferences": {"yes": "Yes", "no": "No"}, "uploadImage": "Upload Image", "descriptionHint": "Add a description here", "rateTitle": "Rate now to get extra Points", "points": "Points", "rate": "Rate", "rateDescription": "Rate the service and earn 5 extra points", "rateAndPost": "Rate & Post", "rateAndPostDescription": "Rate the service and earn 10 extra points", "skip": "Rate later"}}, "bnb": {"discover": "Discover", "feed": "Feed", "search": "Search", "profile": "Profile"}, "filters": {"foodTypeTitle": "Types of food", "dietPreferencesTitle": "Diet preferences", "foodTitle": "Food", "sortTitle": "Sort", "foodType": {"american": "American", "arabic": "Arabic", "asian": "Asian", "bakery": "<PERSON><PERSON>", "burger": "Burger", "chinese": "Chinese", "curry": "<PERSON>", "german": "German", "doner": "<PERSON><PERSON>", "iceCream": "Ice Cream", "espresso": "Espresso", "falafel": "Falafel", "fish": "Fish", "breakfast": "Breakfast", "drinks": "Drinks", "chicken": "Chicken", "hotdog": "Hotdog", "indian": "Indian", "italian": "Italian", "japanese": "Japanese", "coffee": "Coffee", "cake": "Cake", "seafood": "Seafood", "mexican": "Mexican", "lunch": "Lunch", "desserts": "Desserts", "noodles": "Noodles", "austrian": "Austrian", "pakistani": "Pakistani", "pasta": "Pasta", "persian": "Persian", "pizza": "Pizza", "pokeBowl": "Poke Bowl", "ramen": "<PERSON><PERSON>", "frenchFries": "French Fries", "salads": "Salads", "sandwiches": "Sandwiches", "snacks": "Snacks", "steaks": "Steaks", "soups": "Soups", "sushi": "<PERSON><PERSON>", "syrian": "Syrian", "thai": "Thai", "turkish": "Turkish", "vegan": "Vegan", "vegetarian": "Vegetarian", "vietnamese": "Vietnamese", "wraps": "Wraps", "bar": "Bar", "bowls": "Bowls", "cafe": "Café", "cocktails": "Cocktails", "greek": "Greek", "kebab": "Kebab", "restaurant": "Restaurant", "spanish": "Spanish"}, "dietPreferences": {"kosher": "<PERSON><PERSON>", "halal": "<PERSON><PERSON>", "vegetarian": "Vegetarian", "vegan": "Vegan", "glutenFree": "Gluten Free", "lactoseFree": "Lactose Free", "nutsFree": "Nuts Free"}, "food": {"breakfast": "Breakfast", "brunch": "Brunch", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack"}, "sort": {"rating": "by rating"}}, "tools": {"imagePicker": {"title": "C<PERSON>per"}}, "custom": {"pointProgress": {"silver": "Silver", "gold": "Gold", "test": "Test", "member": "Member", "points(value, opposite)": "${value}/${opposite} Points", "pointsString(value)": "${value} Points"}, "isReached": "is reached", "youMadeIt": "You made it"}, "empty": {"image": "No Image"}, "coupons": {"available": "Available Coupons", "activatable": "Activatable Coupons", "noCoupons": "No available coupons", "appliable": "Appliable", "canBeReactivated": "Can be reactivated", "nonAppliable": "Non appliable"}, "coupon": {"title": "Mutualz", "off": "off", "percentage(value)": "${value}%", "separator": "for"}, "cities": {"berlin": "Berlin", "oldenburg": "Oldenburg", "osnaburg": "Osnabrück"}, "specials": {"title": "Specials", "single": "Special", "availableTitle": "Special available", "availableAt": "Available at"}, "genders": {"male": "Male", "female": "Female", "diverse": "Diverse"}, "restaurantInfoTab": {"deals": "Deals", "menu": "<PERSON><PERSON>", "feed": "Feed"}, "scanner": {"label": "Scan the QR code\\nto access the restaurant", "errorDifferentRestaurant": "You are scanning a QR code from a different restaurant", "errorRestaurantClosed(name)": "${name} is closed. Try again during the opening hours."}, "postCard": {"likes": "<PERSON>s", "comments": "Comments"}, "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "paymentInfo": {"total": "Total", "discount": "Discount", "totalWithMutualz": "Total with Mutualz"}, "loyalty": {"test": "Test", "member": "Member", "silver": "Silver", "gold": "Gold"}, "earnWidget": {"label": "You've earned", "points": "points"}, "reportUserPost": {"title": "Report User Post", "hint": "Please describe the reason for your report", "items": {"nudity": "Nudity or sexualization", "violence": "Blood or violence", "suicide": "Suicide, self-harm or eating disorders", "hate": "Hate or incitement to hatred", "ads": "Sale or advertising of products/services outside the catering sector"}}, "blockUserPost": {"title": "Block User Post", "hint": "Please describe the reason for your report", "items": {"nudity": "Nudity or sexualization", "violence": "Blood or violence", "suicide": "Suicide, self-harm or eating disorders", "hate": "Hate or incitement to hatred", "ads": "Sale or advertising of products/services outside the catering sector"}}, "blockUser": {"title": "Block User", "hint": "Please describe the block for your report", "items": {"insultsOrBullying": "Insults or bullying", "nudityOrSexualization": "Nudity or sexualization", "bloodOrViolence": "Blood or violence", "spam": "Spam", "falseIdentityOrIdentityStolen": "False identity/identity stolen", "accountCouldHaveBeenHacked": "Account could have been hacked", "personUnder16": "Person is under 16"}}, "reportUser": {"title": "Report User", "hint": "Please describe the reason for your report", "items": {"insultsOrBullying": "Insults or bullying", "nudityOrSexualization": "Nudity or sexualization", "bloodOrViolence": "Blood or violence", "spam": "Spam", "falseIdentityOrIdentityStolen": "False identity/identity stolen", "accountCouldHaveBeenHacked": "Account could have been hacked", "personUnder16": "Person is under 16"}}, "strangerProfile": {"followers": "Followers", "following": "Following", "friendsInCommon": "friends in common"}, "tutorial": {"map": {"qrCode": "Start your order. Here you can scan the QR code to place your order."}, "filter": {"info": "Filtering changes the results on the map and in the list view. Slide the filters up for the list view."}, "feed": {"addPost": "Here you can post a contribution.", "search": "Do you want to follow friends or family? Here you can search for the person."}, "restaurant": {"rating": "Click on the stars or the clock to get more information", "favorite": "Here you can mark the location as a favorite", "dietPreferences": "Dietary forms and intolerances. All gray icons are information from the restaurateur.", "dietPreferencesConfirmed": "If it's red, the information has been confirmed by users.", "deals": "Here you can see all deals and coupons", "menu": "Here you can browse in advance"}, "profile": {"subscriptions": "Here are all the levels you can reach within the loyalty program. Additionally, you can see how many points you have in total.", "orders": "Here you can view and download your bills", "dietPreferences": "Here you can adjust your dietary preferences"}, "orders": {"tableNumber": "You can find the table number next to the QR code on the table", "cart": "This is your shopping cart, here you can see all the products you have added.", "cartInside": "This is what your shopping cart looks like inside. You can adjust your orders here", "orderAndPayNow": "Here you order and pay immediately, this makes the most sense if you need to leave quickly, for example", "orderAndPayLater": "Here you can order and pay later when you leave the venue", "usedCoupons": "You have already used these coupons and can unlock them again with points", "loyaltyPoints": "You can use these points at this venue to unlock coupons again", "congratulations": "Congratulations! You have collected your first points in the loyalty program!"}, "loyalty": {"fourLevels": "There are four levels in the loyalty program. You always start at the first level and then have one month to advance to the Gold level.", "test": "You collect points with every purchase and thus advance to the next level.\n\nThe best part: Every purchase counts regardless of which MUTUALZ partner you shop with!\n\nImportant: At the end of the month you will be reset back to this level and 0 points!*", "member": "At this level you receive 5% discount on every bill with all partners.\n\nThe faster you reach this level, the more time you have to climb the next levels!\n\nSecret tip: With the Mutualz subscription you start at this level at the beginning of each month! Time advantage means price advantage!", "silver": "This level rewards the reliable and loyal!\n\nWith Silver you save 10% on the total bill in restaurants where you have reached Silver.\n\nYou can only reach this level by collecting enough points in one restaurant and advancing through levels.*", "gold": "Congratulations Your Majesty!\n\nWhen they say: \"The customer is king!\" Then they speak exclusively about you!\n\nYou save 15% on the total bill in the restaurant where you achieved this level.", "subscription": "Subscribe to MUTUALZ now and get 5% discount immediately in addition to your coupons and start every month with a time advantage !!!", "buttons": {"later": "Rather later...", "subscribe": "Subscribe", "next": "Continue"}, "terms": "*for more information check our Terms and Conditions", "level": "Level", "oneCalendarMonth": "One calendar month time"}}}