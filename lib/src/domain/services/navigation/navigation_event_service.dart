import 'dart:async';

class NavigationEventService {
  final _restaurantNavigationController = StreamController<String>.broadcast();
  
  Stream<String> get restaurantNavigationStream => _restaurantNavigationController.stream;
  
  void navigateToRestaurant(String restaurantId) {
    _restaurantNavigationController.add(restaurantId);
  }
  
  void dispose() {
    _restaurantNavigationController.close();
  }
}