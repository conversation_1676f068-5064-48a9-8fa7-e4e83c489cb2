import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  static const String _channelId = 'general';
  static const String _channelName = 'General';

  late final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  late final FirebaseMessaging _firebaseMessaging;
  final StreamController<RemoteMessage> _onBackgroundMessageController =
      StreamController<RemoteMessage>.broadcast();
      
  FlutterLocalNotificationsPlugin getFlutterLocalNotificationsPlugin() {
    return _flutterLocalNotificationsPlugin;
  }

  Stream<RemoteMessage> get onMessage => FirebaseMessaging.onMessage;

  Stream<RemoteMessage> get onMessageOpenedApp =>
      FirebaseMessaging.onMessageOpenedApp;

  Stream<RemoteMessage> get onBackgroundMessage =>
      _onBackgroundMessageController.stream;

  Stream<String> get onTokenRefresh => _firebaseMessaging.onTokenRefresh;

  NotificationService() {
    _initializeLocalNotifications();
    _initializeRemoteNotification();
  }

  Future<void> _initializeLocalNotifications() async {
    _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    
    final NotificationAppLaunchDetails? launchDetails = 
      await _flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
      
    if (launchDetails != null && launchDetails.didNotificationLaunchApp && 
        launchDetails.notificationResponse?.payload != null) {
      log('App launched by notification: ${launchDetails.notificationResponse?.payload}');
    }
    
    await _flutterLocalNotificationsPlugin.initialize(
        const InitializationSettings(
          android: AndroidInitializationSettings('@drawable/ic_notification'),
          iOS: DarwinInitializationSettings(
            requestAlertPermission: false,
            requestBadgePermission: false,
            requestSoundPermission: false,
          ),
        ), onDidReceiveNotificationResponse:
            (NotificationResponse response) async {
      log('Notification clicked: $response');
      if (response.payload != null) {
        _handleNotificationPayload(response.payload!);
      }
    },
        onDidReceiveBackgroundNotificationResponse:
            onDidReceiveBackgroundNotificationResponse);
  }

  void _handleNotificationPayload(String payload) {
    try {
      final Map<String, dynamic> data = json.decode(payload);
      if ([
          'late_review', 
          'couponRefreshmentAvailable'
        ].contains(data['notificationType'])) {
        final message = RemoteMessage(data: data);
        _onBackgroundMessageController.add(message);
      }
    } catch (e) {
      log('Error handling notification payload: $e');
    }
  }

  @pragma('vm:entry-point')
  static void onDidReceiveBackgroundNotificationResponse(
      NotificationResponse notificationResponse) {
    log('Notification clicked in background: $notificationResponse');
  }

  Future<void> _initializeRemoteNotification() async {
    _firebaseMessaging = FirebaseMessaging.instance;
  }

  /// Checks current notification permission status
  Future<bool> hasNotificationPermission() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  /// Requests notification permission from user (shows system modal once)
  /// Returns true if granted, false if denied
  Future<bool> requestNotificationPermission() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      return settings.authorizationStatus == AuthorizationStatus.authorized;
    } catch (e) {
      log('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Checks if permission was permanently denied by user
  Future<bool> isPermissionPermanentlyDenied() async {
    final firebaseSettings = await _firebaseMessaging.getNotificationSettings();
    return firebaseSettings.authorizationStatus == AuthorizationStatus.denied;
  }

  /// Opens system settings for notification permissions
  Future<bool> openNotificationSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      log('Error opening notification settings: $e');
      return false;
    }
  }

  /// Gets notification permission status for analytics/tracking
  Future<AuthorizationStatus> getPermissionStatus() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus;
  }

  Future<String?> getToken() async {
    await Future.delayed(const Duration(seconds: 1));

    return _firebaseMessaging.getToken();
  }

  Future<void> showLocalNotification(
      String title, String body, Map<String, dynamic> data) async {
    const androidNotificationDetail = AndroidNotificationDetails(
      _channelId,
      _channelName,
      priority: Priority.high,
      autoCancel: true,
      fullScreenIntent: true,
      enableVibration: true,
      importance: Importance.max,
      playSound: true,
    );
    const darwinNotificationDetails = DarwinNotificationDetails();
    const notificationDetails = NotificationDetails(
      iOS: darwinNotificationDetails,
      android: androidNotificationDetail,
    );
    try {
      await _flutterLocalNotificationsPlugin
          .show(0, title, body, notificationDetails, payload: json.encode(data));
    } catch (e) {
      log('Error showing notification: $e');
      rethrow;
    }
  }
}
