// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_component.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SpecialComponentImpl _$$SpecialComponentImplFromJson(
        Map<String, dynamic> json) =>
    _$SpecialComponentImpl(
      id: json['_id'] as String,
      name: json['name'] as String,
      dishes: (json['dishes'] as List<dynamic>?)
              ?.map((e) => SpecialDish.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      dish: json['dish'] == null
          ? null
          : SpecialDish.fromJson(json['dish'] as Map<String, dynamic>),
      allowMultipleExtras:
          (json['allowMultipleExtras'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {},
      allowMultipleSauces:
          (json['allowMultipleSauces'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {},
    );

Map<String, dynamic> _$$SpecialComponentImplToJson(
        _$SpecialComponentImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
      'dishes': instance.dishes,
      'dish': instance.dish,
      'allowMultipleExtras': instance.allowMultipleExtras,
      'allowMultipleSauces': instance.allowMultipleSauces,
    };
