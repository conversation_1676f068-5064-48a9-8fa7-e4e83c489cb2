import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/special/special_dish.dart';

part 'special_component.freezed.dart';
part 'special_component.g.dart';

@freezed
class SpecialComponent with _$SpecialComponent {
  const factory SpecialComponent({
    @JsonKey(name: '_id') required String id,
    required String name,
    @Default([]) List<SpecialDish> dishes,
    SpecialDish? dish,
    @Default({}) Map<String, bool> allowMultipleExtras,
    @Default({}) Map<String, bool> allowMultipleSauces,
  }) = _SpecialComponent;
  factory SpecialComponent.fromJson(Map<String, dynamic> json) =>
    _$SpecialComponentFromJson(json);
}