import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';

class SpecialDish {
  final String id;
  final String name;
  final List<ExtraGroupModel> extras;

  const SpecialDish({
    required this.id,
    required this.name,
    this.extras = const [],
  });

  factory SpecialDish.fromJson(Map<String, dynamic> json) {
    // Parse new grouped extras structure from backend
    final List<ExtraGroupModel> groups =
        (json['extras'] as List<dynamic>?)?.map((e) => ExtraGroupModel.fromJson(e as Map<String, dynamic>)).toList() ?? const [];

    return SpecialDish(
      id: json['_id'] as String,
      name: json['name'] as String,
      extras: groups,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'extras': extras.map((e) => e.toJson()).toList(),
    };
  }

  SpecialDish copyWith({
    String? id,
    String? name,
    List<ExtraGroupModel>? extras,
  }) {
    return SpecialDish(
      id: id ?? this.id,
      name: name ?? this.name,
      extras: extras ?? this.extras,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SpecialDish && other.id == id && other.name == name && other.extras == extras;
  }

  @override
  int get hashCode => Object.hash(id, name, extras);

  @override
  String toString() {
    return 'SpecialDish(id: $id, name: $name, extras: $extras)';
  }
}

extension SpecialDishX on SpecialDish {
  // Methods for grouped extras
  List<ExtraGroupModel> get sauceGroups => extras.where((g) => g.type == ExtraType.sauce).toList();
  List<ExtraGroupModel> get toppingGroups => extras.where((g) => g.type == ExtraType.topping).toList();
  List<ExtraGroupModel> get extrasGroups => extras.where((g) => g.type == ExtraType.extras).toList();

  // Helper to check if dish has extras
  bool get hasExtras => extras.isNotEmpty;

  // Legacy compatibility - now returns empty lists since we use grouped structure
  List<dynamic> get toppings => [];
  List<dynamic> get sauces => [];
  List<dynamic> get getExtras => [];
}
