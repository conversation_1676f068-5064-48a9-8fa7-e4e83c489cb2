import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';

part 'special_dish.freezed.dart';
part 'special_dish.g.dart';

@freezed
class SpecialDish with _$SpecialDish {
  const factory SpecialDish({
    @JsonKey(name: '_id') required String id,
    required String name,
    @Default([]) List<SpecialExtraModel> extras,
    @Default([]) List<ExtraGroupModel> extraGroups,
    @Default(true) bool allowMultipleExtras,
    @Default(true) bool allowMultipleSauces,
  }) = _SpecialDish;
  factory SpecialDish.fromJson(Map<String, dynamic> json) =>
    _$SpecialDishFromJson(json);
}

extension SpecialDishX on SpecialDish {
  // Legacy methods for backward compatibility
  List<SpecialExtraModel> get toppings => extras.where((e) => e.isTopping).toList();
  List<SpecialExtraModel> get sauces => extras.where((e) => e.isSauce).toList();
  List<SpecialExtraModel> get getExtras => extras.where((e) => e.isExtras).toList();
  
  // New methods for grouped extras
  List<ExtraGroupModel> get sauceGroups => extraGroups.where((g) => g.type == 'sauce').toList();
  List<ExtraGroupModel> get toppingGroups => extraGroups.where((g) => g.type == 'topping').toList();
  List<ExtraGroupModel> get extrasGroups => extraGroups.where((g) => g.type == 'extras').toList();
  
  // Helper to check if dish uses new grouped structure
  bool get hasGroupedExtras => extraGroups.isNotEmpty;
}
