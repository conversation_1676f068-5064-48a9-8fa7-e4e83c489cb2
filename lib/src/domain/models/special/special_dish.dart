import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';

part 'special_dish.freezed.dart';
part 'special_dish.g.dart';

@freezed
class SpecialDish with _$SpecialDish {
  const factory SpecialDish({
    @JsonKey(name: '_id') required String id,
    required String name,
    @Default([]) List<ExtraGroupModel> extras,
  }) = _SpecialDish;

  factory SpecialDish.fromJson(Map<String, dynamic> json) {
    // Parse new grouped extras structure from backend
    final List<ExtraGroupModel> groups =
        (json['extras'] as List<dynamic>?)?.map((e) => ExtraGroupModel.fromJson(e as Map<String, dynamic>)).toList() ?? const [];

    return SpecialDish(
      id: json['_id'] as String,
      name: json['name'] as String,
      extras: groups,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'extras': extras.map((e) => e.toJson()).toList(),
    };
  }
}

extension SpecialDishX on SpecialDish {
  // Methods for grouped extras
  List<ExtraGroupModel> get sauceGroups => extras.where((g) => g.type == ExtraType.sauce).toList();
  List<ExtraGroupModel> get toppingGroups => extras.where((g) => g.type == ExtraType.topping).toList();
  List<ExtraGroupModel> get extrasGroups => extras.where((g) => g.type == ExtraType.extras).toList();

  // Helper to check if dish has extras
  bool get hasExtras => extras.isNotEmpty;

  // Legacy compatibility - now returns empty lists since we use grouped structure
  List<dynamic> get toppings => [];
  List<dynamic> get sauces => [];
  List<dynamic> get getExtras => [];
}
