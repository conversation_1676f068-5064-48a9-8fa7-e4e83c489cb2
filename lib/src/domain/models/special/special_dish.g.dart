// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_dish.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SpecialDishImpl _$$SpecialDishImplFromJson(Map<String, dynamic> json) =>
    _$SpecialDishImpl(
      id: json['_id'] as String,
      name: json['name'] as String,
      extras: (json['extras'] as List<dynamic>?)
              ?.map(
                  (e) => SpecialExtraModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      extraGroups: (json['extraGroups'] as List<dynamic>?)
              ?.map((e) => ExtraGroupModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      allowMultipleExtras: json['allowMultipleExtras'] as bool? ?? true,
      allowMultipleSauces: json['allowMultipleSauces'] as bool? ?? true,
    );

Map<String, dynamic> _$$SpecialDishImplToJson(_$SpecialDishImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
      'extras': instance.extras,
      'extraGroups': instance.extraGroups,
      'allowMultipleExtras': instance.allowMultipleExtras,
      'allowMultipleSauces': instance.allowMultipleSauces,
    };
