// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'special_component.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SpecialComponent _$SpecialComponentFromJson(Map<String, dynamic> json) {
  return _SpecialComponent.fromJson(json);
}

/// @nodoc
mixin _$SpecialComponent {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<SpecialDish> get dishes => throw _privateConstructorUsedError;
  SpecialDish? get dish => throw _privateConstructorUsedError;
  Map<String, bool> get allowMultipleExtras =>
      throw _privateConstructorUsedError;
  Map<String, bool> get allowMultipleSauces =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialComponentCopyWith<SpecialComponent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialComponentCopyWith<$Res> {
  factory $SpecialComponentCopyWith(
          SpecialComponent value, $Res Function(SpecialComponent) then) =
      _$SpecialComponentCopyWithImpl<$Res, SpecialComponent>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      List<SpecialDish> dishes,
      SpecialDish? dish,
      Map<String, bool> allowMultipleExtras,
      Map<String, bool> allowMultipleSauces});

  $SpecialDishCopyWith<$Res>? get dish;
}

/// @nodoc
class _$SpecialComponentCopyWithImpl<$Res, $Val extends SpecialComponent>
    implements $SpecialComponentCopyWith<$Res> {
  _$SpecialComponentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? dishes = null,
    Object? dish = freezed,
    Object? allowMultipleExtras = null,
    Object? allowMultipleSauces = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dishes: null == dishes
          ? _value.dishes
          : dishes // ignore: cast_nullable_to_non_nullable
              as List<SpecialDish>,
      dish: freezed == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as SpecialDish?,
      allowMultipleExtras: null == allowMultipleExtras
          ? _value.allowMultipleExtras
          : allowMultipleExtras // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      allowMultipleSauces: null == allowMultipleSauces
          ? _value.allowMultipleSauces
          : allowMultipleSauces // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SpecialDishCopyWith<$Res>? get dish {
    if (_value.dish == null) {
      return null;
    }

    return $SpecialDishCopyWith<$Res>(_value.dish!, (value) {
      return _then(_value.copyWith(dish: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpecialComponentImplCopyWith<$Res>
    implements $SpecialComponentCopyWith<$Res> {
  factory _$$SpecialComponentImplCopyWith(_$SpecialComponentImpl value,
          $Res Function(_$SpecialComponentImpl) then) =
      __$$SpecialComponentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      List<SpecialDish> dishes,
      SpecialDish? dish,
      Map<String, bool> allowMultipleExtras,
      Map<String, bool> allowMultipleSauces});

  @override
  $SpecialDishCopyWith<$Res>? get dish;
}

/// @nodoc
class __$$SpecialComponentImplCopyWithImpl<$Res>
    extends _$SpecialComponentCopyWithImpl<$Res, _$SpecialComponentImpl>
    implements _$$SpecialComponentImplCopyWith<$Res> {
  __$$SpecialComponentImplCopyWithImpl(_$SpecialComponentImpl _value,
      $Res Function(_$SpecialComponentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? dishes = null,
    Object? dish = freezed,
    Object? allowMultipleExtras = null,
    Object? allowMultipleSauces = null,
  }) {
    return _then(_$SpecialComponentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      dishes: null == dishes
          ? _value._dishes
          : dishes // ignore: cast_nullable_to_non_nullable
              as List<SpecialDish>,
      dish: freezed == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as SpecialDish?,
      allowMultipleExtras: null == allowMultipleExtras
          ? _value._allowMultipleExtras
          : allowMultipleExtras // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      allowMultipleSauces: null == allowMultipleSauces
          ? _value._allowMultipleSauces
          : allowMultipleSauces // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialComponentImpl implements _SpecialComponent {
  const _$SpecialComponentImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.name,
      final List<SpecialDish> dishes = const [],
      this.dish,
      final Map<String, bool> allowMultipleExtras = const {},
      final Map<String, bool> allowMultipleSauces = const {}})
      : _dishes = dishes,
        _allowMultipleExtras = allowMultipleExtras,
        _allowMultipleSauces = allowMultipleSauces;

  factory _$SpecialComponentImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialComponentImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String name;
  final List<SpecialDish> _dishes;
  @override
  @JsonKey()
  List<SpecialDish> get dishes {
    if (_dishes is EqualUnmodifiableListView) return _dishes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dishes);
  }

  @override
  final SpecialDish? dish;
  final Map<String, bool> _allowMultipleExtras;
  @override
  @JsonKey()
  Map<String, bool> get allowMultipleExtras {
    if (_allowMultipleExtras is EqualUnmodifiableMapView)
      return _allowMultipleExtras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_allowMultipleExtras);
  }

  final Map<String, bool> _allowMultipleSauces;
  @override
  @JsonKey()
  Map<String, bool> get allowMultipleSauces {
    if (_allowMultipleSauces is EqualUnmodifiableMapView)
      return _allowMultipleSauces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_allowMultipleSauces);
  }

  @override
  String toString() {
    return 'SpecialComponent(id: $id, name: $name, dishes: $dishes, dish: $dish, allowMultipleExtras: $allowMultipleExtras, allowMultipleSauces: $allowMultipleSauces)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialComponentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._dishes, _dishes) &&
            (identical(other.dish, dish) || other.dish == dish) &&
            const DeepCollectionEquality()
                .equals(other._allowMultipleExtras, _allowMultipleExtras) &&
            const DeepCollectionEquality()
                .equals(other._allowMultipleSauces, _allowMultipleSauces));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      const DeepCollectionEquality().hash(_dishes),
      dish,
      const DeepCollectionEquality().hash(_allowMultipleExtras),
      const DeepCollectionEquality().hash(_allowMultipleSauces));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialComponentImplCopyWith<_$SpecialComponentImpl> get copyWith =>
      __$$SpecialComponentImplCopyWithImpl<_$SpecialComponentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialComponentImplToJson(
      this,
    );
  }
}

abstract class _SpecialComponent implements SpecialComponent {
  const factory _SpecialComponent(
      {@JsonKey(name: '_id') required final String id,
      required final String name,
      final List<SpecialDish> dishes,
      final SpecialDish? dish,
      final Map<String, bool> allowMultipleExtras,
      final Map<String, bool> allowMultipleSauces}) = _$SpecialComponentImpl;

  factory _SpecialComponent.fromJson(Map<String, dynamic> json) =
      _$SpecialComponentImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String get name;
  @override
  List<SpecialDish> get dishes;
  @override
  SpecialDish? get dish;
  @override
  Map<String, bool> get allowMultipleExtras;
  @override
  Map<String, bool> get allowMultipleSauces;
  @override
  @JsonKey(ignore: true)
  _$$SpecialComponentImplCopyWith<_$SpecialComponentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
