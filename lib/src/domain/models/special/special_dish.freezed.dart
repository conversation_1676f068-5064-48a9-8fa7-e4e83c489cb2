// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'special_dish.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SpecialDish _$SpecialDishFromJson(Map<String, dynamic> json) {
  return _SpecialDish.fromJson(json);
}

/// @nodoc
mixin _$SpecialDish {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<SpecialExtraModel> get extras => throw _privateConstructorUsedError;
  List<ExtraGroupModel> get extraGroups => throw _privateConstructorUsedError;
  bool get allowMultipleExtras => throw _privateConstructorUsedError;
  bool get allowMultipleSauces => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialDishCopyWith<SpecialDish> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialDishCopyWith<$Res> {
  factory $SpecialDishCopyWith(
          SpecialDish value, $Res Function(SpecialDish) then) =
      _$SpecialDishCopyWithImpl<$Res, SpecialDish>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      List<SpecialExtraModel> extras,
      List<ExtraGroupModel> extraGroups,
      bool allowMultipleExtras,
      bool allowMultipleSauces});
}

/// @nodoc
class _$SpecialDishCopyWithImpl<$Res, $Val extends SpecialDish>
    implements $SpecialDishCopyWith<$Res> {
  _$SpecialDishCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? extras = null,
    Object? extraGroups = null,
    Object? allowMultipleExtras = null,
    Object? allowMultipleSauces = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value.extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<SpecialExtraModel>,
      extraGroups: null == extraGroups
          ? _value.extraGroups
          : extraGroups // ignore: cast_nullable_to_non_nullable
              as List<ExtraGroupModel>,
      allowMultipleExtras: null == allowMultipleExtras
          ? _value.allowMultipleExtras
          : allowMultipleExtras // ignore: cast_nullable_to_non_nullable
              as bool,
      allowMultipleSauces: null == allowMultipleSauces
          ? _value.allowMultipleSauces
          : allowMultipleSauces // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SpecialDishImplCopyWith<$Res>
    implements $SpecialDishCopyWith<$Res> {
  factory _$$SpecialDishImplCopyWith(
          _$SpecialDishImpl value, $Res Function(_$SpecialDishImpl) then) =
      __$$SpecialDishImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      List<SpecialExtraModel> extras,
      List<ExtraGroupModel> extraGroups,
      bool allowMultipleExtras,
      bool allowMultipleSauces});
}

/// @nodoc
class __$$SpecialDishImplCopyWithImpl<$Res>
    extends _$SpecialDishCopyWithImpl<$Res, _$SpecialDishImpl>
    implements _$$SpecialDishImplCopyWith<$Res> {
  __$$SpecialDishImplCopyWithImpl(
      _$SpecialDishImpl _value, $Res Function(_$SpecialDishImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? extras = null,
    Object? extraGroups = null,
    Object? allowMultipleExtras = null,
    Object? allowMultipleSauces = null,
  }) {
    return _then(_$SpecialDishImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value._extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<SpecialExtraModel>,
      extraGroups: null == extraGroups
          ? _value._extraGroups
          : extraGroups // ignore: cast_nullable_to_non_nullable
              as List<ExtraGroupModel>,
      allowMultipleExtras: null == allowMultipleExtras
          ? _value.allowMultipleExtras
          : allowMultipleExtras // ignore: cast_nullable_to_non_nullable
              as bool,
      allowMultipleSauces: null == allowMultipleSauces
          ? _value.allowMultipleSauces
          : allowMultipleSauces // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialDishImpl implements _SpecialDish {
  const _$SpecialDishImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.name,
      final List<SpecialExtraModel> extras = const [],
      final List<ExtraGroupModel> extraGroups = const [],
      this.allowMultipleExtras = true,
      this.allowMultipleSauces = true})
      : _extras = extras,
        _extraGroups = extraGroups;

  factory _$SpecialDishImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialDishImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String name;
  final List<SpecialExtraModel> _extras;
  @override
  @JsonKey()
  List<SpecialExtraModel> get extras {
    if (_extras is EqualUnmodifiableListView) return _extras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extras);
  }

  final List<ExtraGroupModel> _extraGroups;
  @override
  @JsonKey()
  List<ExtraGroupModel> get extraGroups {
    if (_extraGroups is EqualUnmodifiableListView) return _extraGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extraGroups);
  }

  @override
  @JsonKey()
  final bool allowMultipleExtras;
  @override
  @JsonKey()
  final bool allowMultipleSauces;

  @override
  String toString() {
    return 'SpecialDish(id: $id, name: $name, extras: $extras, extraGroups: $extraGroups, allowMultipleExtras: $allowMultipleExtras, allowMultipleSauces: $allowMultipleSauces)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialDishImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._extras, _extras) &&
            const DeepCollectionEquality()
                .equals(other._extraGroups, _extraGroups) &&
            (identical(other.allowMultipleExtras, allowMultipleExtras) ||
                other.allowMultipleExtras == allowMultipleExtras) &&
            (identical(other.allowMultipleSauces, allowMultipleSauces) ||
                other.allowMultipleSauces == allowMultipleSauces));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      const DeepCollectionEquality().hash(_extras),
      const DeepCollectionEquality().hash(_extraGroups),
      allowMultipleExtras,
      allowMultipleSauces);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialDishImplCopyWith<_$SpecialDishImpl> get copyWith =>
      __$$SpecialDishImplCopyWithImpl<_$SpecialDishImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialDishImplToJson(
      this,
    );
  }
}

abstract class _SpecialDish implements SpecialDish {
  const factory _SpecialDish(
      {@JsonKey(name: '_id') required final String id,
      required final String name,
      final List<SpecialExtraModel> extras,
      final List<ExtraGroupModel> extraGroups,
      final bool allowMultipleExtras,
      final bool allowMultipleSauces}) = _$SpecialDishImpl;

  factory _SpecialDish.fromJson(Map<String, dynamic> json) =
      _$SpecialDishImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String get name;
  @override
  List<SpecialExtraModel> get extras;
  @override
  List<ExtraGroupModel> get extraGroups;
  @override
  bool get allowMultipleExtras;
  @override
  bool get allowMultipleSauces;
  @override
  @JsonKey(ignore: true)
  _$$SpecialDishImplCopyWith<_$SpecialDishImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
