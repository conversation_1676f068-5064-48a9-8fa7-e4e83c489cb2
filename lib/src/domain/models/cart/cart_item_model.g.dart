// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CartItemModelImpl _$$CartItemModelImplFromJson(Map<String, dynamic> json) =>
    _$CartItemModelImpl(
      id: json['_id'] as String?,
      quantity: json['quantity'] as int,
      dish: DishModel.fromJson(json['dish'] as Map<String, dynamic>),
      selectedExtras: (json['selectedExtras'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                (e as List<dynamic>)
                    .map((e) =>
                        ExtraItemModel.fromJson(e as Map<String, dynamic>))
                    .toList()),
          ) ??
          const {},
      notes: json['notes'] as String?,
      uuid: json['uuid'] as String?,
    );

Map<String, dynamic> _$$CartItemModelImplToJson(_$CartItemModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'quantity': instance.quantity,
      'dish': instance.dish,
      'selectedExtras': instance.selectedExtras,
      'notes': instance.notes,
      'uuid': instance.uuid,
    };
