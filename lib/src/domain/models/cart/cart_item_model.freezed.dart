// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cart_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CartItemModel _$CartItemModelFromJson(Map<String, dynamic> json) {
  return _CartItemModel.fromJson(json);
}

/// @nodoc
mixin _$CartItemModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  DishModel get dish => throw _privateConstructorUsedError;
  Map<String, List<ExtraItemModel>> get selectedExtras =>
      throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get uuid => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CartItemModelCopyWith<CartItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CartItemModelCopyWith<$Res> {
  factory $CartItemModelCopyWith(
          CartItemModel value, $Res Function(CartItemModel) then) =
      _$CartItemModelCopyWithImpl<$Res, CartItemModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      int quantity,
      DishModel dish,
      Map<String, List<ExtraItemModel>> selectedExtras,
      String? notes,
      String? uuid});

  $DishModelCopyWith<$Res> get dish;
}

/// @nodoc
class _$CartItemModelCopyWithImpl<$Res, $Val extends CartItemModel>
    implements $CartItemModelCopyWith<$Res> {
  _$CartItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? quantity = null,
    Object? dish = null,
    Object? selectedExtras = null,
    Object? notes = freezed,
    Object? uuid = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      dish: null == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as DishModel,
      selectedExtras: null == selectedExtras
          ? _value.selectedExtras
          : selectedExtras // ignore: cast_nullable_to_non_nullable
              as Map<String, List<ExtraItemModel>>,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DishModelCopyWith<$Res> get dish {
    return $DishModelCopyWith<$Res>(_value.dish, (value) {
      return _then(_value.copyWith(dish: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CartItemModelImplCopyWith<$Res>
    implements $CartItemModelCopyWith<$Res> {
  factory _$$CartItemModelImplCopyWith(
          _$CartItemModelImpl value, $Res Function(_$CartItemModelImpl) then) =
      __$$CartItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      int quantity,
      DishModel dish,
      Map<String, List<ExtraItemModel>> selectedExtras,
      String? notes,
      String? uuid});

  @override
  $DishModelCopyWith<$Res> get dish;
}

/// @nodoc
class __$$CartItemModelImplCopyWithImpl<$Res>
    extends _$CartItemModelCopyWithImpl<$Res, _$CartItemModelImpl>
    implements _$$CartItemModelImplCopyWith<$Res> {
  __$$CartItemModelImplCopyWithImpl(
      _$CartItemModelImpl _value, $Res Function(_$CartItemModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? quantity = null,
    Object? dish = null,
    Object? selectedExtras = null,
    Object? notes = freezed,
    Object? uuid = freezed,
  }) {
    return _then(_$CartItemModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      dish: null == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as DishModel,
      selectedExtras: null == selectedExtras
          ? _value._selectedExtras
          : selectedExtras // ignore: cast_nullable_to_non_nullable
              as Map<String, List<ExtraItemModel>>,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CartItemModelImpl implements _CartItemModel {
  const _$CartItemModelImpl(
      {@JsonKey(name: '_id') this.id,
      required this.quantity,
      required this.dish,
      final Map<String, List<ExtraItemModel>> selectedExtras = const {},
      this.notes,
      this.uuid})
      : _selectedExtras = selectedExtras;

  factory _$CartItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CartItemModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final int quantity;
  @override
  final DishModel dish;
  final Map<String, List<ExtraItemModel>> _selectedExtras;
  @override
  @JsonKey()
  Map<String, List<ExtraItemModel>> get selectedExtras {
    if (_selectedExtras is EqualUnmodifiableMapView) return _selectedExtras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_selectedExtras);
  }

  @override
  final String? notes;
  @override
  final String? uuid;

  @override
  String toString() {
    return 'CartItemModel(id: $id, quantity: $quantity, dish: $dish, selectedExtras: $selectedExtras, notes: $notes, uuid: $uuid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CartItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.dish, dish) || other.dish == dish) &&
            const DeepCollectionEquality()
                .equals(other._selectedExtras, _selectedExtras) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, quantity, dish,
      const DeepCollectionEquality().hash(_selectedExtras), notes, uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CartItemModelImplCopyWith<_$CartItemModelImpl> get copyWith =>
      __$$CartItemModelImplCopyWithImpl<_$CartItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CartItemModelImplToJson(
      this,
    );
  }
}

abstract class _CartItemModel implements CartItemModel {
  const factory _CartItemModel(
      {@JsonKey(name: '_id') final String? id,
      required final int quantity,
      required final DishModel dish,
      final Map<String, List<ExtraItemModel>> selectedExtras,
      final String? notes,
      final String? uuid}) = _$CartItemModelImpl;

  factory _CartItemModel.fromJson(Map<String, dynamic> json) =
      _$CartItemModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  int get quantity;
  @override
  DishModel get dish;
  @override
  Map<String, List<ExtraItemModel>> get selectedExtras;
  @override
  String? get notes;
  @override
  String? get uuid;
  @override
  @JsonKey(ignore: true)
  _$$CartItemModelImplCopyWith<_$CartItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
