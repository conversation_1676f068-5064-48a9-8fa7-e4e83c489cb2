import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/models.dart';

part 'special_cart_item_model.freezed.dart';
part 'special_cart_item_model.g.dart';

@freezed
class SpecialCartItemModel with _$SpecialCartItemModel {
  const factory SpecialCartItemModel({
    @JsonKey(name: '_id') String? id,
    required int quantity,
    required SpecialModel special,
    @Default([]) List<SpecialComponent> components,
    String? notes,
  }) = _SpecialCartItemModel;
  factory SpecialCartItemModel.fromJson(Map<String, dynamic> json) => _$SpecialCartItemModelFromJson(json);
}

extension SpecialCartItemModelX on SpecialCartItemModel {
  double get extrasPrice => components.fold<double>(
      0,
      (prev, component) =>
          prev +
          component.dishes.fold(0,
              (prev, dish) => prev + dish.extras.fold(0, (prev, group) => prev + group.items.fold(0, (prev, item) => prev + item.price))));

  double get total => special.price * quantity;

  double get totalWithExtras => total + extrasPrice;

  String get totalInEuro => total.inEuro;

  // Map<String, dynamic> toJson() => {
  //   'quantity': quantity,
  //   'special': special.toJson(),
  //   'components': components.map((e) => e.toJson()).toList(),
  //   'notes': notes,
  // };
}
