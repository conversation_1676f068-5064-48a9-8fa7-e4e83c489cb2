import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/menu/dish_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:uuid/uuid.dart';

part 'cart_item_model.freezed.dart';
part 'cart_item_model.g.dart';

@freezed
class CartItemModel with _$CartItemModel {
  const factory CartItemModel({
    @JsonKey(name: '_id') String? id,
    required int quantity,
    required DishModel dish,
    @Default({}) Map<String, List<ExtraItemModel>> selectedExtras,
    String? notes,
    String? uuid
  }) = _CartItemModel;
  factory CartItemModel.fromJson(Map<String, dynamic> json) =>
    _$CartItemModelFromJson(json);
}

extension CartItemModelX on CartItemModel {
  // Extras calculation
  double get totalExtras {
    double total = 0.0;
    selectedExtras.values.forEach((items) {
      total += items.fold(0.0, (acc, item) => acc + item.price);
    });
    return total;
  }

  double get total => dish.price * quantity;

  double get totalWithExtras => total + totalExtras;

  String get totalInEuro => totalWithExtras.inEuro;

  // Convert selectedExtras back to List<ExtraModel> with correct types
  List<ExtraModel> get extrasForDisplay {
    List<ExtraModel> extras = [];
    
    for (final entry in selectedExtras.entries) {
      final groupId = entry.key;
      final items = entry.value;
      
      // Find the group in dish.extras to get the correct type
      final group = dish.extras.firstWhere(
        (g) => g.id == groupId,
        orElse: () => throw Exception('Group $groupId not found in dish extras'),
      );
      
      // Convert each item to ExtraModel with correct type
      for (final item in items) {
        extras.add(ExtraModel(
          id: item.id,
          name: item.name,
          price: item.price,
          type: group.type, // Use the correct type from the group
        ));
      }
    }
    
    return extras;
  }

}