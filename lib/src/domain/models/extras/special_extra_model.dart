import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';

part 'special_extra_model.freezed.dart';
part 'special_extra_model.g.dart';

@freezed
class SpecialExtraModel with _$SpecialExtraModel {
  const factory SpecialExtraModel({
    @JsonKey(name: '_id') required String id,
    required ExtraType type,
    required String name,
    @Default(0.0) double price,
  }) = _SpecialExtraModel;
  factory SpecialExtraModel.fromJson(Map<String, dynamic> json) =>
    _$SpecialExtraModelFromJson(json);
}

extension SpecialExtraModelX on SpecialExtraModel {
  bool get isTopping => type == ExtraType.topping;
  bool get isExtras => type == ExtraType.extras;
  bool get isSauce => type == ExtraType.sauce;
}
