import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';

part 'extra_model.freezed.dart';
part 'extra_model.g.dart';

@freezed
class ExtraModel with _$ExtraModel {
  const factory ExtraModel({
    @JsonKey(name: '_id') required String id,
    required ExtraType type,
    required String name,
    @Default(0.0) double price,
  }) = _ExtraModel;

  factory ExtraModel.fromJson(Map<String, dynamic> json) => _$ExtraModelFromJson(json);
}

extension ExtraModelX on ExtraModel {
  bool get isTopping => type == ExtraType.topping;
  bool get isSauce => type == ExtraType.sauce;
  bool get isExtras => type == ExtraType.extras;
}
