// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'extra_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExtraItemModel _$ExtraItemModelFromJson(Map<String, dynamic> json) {
  return _ExtraItemModel.fromJson(json);
}

/// @nodoc
mixin _$ExtraItemModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraItemModelCopyWith<ExtraItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraItemModelCopyWith<$Res> {
  factory $ExtraItemModelCopyWith(
          ExtraItemModel value, $Res Function(ExtraItemModel) then) =
      _$ExtraItemModelCopyWithImpl<$Res, ExtraItemModel>;
  @useResult
  $Res call({@JsonKey(name: '_id') String id, String name, double price});
}

/// @nodoc
class _$ExtraItemModelCopyWithImpl<$Res, $Val extends ExtraItemModel>
    implements $ExtraItemModelCopyWith<$Res> {
  _$ExtraItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExtraItemModelImplCopyWith<$Res>
    implements $ExtraItemModelCopyWith<$Res> {
  factory _$$ExtraItemModelImplCopyWith(_$ExtraItemModelImpl value,
          $Res Function(_$ExtraItemModelImpl) then) =
      __$$ExtraItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: '_id') String id, String name, double price});
}

/// @nodoc
class __$$ExtraItemModelImplCopyWithImpl<$Res>
    extends _$ExtraItemModelCopyWithImpl<$Res, _$ExtraItemModelImpl>
    implements _$$ExtraItemModelImplCopyWith<$Res> {
  __$$ExtraItemModelImplCopyWithImpl(
      _$ExtraItemModelImpl _value, $Res Function(_$ExtraItemModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? price = null,
  }) {
    return _then(_$ExtraItemModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExtraItemModelImpl implements _ExtraItemModel {
  const _$ExtraItemModelImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.name,
      this.price = 0.0});

  factory _$ExtraItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExtraItemModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String name;
  @override
  @JsonKey()
  final double price;

  @override
  String toString() {
    return 'ExtraItemModel(id: $id, name: $name, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExtraItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, price);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExtraItemModelImplCopyWith<_$ExtraItemModelImpl> get copyWith =>
      __$$ExtraItemModelImplCopyWithImpl<_$ExtraItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExtraItemModelImplToJson(
      this,
    );
  }
}

abstract class _ExtraItemModel implements ExtraItemModel {
  const factory _ExtraItemModel(
      {@JsonKey(name: '_id') required final String id,
      required final String name,
      final double price}) = _$ExtraItemModelImpl;

  factory _ExtraItemModel.fromJson(Map<String, dynamic> json) =
      _$ExtraItemModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String get name;
  @override
  double get price;
  @override
  @JsonKey(ignore: true)
  _$$ExtraItemModelImplCopyWith<_$ExtraItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
