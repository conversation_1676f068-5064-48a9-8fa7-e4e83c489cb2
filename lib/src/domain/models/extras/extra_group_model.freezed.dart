// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'extra_group_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExtraGroupModel _$ExtraGroupModelFromJson(Map<String, dynamic> json) {
  return _ExtraGroupModel.fromJson(json);
}

/// @nodoc
mixin _$ExtraGroupModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  ExtraType get type => throw _privateConstructorUsedError;
  bool get allowMultiple => throw _privateConstructorUsedError;
  List<ExtraItemModel> get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraGroupModelCopyWith<ExtraGroupModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraGroupModelCopyWith<$Res> {
  factory $ExtraGroupModelCopyWith(
          ExtraGroupModel value, $Res Function(ExtraGroupModel) then) =
      _$ExtraGroupModelCopyWithImpl<$Res, ExtraGroupModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      ExtraType type,
      bool allowMultiple,
      List<ExtraItemModel> items});
}

/// @nodoc
class _$ExtraGroupModelCopyWithImpl<$Res, $Val extends ExtraGroupModel>
    implements $ExtraGroupModelCopyWith<$Res> {
  _$ExtraGroupModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? allowMultiple = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExtraType,
      allowMultiple: null == allowMultiple
          ? _value.allowMultiple
          : allowMultiple // ignore: cast_nullable_to_non_nullable
              as bool,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ExtraItemModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExtraGroupModelImplCopyWith<$Res>
    implements $ExtraGroupModelCopyWith<$Res> {
  factory _$$ExtraGroupModelImplCopyWith(_$ExtraGroupModelImpl value,
          $Res Function(_$ExtraGroupModelImpl) then) =
      __$$ExtraGroupModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String name,
      ExtraType type,
      bool allowMultiple,
      List<ExtraItemModel> items});
}

/// @nodoc
class __$$ExtraGroupModelImplCopyWithImpl<$Res>
    extends _$ExtraGroupModelCopyWithImpl<$Res, _$ExtraGroupModelImpl>
    implements _$$ExtraGroupModelImplCopyWith<$Res> {
  __$$ExtraGroupModelImplCopyWithImpl(
      _$ExtraGroupModelImpl _value, $Res Function(_$ExtraGroupModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? allowMultiple = null,
    Object? items = null,
  }) {
    return _then(_$ExtraGroupModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExtraType,
      allowMultiple: null == allowMultiple
          ? _value.allowMultiple
          : allowMultiple // ignore: cast_nullable_to_non_nullable
              as bool,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ExtraItemModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExtraGroupModelImpl implements _ExtraGroupModel {
  const _$ExtraGroupModelImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.name,
      required this.type,
      this.allowMultiple = false,
      final List<ExtraItemModel> items = const []})
      : _items = items;

  factory _$ExtraGroupModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExtraGroupModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String name;
  @override
  final ExtraType type;
  @override
  @JsonKey()
  final bool allowMultiple;
  final List<ExtraItemModel> _items;
  @override
  @JsonKey()
  List<ExtraItemModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'ExtraGroupModel(id: $id, name: $name, type: $type, allowMultiple: $allowMultiple, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExtraGroupModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.allowMultiple, allowMultiple) ||
                other.allowMultiple == allowMultiple) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, type, allowMultiple,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExtraGroupModelImplCopyWith<_$ExtraGroupModelImpl> get copyWith =>
      __$$ExtraGroupModelImplCopyWithImpl<_$ExtraGroupModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExtraGroupModelImplToJson(
      this,
    );
  }
}

abstract class _ExtraGroupModel implements ExtraGroupModel {
  const factory _ExtraGroupModel(
      {@JsonKey(name: '_id') required final String id,
      required final String name,
      required final ExtraType type,
      final bool allowMultiple,
      final List<ExtraItemModel> items}) = _$ExtraGroupModelImpl;

  factory _ExtraGroupModel.fromJson(Map<String, dynamic> json) =
      _$ExtraGroupModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String get name;
  @override
  ExtraType get type;
  @override
  bool get allowMultiple;
  @override
  List<ExtraItemModel> get items;
  @override
  @JsonKey(ignore: true)
  _$$ExtraGroupModelImplCopyWith<_$ExtraGroupModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
