// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'extra_selection_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ExtraSelectionModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  List<String> get items => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraSelectionModelCopyWith<ExtraSelectionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraSelectionModelCopyWith<$Res> {
  factory $ExtraSelectionModelCopyWith(
          ExtraSelectionModel value, $Res Function(ExtraSelectionModel) then) =
      _$ExtraSelectionModelCopyWithImpl<$Res, ExtraSelectionModel>;
  @useResult
  $Res call({@JsonKey(name: '_id') String id, List<String> items});
}

/// @nodoc
class _$ExtraSelectionModelCopyWithImpl<$Res, $Val extends ExtraSelectionModel>
    implements $ExtraSelectionModelCopyWith<$Res> {
  _$ExtraSelectionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExtraSelectionModelImplCopyWith<$Res>
    implements $ExtraSelectionModelCopyWith<$Res> {
  factory _$$ExtraSelectionModelImplCopyWith(_$ExtraSelectionModelImpl value,
          $Res Function(_$ExtraSelectionModelImpl) then) =
      __$$ExtraSelectionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: '_id') String id, List<String> items});
}

/// @nodoc
class __$$ExtraSelectionModelImplCopyWithImpl<$Res>
    extends _$ExtraSelectionModelCopyWithImpl<$Res, _$ExtraSelectionModelImpl>
    implements _$$ExtraSelectionModelImplCopyWith<$Res> {
  __$$ExtraSelectionModelImplCopyWithImpl(_$ExtraSelectionModelImpl _value,
      $Res Function(_$ExtraSelectionModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? items = null,
  }) {
    return _then(_$ExtraSelectionModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$ExtraSelectionModelImpl implements _ExtraSelectionModel {
  const _$ExtraSelectionModelImpl(
      {@JsonKey(name: '_id') required this.id,
      final List<String> items = const []})
      : _items = items;

  @override
  @JsonKey(name: '_id')
  final String id;
  final List<String> _items;
  @override
  @JsonKey()
  List<String> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'ExtraSelectionModel(id: $id, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExtraSelectionModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExtraSelectionModelImplCopyWith<_$ExtraSelectionModelImpl> get copyWith =>
      __$$ExtraSelectionModelImplCopyWithImpl<_$ExtraSelectionModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExtraSelectionModelImplToJson(
      this,
    );
  }
}

abstract class _ExtraSelectionModel implements ExtraSelectionModel {
  const factory _ExtraSelectionModel(
      {@JsonKey(name: '_id') required final String id,
      final List<String> items}) = _$ExtraSelectionModelImpl;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  List<String> get items;
  @override
  @JsonKey(ignore: true)
  _$$ExtraSelectionModelImplCopyWith<_$ExtraSelectionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
