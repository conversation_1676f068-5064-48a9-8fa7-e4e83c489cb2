import 'package:freezed_annotation/freezed_annotation.dart';

part 'extra_item_model.freezed.dart';
part 'extra_item_model.g.dart';

@freezed
class ExtraItemModel with _$ExtraItemModel {
  const factory ExtraItemModel({
    @JsonKey(name: '_id') required String id,
    required String name,
    @Default(0.0) double price,
  }) = _ExtraItemModel;

  factory ExtraItemModel.fromJson(Map<String, dynamic> json) => 
      _$ExtraItemModelFromJson(json);
}