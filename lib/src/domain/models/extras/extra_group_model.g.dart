// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'extra_group_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExtraGroupModelImpl _$$ExtraGroupModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ExtraGroupModelImpl(
      id: json['_id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ExtraTypeEnumMap, json['type']),
      allowMultiple: json['allowMultiple'] as bool? ?? false,
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => ExtraItemModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ExtraGroupModelImplToJson(
        _$ExtraGroupModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'name': instance.name,
      'type': _$ExtraTypeEnumMap[instance.type]!,
      'allowMultiple': instance.allowMultiple,
      'items': instance.items,
    };

const _$ExtraTypeEnumMap = {
  ExtraType.extras: 'extras',
  ExtraType.sauce: 'sauce',
  ExtraType.topping: 'topping',
};
