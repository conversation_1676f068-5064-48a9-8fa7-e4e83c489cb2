// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'special_extra_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SpecialExtraModel _$SpecialExtraModelFromJson(Map<String, dynamic> json) {
  return _SpecialExtraModel.fromJson(json);
}

/// @nodoc
mixin _$SpecialExtraModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  ExtraType get type => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialExtraModelCopyWith<SpecialExtraModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialExtraModelCopyWith<$Res> {
  factory $SpecialExtraModelCopyWith(
          SpecialExtraModel value, $Res Function(SpecialExtraModel) then) =
      _$SpecialExtraModelCopyWithImpl<$Res, SpecialExtraModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      ExtraType type,
      String name,
      double price});
}

/// @nodoc
class _$SpecialExtraModelCopyWithImpl<$Res, $Val extends SpecialExtraModel>
    implements $SpecialExtraModelCopyWith<$Res> {
  _$SpecialExtraModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? name = null,
    Object? price = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExtraType,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SpecialExtraModelImplCopyWith<$Res>
    implements $SpecialExtraModelCopyWith<$Res> {
  factory _$$SpecialExtraModelImplCopyWith(_$SpecialExtraModelImpl value,
          $Res Function(_$SpecialExtraModelImpl) then) =
      __$$SpecialExtraModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      ExtraType type,
      String name,
      double price});
}

/// @nodoc
class __$$SpecialExtraModelImplCopyWithImpl<$Res>
    extends _$SpecialExtraModelCopyWithImpl<$Res, _$SpecialExtraModelImpl>
    implements _$$SpecialExtraModelImplCopyWith<$Res> {
  __$$SpecialExtraModelImplCopyWithImpl(_$SpecialExtraModelImpl _value,
      $Res Function(_$SpecialExtraModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? name = null,
    Object? price = null,
  }) {
    return _then(_$SpecialExtraModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExtraType,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialExtraModelImpl implements _SpecialExtraModel {
  const _$SpecialExtraModelImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.type,
      required this.name,
      this.price = 0.0});

  factory _$SpecialExtraModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialExtraModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final ExtraType type;
  @override
  final String name;
  @override
  @JsonKey()
  final double price;

  @override
  String toString() {
    return 'SpecialExtraModel(id: $id, type: $type, name: $name, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialExtraModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, name, price);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialExtraModelImplCopyWith<_$SpecialExtraModelImpl> get copyWith =>
      __$$SpecialExtraModelImplCopyWithImpl<_$SpecialExtraModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialExtraModelImplToJson(
      this,
    );
  }
}

abstract class _SpecialExtraModel implements SpecialExtraModel {
  const factory _SpecialExtraModel(
      {@JsonKey(name: '_id') required final String id,
      required final ExtraType type,
      required final String name,
      final double price}) = _$SpecialExtraModelImpl;

  factory _SpecialExtraModel.fromJson(Map<String, dynamic> json) =
      _$SpecialExtraModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  ExtraType get type;
  @override
  String get name;
  @override
  double get price;
  @override
  @JsonKey(ignore: true)
  _$$SpecialExtraModelImplCopyWith<_$SpecialExtraModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
