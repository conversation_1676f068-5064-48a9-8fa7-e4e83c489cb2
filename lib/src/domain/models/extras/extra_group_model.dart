import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';

part 'extra_group_model.freezed.dart';
part 'extra_group_model.g.dart';

@freezed
class ExtraGroupModel with _$ExtraGroupModel {
  const factory ExtraGroupModel({
    @JsonKey(name: '_id') required String id,
    required String name,
    required ExtraType type,
    @Default(false) bool allowMultiple,
    @Default([]) List<ExtraItemModel> items,
  }) = _ExtraGroupModel;

  factory ExtraGroupModel.fromJson(Map<String, dynamic> json) => 
      _$ExtraGroupModelFromJson(json);
}