// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'extra_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExtraModelImpl _$$ExtraModelImplFromJson(Map<String, dynamic> json) =>
    _$ExtraModelImpl(
      id: json['_id'] as String,
      type: $enumDecode(_$ExtraTypeEnumMap, json['type']),
      name: json['name'] as String,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$ExtraModelImplToJson(_$ExtraModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'type': _$ExtraTypeEnumMap[instance.type]!,
      'name': instance.name,
      'price': instance.price,
    };

const _$ExtraTypeEnumMap = {
  ExtraType.extras: 'extras',
  ExtraType.sauce: 'sauce',
  ExtraType.topping: 'topping',
};
