import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';
import 'package:mutualz/src/domain/models/constants/order_type.dart';
import 'package:mutualz/src/domain/models/constants/service_type.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/orders/order_detail_model.dart';
import 'package:mutualz/src/domain/models/orders/order_status.dart';

part 'order_model.freezed.dart';
part 'order_model.g.dart';

@freezed
class OrderModel with _$OrderModel {
  const factory OrderModel({
    @JsonKey(name: '_id') required String id,
    required String orderNumber,
    required String restaurantId,
    RestaurantModel? restaurant,
    required String userId,
    required String tableNumber,
    @Default([]) List<OrderDetailModel> details,
    // Deprecated field
    @Default([]) List<String> discountIds,
    String? couponId,
    PaymentStatus? paymentStatus,
    OrderStatus? orderStatus,
    String? paymentId,
    ServiceType? orderType,
    @Default(false) bool isArchived,
    @Default(0.0) double couponDiscount,
    @Default(0.0) double memberDiscount,
    @Default(0.0) double taxAmount,
    @Default(0.0) double taxRate,
    @Default(0.0) double netAmount,
    @Default(0.0) double totalAmount,
    @Default(0.0) double totalAmountAfterDiscount,
    @Default([]) List<DietPreferencesModel> dietPreferences,
    required String createdAt,
    required String updatedAt,
  }) = _OrderModel;

  factory OrderModel.fromJson(Map<String, dynamic> json) => _$OrderModelFromJson(json);
}

extension OrderModelExtension on OrderModel {
  // double get totalAmount => details.fold(0.0, (total, detail) =>
  //   total + detail.items.fold(0.0, (total, item) => item.type == OrderType.special ?
  //     total + item.special!.price * item.quantity :
  //     total + item.dish!.price * item.quantity + item.extras.fold(0.0, (total, extra) =>
  //       total + extra.price * item.quantity)
  //   ));

  String get date => DateFormat('dd MMMM yyyy').format(DateTime.parse(updatedAt));
}