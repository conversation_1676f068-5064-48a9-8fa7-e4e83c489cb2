// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'special_order_component.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SpecialOrderComponentImpl _$$SpecialOrderComponentImplFromJson(
        Map<String, dynamic> json) =>
    _$SpecialOrderComponentImpl(
      componentId: json['componentId'] as String,
      component: SpecialInnerComponent.fromJson(
          json['component'] as Map<String, dynamic>),
      dish: json['dish'] == null
          ? null
          : SpecialDish.fromJson(json['dish'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SpecialOrderComponentImplToJson(
        _$SpecialOrderComponentImpl instance) =>
    <String, dynamic>{
      'componentId': instance.componentId,
      'component': instance.component,
      'dish': instance.dish,
    };
