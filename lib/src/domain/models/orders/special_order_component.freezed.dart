// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'special_order_component.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SpecialOrderComponent _$SpecialOrderComponentFromJson(
    Map<String, dynamic> json) {
  return _SpecialOrderComponent.fromJson(json);
}

/// @nodoc
mixin _$SpecialOrderComponent {
  String get componentId =>
      throw _privateConstructorUsedError; //required String dishId,
  SpecialInnerComponent get component => throw _privateConstructorUsedError;
  SpecialDish? get dish => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SpecialOrderComponentCopyWith<SpecialOrderComponent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SpecialOrderComponentCopyWith<$Res> {
  factory $SpecialOrderComponentCopyWith(SpecialOrderComponent value,
          $Res Function(SpecialOrderComponent) then) =
      _$SpecialOrderComponentCopyWithImpl<$Res, SpecialOrderComponent>;
  @useResult
  $Res call(
      {String componentId, SpecialInnerComponent component, SpecialDish? dish});

  $SpecialInnerComponentCopyWith<$Res> get component;
}

/// @nodoc
class _$SpecialOrderComponentCopyWithImpl<$Res,
        $Val extends SpecialOrderComponent>
    implements $SpecialOrderComponentCopyWith<$Res> {
  _$SpecialOrderComponentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? componentId = null,
    Object? component = null,
    Object? dish = freezed,
  }) {
    return _then(_value.copyWith(
      componentId: null == componentId
          ? _value.componentId
          : componentId // ignore: cast_nullable_to_non_nullable
              as String,
      component: null == component
          ? _value.component
          : component // ignore: cast_nullable_to_non_nullable
              as SpecialInnerComponent,
      dish: freezed == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as SpecialDish?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SpecialInnerComponentCopyWith<$Res> get component {
    return $SpecialInnerComponentCopyWith<$Res>(_value.component, (value) {
      return _then(_value.copyWith(component: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SpecialOrderComponentImplCopyWith<$Res>
    implements $SpecialOrderComponentCopyWith<$Res> {
  factory _$$SpecialOrderComponentImplCopyWith(
          _$SpecialOrderComponentImpl value,
          $Res Function(_$SpecialOrderComponentImpl) then) =
      __$$SpecialOrderComponentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String componentId, SpecialInnerComponent component, SpecialDish? dish});

  @override
  $SpecialInnerComponentCopyWith<$Res> get component;
}

/// @nodoc
class __$$SpecialOrderComponentImplCopyWithImpl<$Res>
    extends _$SpecialOrderComponentCopyWithImpl<$Res,
        _$SpecialOrderComponentImpl>
    implements _$$SpecialOrderComponentImplCopyWith<$Res> {
  __$$SpecialOrderComponentImplCopyWithImpl(_$SpecialOrderComponentImpl _value,
      $Res Function(_$SpecialOrderComponentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? componentId = null,
    Object? component = null,
    Object? dish = freezed,
  }) {
    return _then(_$SpecialOrderComponentImpl(
      componentId: null == componentId
          ? _value.componentId
          : componentId // ignore: cast_nullable_to_non_nullable
              as String,
      component: null == component
          ? _value.component
          : component // ignore: cast_nullable_to_non_nullable
              as SpecialInnerComponent,
      dish: freezed == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as SpecialDish?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SpecialOrderComponentImpl implements _SpecialOrderComponent {
  const _$SpecialOrderComponentImpl(
      {required this.componentId, required this.component, this.dish});

  factory _$SpecialOrderComponentImpl.fromJson(Map<String, dynamic> json) =>
      _$$SpecialOrderComponentImplFromJson(json);

  @override
  final String componentId;
//required String dishId,
  @override
  final SpecialInnerComponent component;
  @override
  final SpecialDish? dish;

  @override
  String toString() {
    return 'SpecialOrderComponent(componentId: $componentId, component: $component, dish: $dish)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpecialOrderComponentImpl &&
            (identical(other.componentId, componentId) ||
                other.componentId == componentId) &&
            (identical(other.component, component) ||
                other.component == component) &&
            (identical(other.dish, dish) || other.dish == dish));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, componentId, component, dish);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SpecialOrderComponentImplCopyWith<_$SpecialOrderComponentImpl>
      get copyWith => __$$SpecialOrderComponentImplCopyWithImpl<
          _$SpecialOrderComponentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SpecialOrderComponentImplToJson(
      this,
    );
  }
}

abstract class _SpecialOrderComponent implements SpecialOrderComponent {
  const factory _SpecialOrderComponent(
      {required final String componentId,
      required final SpecialInnerComponent component,
      final SpecialDish? dish}) = _$SpecialOrderComponentImpl;

  factory _SpecialOrderComponent.fromJson(Map<String, dynamic> json) =
      _$SpecialOrderComponentImpl.fromJson;

  @override
  String get componentId;
  @override //required String dishId,
  SpecialInnerComponent get component;
  @override
  SpecialDish? get dish;
  @override
  @JsonKey(ignore: true)
  _$$SpecialOrderComponentImplCopyWith<_$SpecialOrderComponentImpl>
      get copyWith => throw _privateConstructorUsedError;
}
