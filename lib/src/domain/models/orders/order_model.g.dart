// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OrderModelImpl _$$OrderModelImplFromJson(Map<String, dynamic> json) =>
    _$OrderModelImpl(
      id: json['_id'] as String,
      orderNumber: json['orderNumber'] as String,
      restaurantId: json['restaurantId'] as String,
      restaurant: json['restaurant'] == null
          ? null
          : RestaurantModel.fromJson(
              json['restaurant'] as Map<String, dynamic>),
      userId: json['userId'] as String,
      tableNumber: json['tableNumber'] as String,
      details: (json['details'] as List<dynamic>?)
              ?.map((e) => OrderDetailModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      discountIds: (json['discountIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      couponId: json['couponId'] as String?,
      paymentStatus:
          $enumDecodeNullable(_$PaymentStatusEnumMap, json['paymentStatus']),
      orderStatus:
          $enumDecodeNullable(_$OrderStatusEnumMap, json['orderStatus']),
      paymentId: json['paymentId'] as String?,
      orderType: $enumDecodeNullable(_$ServiceTypeEnumMap, json['orderType']),
      isArchived: json['isArchived'] as bool? ?? false,
      couponDiscount: (json['couponDiscount'] as num?)?.toDouble() ?? 0.0,
      memberDiscount: (json['memberDiscount'] as num?)?.toDouble() ?? 0.0,
      taxAmount: (json['taxAmount'] as num?)?.toDouble() ?? 0.0,
      taxRate: (json['taxRate'] as num?)?.toDouble() ?? 0.0,
      netAmount: (json['netAmount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      totalAmountAfterDiscount:
          (json['totalAmountAfterDiscount'] as num?)?.toDouble() ?? 0.0,
      dietPreferences: (json['dietPreferences'] as List<dynamic>?)
              ?.map((e) =>
                  DietPreferencesModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$$OrderModelImplToJson(_$OrderModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'orderNumber': instance.orderNumber,
      'restaurantId': instance.restaurantId,
      'restaurant': instance.restaurant,
      'userId': instance.userId,
      'tableNumber': instance.tableNumber,
      'details': instance.details,
      'discountIds': instance.discountIds,
      'couponId': instance.couponId,
      'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus],
      'orderStatus': _$OrderStatusEnumMap[instance.orderStatus],
      'paymentId': instance.paymentId,
      'orderType': _$ServiceTypeEnumMap[instance.orderType],
      'isArchived': instance.isArchived,
      'couponDiscount': instance.couponDiscount,
      'memberDiscount': instance.memberDiscount,
      'taxAmount': instance.taxAmount,
      'taxRate': instance.taxRate,
      'netAmount': instance.netAmount,
      'totalAmount': instance.totalAmount,
      'totalAmountAfterDiscount': instance.totalAmountAfterDiscount,
      'dietPreferences': instance.dietPreferences,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.paid: 'paid',
  PaymentStatus.unpaid: 'unpaid',
};

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.preparing: 'preparing',
  OrderStatus.completed: 'completed',
  OrderStatus.cancelled: 'cancelled',
};

const _$ServiceTypeEnumMap = {
  ServiceType.dineIn: 'dine_in',
  ServiceType.takeaway: 'takeaway',
};
