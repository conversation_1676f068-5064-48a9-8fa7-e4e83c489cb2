// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderModel _$OrderModelFromJson(Map<String, dynamic> json) {
  return _OrderModel.fromJson(json);
}

/// @nodoc
mixin _$OrderModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String get orderNumber => throw _privateConstructorUsedError;
  String get restaurantId => throw _privateConstructorUsedError;
  RestaurantModel? get restaurant => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get tableNumber => throw _privateConstructorUsedError;
  List<OrderDetailModel> get details =>
      throw _privateConstructorUsedError; // Deprecated field
  List<String> get discountIds => throw _privateConstructorUsedError;
  String? get couponId => throw _privateConstructorUsedError;
  PaymentStatus? get paymentStatus => throw _privateConstructorUsedError;
  OrderStatus? get orderStatus => throw _privateConstructorUsedError;
  String? get paymentId => throw _privateConstructorUsedError;
  ServiceType? get orderType => throw _privateConstructorUsedError;
  bool get isArchived => throw _privateConstructorUsedError;
  double get couponDiscount => throw _privateConstructorUsedError;
  double get memberDiscount => throw _privateConstructorUsedError;
  double get taxAmount => throw _privateConstructorUsedError;
  double get taxRate => throw _privateConstructorUsedError;
  double get netAmount => throw _privateConstructorUsedError;
  double get totalAmount => throw _privateConstructorUsedError;
  double get totalAmountAfterDiscount => throw _privateConstructorUsedError;
  List<DietPreferencesModel> get dietPreferences =>
      throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderModelCopyWith<OrderModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderModelCopyWith<$Res> {
  factory $OrderModelCopyWith(
          OrderModel value, $Res Function(OrderModel) then) =
      _$OrderModelCopyWithImpl<$Res, OrderModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String orderNumber,
      String restaurantId,
      RestaurantModel? restaurant,
      String userId,
      String tableNumber,
      List<OrderDetailModel> details,
      List<String> discountIds,
      String? couponId,
      PaymentStatus? paymentStatus,
      OrderStatus? orderStatus,
      String? paymentId,
      ServiceType? orderType,
      bool isArchived,
      double couponDiscount,
      double memberDiscount,
      double taxAmount,
      double taxRate,
      double netAmount,
      double totalAmount,
      double totalAmountAfterDiscount,
      List<DietPreferencesModel> dietPreferences,
      String createdAt,
      String updatedAt});

  $RestaurantModelCopyWith<$Res>? get restaurant;
}

/// @nodoc
class _$OrderModelCopyWithImpl<$Res, $Val extends OrderModel>
    implements $OrderModelCopyWith<$Res> {
  _$OrderModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderNumber = null,
    Object? restaurantId = null,
    Object? restaurant = freezed,
    Object? userId = null,
    Object? tableNumber = null,
    Object? details = null,
    Object? discountIds = null,
    Object? couponId = freezed,
    Object? paymentStatus = freezed,
    Object? orderStatus = freezed,
    Object? paymentId = freezed,
    Object? orderType = freezed,
    Object? isArchived = null,
    Object? couponDiscount = null,
    Object? memberDiscount = null,
    Object? taxAmount = null,
    Object? taxRate = null,
    Object? netAmount = null,
    Object? totalAmount = null,
    Object? totalAmountAfterDiscount = null,
    Object? dietPreferences = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      orderNumber: null == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String,
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      restaurant: freezed == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as RestaurantModel?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      details: null == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailModel>,
      discountIds: null == discountIds
          ? _value.discountIds
          : discountIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderStatus?,
      paymentId: freezed == paymentId
          ? _value.paymentId
          : paymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as ServiceType?,
      isArchived: null == isArchived
          ? _value.isArchived
          : isArchived // ignore: cast_nullable_to_non_nullable
              as bool,
      couponDiscount: null == couponDiscount
          ? _value.couponDiscount
          : couponDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      memberDiscount: null == memberDiscount
          ? _value.memberDiscount
          : memberDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      taxAmount: null == taxAmount
          ? _value.taxAmount
          : taxAmount // ignore: cast_nullable_to_non_nullable
              as double,
      taxRate: null == taxRate
          ? _value.taxRate
          : taxRate // ignore: cast_nullable_to_non_nullable
              as double,
      netAmount: null == netAmount
          ? _value.netAmount
          : netAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmountAfterDiscount: null == totalAmountAfterDiscount
          ? _value.totalAmountAfterDiscount
          : totalAmountAfterDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      dietPreferences: null == dietPreferences
          ? _value.dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<DietPreferencesModel>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RestaurantModelCopyWith<$Res>? get restaurant {
    if (_value.restaurant == null) {
      return null;
    }

    return $RestaurantModelCopyWith<$Res>(_value.restaurant!, (value) {
      return _then(_value.copyWith(restaurant: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderModelImplCopyWith<$Res>
    implements $OrderModelCopyWith<$Res> {
  factory _$$OrderModelImplCopyWith(
          _$OrderModelImpl value, $Res Function(_$OrderModelImpl) then) =
      __$$OrderModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String orderNumber,
      String restaurantId,
      RestaurantModel? restaurant,
      String userId,
      String tableNumber,
      List<OrderDetailModel> details,
      List<String> discountIds,
      String? couponId,
      PaymentStatus? paymentStatus,
      OrderStatus? orderStatus,
      String? paymentId,
      ServiceType? orderType,
      bool isArchived,
      double couponDiscount,
      double memberDiscount,
      double taxAmount,
      double taxRate,
      double netAmount,
      double totalAmount,
      double totalAmountAfterDiscount,
      List<DietPreferencesModel> dietPreferences,
      String createdAt,
      String updatedAt});

  @override
  $RestaurantModelCopyWith<$Res>? get restaurant;
}

/// @nodoc
class __$$OrderModelImplCopyWithImpl<$Res>
    extends _$OrderModelCopyWithImpl<$Res, _$OrderModelImpl>
    implements _$$OrderModelImplCopyWith<$Res> {
  __$$OrderModelImplCopyWithImpl(
      _$OrderModelImpl _value, $Res Function(_$OrderModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? orderNumber = null,
    Object? restaurantId = null,
    Object? restaurant = freezed,
    Object? userId = null,
    Object? tableNumber = null,
    Object? details = null,
    Object? discountIds = null,
    Object? couponId = freezed,
    Object? paymentStatus = freezed,
    Object? orderStatus = freezed,
    Object? paymentId = freezed,
    Object? orderType = freezed,
    Object? isArchived = null,
    Object? couponDiscount = null,
    Object? memberDiscount = null,
    Object? taxAmount = null,
    Object? taxRate = null,
    Object? netAmount = null,
    Object? totalAmount = null,
    Object? totalAmountAfterDiscount = null,
    Object? dietPreferences = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$OrderModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      orderNumber: null == orderNumber
          ? _value.orderNumber
          : orderNumber // ignore: cast_nullable_to_non_nullable
              as String,
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      restaurant: freezed == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as RestaurantModel?,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      details: null == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as List<OrderDetailModel>,
      discountIds: null == discountIds
          ? _value._discountIds
          : discountIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderStatus?,
      paymentId: freezed == paymentId
          ? _value.paymentId
          : paymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderType: freezed == orderType
          ? _value.orderType
          : orderType // ignore: cast_nullable_to_non_nullable
              as ServiceType?,
      isArchived: null == isArchived
          ? _value.isArchived
          : isArchived // ignore: cast_nullable_to_non_nullable
              as bool,
      couponDiscount: null == couponDiscount
          ? _value.couponDiscount
          : couponDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      memberDiscount: null == memberDiscount
          ? _value.memberDiscount
          : memberDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      taxAmount: null == taxAmount
          ? _value.taxAmount
          : taxAmount // ignore: cast_nullable_to_non_nullable
              as double,
      taxRate: null == taxRate
          ? _value.taxRate
          : taxRate // ignore: cast_nullable_to_non_nullable
              as double,
      netAmount: null == netAmount
          ? _value.netAmount
          : netAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      totalAmountAfterDiscount: null == totalAmountAfterDiscount
          ? _value.totalAmountAfterDiscount
          : totalAmountAfterDiscount // ignore: cast_nullable_to_non_nullable
              as double,
      dietPreferences: null == dietPreferences
          ? _value._dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<DietPreferencesModel>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderModelImpl implements _OrderModel {
  const _$OrderModelImpl(
      {@JsonKey(name: '_id') required this.id,
      required this.orderNumber,
      required this.restaurantId,
      this.restaurant,
      required this.userId,
      required this.tableNumber,
      final List<OrderDetailModel> details = const [],
      final List<String> discountIds = const [],
      this.couponId,
      this.paymentStatus,
      this.orderStatus,
      this.paymentId,
      this.orderType,
      this.isArchived = false,
      this.couponDiscount = 0.0,
      this.memberDiscount = 0.0,
      this.taxAmount = 0.0,
      this.taxRate = 0.0,
      this.netAmount = 0.0,
      this.totalAmount = 0.0,
      this.totalAmountAfterDiscount = 0.0,
      final List<DietPreferencesModel> dietPreferences = const [],
      required this.createdAt,
      required this.updatedAt})
      : _details = details,
        _discountIds = discountIds,
        _dietPreferences = dietPreferences;

  factory _$OrderModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String orderNumber;
  @override
  final String restaurantId;
  @override
  final RestaurantModel? restaurant;
  @override
  final String userId;
  @override
  final String tableNumber;
  final List<OrderDetailModel> _details;
  @override
  @JsonKey()
  List<OrderDetailModel> get details {
    if (_details is EqualUnmodifiableListView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_details);
  }

// Deprecated field
  final List<String> _discountIds;
// Deprecated field
  @override
  @JsonKey()
  List<String> get discountIds {
    if (_discountIds is EqualUnmodifiableListView) return _discountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_discountIds);
  }

  @override
  final String? couponId;
  @override
  final PaymentStatus? paymentStatus;
  @override
  final OrderStatus? orderStatus;
  @override
  final String? paymentId;
  @override
  final ServiceType? orderType;
  @override
  @JsonKey()
  final bool isArchived;
  @override
  @JsonKey()
  final double couponDiscount;
  @override
  @JsonKey()
  final double memberDiscount;
  @override
  @JsonKey()
  final double taxAmount;
  @override
  @JsonKey()
  final double taxRate;
  @override
  @JsonKey()
  final double netAmount;
  @override
  @JsonKey()
  final double totalAmount;
  @override
  @JsonKey()
  final double totalAmountAfterDiscount;
  final List<DietPreferencesModel> _dietPreferences;
  @override
  @JsonKey()
  List<DietPreferencesModel> get dietPreferences {
    if (_dietPreferences is EqualUnmodifiableListView) return _dietPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dietPreferences);
  }

  @override
  final String createdAt;
  @override
  final String updatedAt;

  @override
  String toString() {
    return 'OrderModel(id: $id, orderNumber: $orderNumber, restaurantId: $restaurantId, restaurant: $restaurant, userId: $userId, tableNumber: $tableNumber, details: $details, discountIds: $discountIds, couponId: $couponId, paymentStatus: $paymentStatus, orderStatus: $orderStatus, paymentId: $paymentId, orderType: $orderType, isArchived: $isArchived, couponDiscount: $couponDiscount, memberDiscount: $memberDiscount, taxAmount: $taxAmount, taxRate: $taxRate, netAmount: $netAmount, totalAmount: $totalAmount, totalAmountAfterDiscount: $totalAmountAfterDiscount, dietPreferences: $dietPreferences, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderNumber, orderNumber) ||
                other.orderNumber == orderNumber) &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.restaurant, restaurant) ||
                other.restaurant == restaurant) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.tableNumber, tableNumber) ||
                other.tableNumber == tableNumber) &&
            const DeepCollectionEquality().equals(other._details, _details) &&
            const DeepCollectionEquality()
                .equals(other._discountIds, _discountIds) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.orderType, orderType) ||
                other.orderType == orderType) &&
            (identical(other.isArchived, isArchived) ||
                other.isArchived == isArchived) &&
            (identical(other.couponDiscount, couponDiscount) ||
                other.couponDiscount == couponDiscount) &&
            (identical(other.memberDiscount, memberDiscount) ||
                other.memberDiscount == memberDiscount) &&
            (identical(other.taxAmount, taxAmount) ||
                other.taxAmount == taxAmount) &&
            (identical(other.taxRate, taxRate) || other.taxRate == taxRate) &&
            (identical(other.netAmount, netAmount) ||
                other.netAmount == netAmount) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(
                    other.totalAmountAfterDiscount, totalAmountAfterDiscount) ||
                other.totalAmountAfterDiscount == totalAmountAfterDiscount) &&
            const DeepCollectionEquality()
                .equals(other._dietPreferences, _dietPreferences) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        orderNumber,
        restaurantId,
        restaurant,
        userId,
        tableNumber,
        const DeepCollectionEquality().hash(_details),
        const DeepCollectionEquality().hash(_discountIds),
        couponId,
        paymentStatus,
        orderStatus,
        paymentId,
        orderType,
        isArchived,
        couponDiscount,
        memberDiscount,
        taxAmount,
        taxRate,
        netAmount,
        totalAmount,
        totalAmountAfterDiscount,
        const DeepCollectionEquality().hash(_dietPreferences),
        createdAt,
        updatedAt
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      __$$OrderModelImplCopyWithImpl<_$OrderModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderModelImplToJson(
      this,
    );
  }
}

abstract class _OrderModel implements OrderModel {
  const factory _OrderModel(
      {@JsonKey(name: '_id') required final String id,
      required final String orderNumber,
      required final String restaurantId,
      final RestaurantModel? restaurant,
      required final String userId,
      required final String tableNumber,
      final List<OrderDetailModel> details,
      final List<String> discountIds,
      final String? couponId,
      final PaymentStatus? paymentStatus,
      final OrderStatus? orderStatus,
      final String? paymentId,
      final ServiceType? orderType,
      final bool isArchived,
      final double couponDiscount,
      final double memberDiscount,
      final double taxAmount,
      final double taxRate,
      final double netAmount,
      final double totalAmount,
      final double totalAmountAfterDiscount,
      final List<DietPreferencesModel> dietPreferences,
      required final String createdAt,
      required final String updatedAt}) = _$OrderModelImpl;

  factory _OrderModel.fromJson(Map<String, dynamic> json) =
      _$OrderModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String get orderNumber;
  @override
  String get restaurantId;
  @override
  RestaurantModel? get restaurant;
  @override
  String get userId;
  @override
  String get tableNumber;
  @override
  List<OrderDetailModel> get details;
  @override // Deprecated field
  List<String> get discountIds;
  @override
  String? get couponId;
  @override
  PaymentStatus? get paymentStatus;
  @override
  OrderStatus? get orderStatus;
  @override
  String? get paymentId;
  @override
  ServiceType? get orderType;
  @override
  bool get isArchived;
  @override
  double get couponDiscount;
  @override
  double get memberDiscount;
  @override
  double get taxAmount;
  @override
  double get taxRate;
  @override
  double get netAmount;
  @override
  double get totalAmount;
  @override
  double get totalAmountAfterDiscount;
  @override
  List<DietPreferencesModel> get dietPreferences;
  @override
  String get createdAt;
  @override
  String get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$OrderModelImplCopyWith<_$OrderModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
