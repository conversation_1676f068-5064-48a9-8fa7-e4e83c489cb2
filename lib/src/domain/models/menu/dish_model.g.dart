// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dish_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DishModelImpl _$$DishModelImplFromJson(Map<String, dynamic> json) =>
    _$DishModelImpl(
      id: json['_id'] as String,
      restaurantId: json['restaurantId'] as String?,
      ownerId: json['ownerId'] as String?,
      menuId: json['menuId'] as String?,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      mainCategory: json['mainCategory'] as String,
      subcategory: json['subcategory'] as String,
      subcategoryId: json['subcategoryId'] as String? ?? '',
      price: (json['price'] as num).toDouble(),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      coverUrl: json['coverUrl'] as String?,
      dietPreferences: (json['dietPreferences'] as List<dynamic>?)
              ?.map((e) =>
                  DietPreferencesModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      extras: (json['extras'] as List<dynamic>?)
              ?.map((e) => ExtraGroupModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      position: json['position'] as int? ?? 999,
    );

Map<String, dynamic> _$$DishModelImplToJson(_$DishModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'restaurantId': instance.restaurantId,
      'ownerId': instance.ownerId,
      'menuId': instance.menuId,
      'name': instance.name,
      'description': instance.description,
      'mainCategory': instance.mainCategory,
      'subcategory': instance.subcategory,
      'subcategoryId': instance.subcategoryId,
      'price': instance.price,
      'rating': instance.rating,
      'coverUrl': instance.coverUrl,
      'dietPreferences': instance.dietPreferences,
      'extras': instance.extras,
      'position': instance.position,
    };
