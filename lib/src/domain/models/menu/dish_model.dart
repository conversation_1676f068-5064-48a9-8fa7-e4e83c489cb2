import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/diet_preferences/diet_preferences_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:uuid/uuid.dart';

part 'dish_model.freezed.dart';
part 'dish_model.g.dart';

@freezed
class DishModel with _$DishModel {
  const factory DishModel({
    @JsonKey(name: '_id') required String id,
    String? restaurantId,
    String? ownerId,
    String? menuId,
    required String name,
    @Default('') String description,
    required String mainCategory,
    required String subcategory,
    @Default('') String subcategoryId,
    required double price,
    @Default(0.0) double rating,
    required String? coverUrl,
    @Default([]) List<DietPreferencesModel> dietPreferences,
    @Default([]) List<ExtraGroupModel> extras,
    @Default(999) int position,
  }) = _DishModel;

  factory DishModel.fromJson(Map<String, dynamic> json) => _$DishModelFromJson(json);
}

extension DishModelX on DishModel {
  bool get isSpecial => mainCategory == 'specials';
  
  // Methods for grouped extras
  List<ExtraGroupModel> get sauceGroups => extras.where((g) => g.type == ExtraType.sauce).toList();
  List<ExtraGroupModel> get toppingGroups => extras.where((g) => g.type == ExtraType.topping).toList();
  List<ExtraGroupModel> get extrasGroups => extras.where((g) => g.type == ExtraType.extras).toList();
  
  // Helper to check if dish has extras
  bool get hasExtras => extras.isNotEmpty;
}
