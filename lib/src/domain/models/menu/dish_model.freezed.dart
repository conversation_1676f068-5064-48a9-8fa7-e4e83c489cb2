// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dish_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DishModel _$DishModelFromJson(Map<String, dynamic> json) {
  return _DishModel.fromJson(json);
}

/// @nodoc
mixin _$DishModel {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  String? get restaurantId => throw _privateConstructorUsedError;
  String? get ownerId => throw _privateConstructorUsedError;
  String? get menuId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get mainCategory => throw _privateConstructorUsedError;
  String get subcategory => throw _privateConstructorUsedError;
  String get subcategoryId => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  String? get coverUrl => throw _privateConstructorUsedError;
  List<DietPreferencesModel> get dietPreferences =>
      throw _privateConstructorUsedError;
  List<ExtraGroupModel> get extras => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DishModelCopyWith<DishModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DishModelCopyWith<$Res> {
  factory $DishModelCopyWith(DishModel value, $Res Function(DishModel) then) =
      _$DishModelCopyWithImpl<$Res, DishModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String? restaurantId,
      String? ownerId,
      String? menuId,
      String name,
      String description,
      String mainCategory,
      String subcategory,
      String subcategoryId,
      double price,
      double rating,
      String? coverUrl,
      List<DietPreferencesModel> dietPreferences,
      List<ExtraGroupModel> extras,
      int position});
}

/// @nodoc
class _$DishModelCopyWithImpl<$Res, $Val extends DishModel>
    implements $DishModelCopyWith<$Res> {
  _$DishModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? restaurantId = freezed,
    Object? ownerId = freezed,
    Object? menuId = freezed,
    Object? name = null,
    Object? description = null,
    Object? mainCategory = null,
    Object? subcategory = null,
    Object? subcategoryId = null,
    Object? price = null,
    Object? rating = null,
    Object? coverUrl = freezed,
    Object? dietPreferences = null,
    Object? extras = null,
    Object? position = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      restaurantId: freezed == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      menuId: freezed == menuId
          ? _value.menuId
          : menuId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      subcategory: null == subcategory
          ? _value.subcategory
          : subcategory // ignore: cast_nullable_to_non_nullable
              as String,
      subcategoryId: null == subcategoryId
          ? _value.subcategoryId
          : subcategoryId // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double,
      coverUrl: freezed == coverUrl
          ? _value.coverUrl
          : coverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dietPreferences: null == dietPreferences
          ? _value.dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<DietPreferencesModel>,
      extras: null == extras
          ? _value.extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraGroupModel>,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DishModelImplCopyWith<$Res>
    implements $DishModelCopyWith<$Res> {
  factory _$$DishModelImplCopyWith(
          _$DishModelImpl value, $Res Function(_$DishModelImpl) then) =
      __$$DishModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id,
      String? restaurantId,
      String? ownerId,
      String? menuId,
      String name,
      String description,
      String mainCategory,
      String subcategory,
      String subcategoryId,
      double price,
      double rating,
      String? coverUrl,
      List<DietPreferencesModel> dietPreferences,
      List<ExtraGroupModel> extras,
      int position});
}

/// @nodoc
class __$$DishModelImplCopyWithImpl<$Res>
    extends _$DishModelCopyWithImpl<$Res, _$DishModelImpl>
    implements _$$DishModelImplCopyWith<$Res> {
  __$$DishModelImplCopyWithImpl(
      _$DishModelImpl _value, $Res Function(_$DishModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? restaurantId = freezed,
    Object? ownerId = freezed,
    Object? menuId = freezed,
    Object? name = null,
    Object? description = null,
    Object? mainCategory = null,
    Object? subcategory = null,
    Object? subcategoryId = null,
    Object? price = null,
    Object? rating = null,
    Object? coverUrl = freezed,
    Object? dietPreferences = null,
    Object? extras = null,
    Object? position = null,
  }) {
    return _then(_$DishModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      restaurantId: freezed == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String?,
      ownerId: freezed == ownerId
          ? _value.ownerId
          : ownerId // ignore: cast_nullable_to_non_nullable
              as String?,
      menuId: freezed == menuId
          ? _value.menuId
          : menuId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      mainCategory: null == mainCategory
          ? _value.mainCategory
          : mainCategory // ignore: cast_nullable_to_non_nullable
              as String,
      subcategory: null == subcategory
          ? _value.subcategory
          : subcategory // ignore: cast_nullable_to_non_nullable
              as String,
      subcategoryId: null == subcategoryId
          ? _value.subcategoryId
          : subcategoryId // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double,
      coverUrl: freezed == coverUrl
          ? _value.coverUrl
          : coverUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dietPreferences: null == dietPreferences
          ? _value._dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<DietPreferencesModel>,
      extras: null == extras
          ? _value._extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraGroupModel>,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DishModelImpl implements _DishModel {
  const _$DishModelImpl(
      {@JsonKey(name: '_id') required this.id,
      this.restaurantId,
      this.ownerId,
      this.menuId,
      required this.name,
      this.description = '',
      required this.mainCategory,
      required this.subcategory,
      this.subcategoryId = '',
      required this.price,
      this.rating = 0.0,
      required this.coverUrl,
      final List<DietPreferencesModel> dietPreferences = const [],
      final List<ExtraGroupModel> extras = const [],
      this.position = 999})
      : _dietPreferences = dietPreferences,
        _extras = extras;

  factory _$DishModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DishModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String id;
  @override
  final String? restaurantId;
  @override
  final String? ownerId;
  @override
  final String? menuId;
  @override
  final String name;
  @override
  @JsonKey()
  final String description;
  @override
  final String mainCategory;
  @override
  final String subcategory;
  @override
  @JsonKey()
  final String subcategoryId;
  @override
  final double price;
  @override
  @JsonKey()
  final double rating;
  @override
  final String? coverUrl;
  final List<DietPreferencesModel> _dietPreferences;
  @override
  @JsonKey()
  List<DietPreferencesModel> get dietPreferences {
    if (_dietPreferences is EqualUnmodifiableListView) return _dietPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dietPreferences);
  }

  final List<ExtraGroupModel> _extras;
  @override
  @JsonKey()
  List<ExtraGroupModel> get extras {
    if (_extras is EqualUnmodifiableListView) return _extras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extras);
  }

  @override
  @JsonKey()
  final int position;

  @override
  String toString() {
    return 'DishModel(id: $id, restaurantId: $restaurantId, ownerId: $ownerId, menuId: $menuId, name: $name, description: $description, mainCategory: $mainCategory, subcategory: $subcategory, subcategoryId: $subcategoryId, price: $price, rating: $rating, coverUrl: $coverUrl, dietPreferences: $dietPreferences, extras: $extras, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DishModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.ownerId, ownerId) || other.ownerId == ownerId) &&
            (identical(other.menuId, menuId) || other.menuId == menuId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.mainCategory, mainCategory) ||
                other.mainCategory == mainCategory) &&
            (identical(other.subcategory, subcategory) ||
                other.subcategory == subcategory) &&
            (identical(other.subcategoryId, subcategoryId) ||
                other.subcategoryId == subcategoryId) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.coverUrl, coverUrl) ||
                other.coverUrl == coverUrl) &&
            const DeepCollectionEquality()
                .equals(other._dietPreferences, _dietPreferences) &&
            const DeepCollectionEquality().equals(other._extras, _extras) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      restaurantId,
      ownerId,
      menuId,
      name,
      description,
      mainCategory,
      subcategory,
      subcategoryId,
      price,
      rating,
      coverUrl,
      const DeepCollectionEquality().hash(_dietPreferences),
      const DeepCollectionEquality().hash(_extras),
      position);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DishModelImplCopyWith<_$DishModelImpl> get copyWith =>
      __$$DishModelImplCopyWithImpl<_$DishModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DishModelImplToJson(
      this,
    );
  }
}

abstract class _DishModel implements DishModel {
  const factory _DishModel(
      {@JsonKey(name: '_id') required final String id,
      final String? restaurantId,
      final String? ownerId,
      final String? menuId,
      required final String name,
      final String description,
      required final String mainCategory,
      required final String subcategory,
      final String subcategoryId,
      required final double price,
      final double rating,
      required final String? coverUrl,
      final List<DietPreferencesModel> dietPreferences,
      final List<ExtraGroupModel> extras,
      final int position}) = _$DishModelImpl;

  factory _DishModel.fromJson(Map<String, dynamic> json) =
      _$DishModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  String? get restaurantId;
  @override
  String? get ownerId;
  @override
  String? get menuId;
  @override
  String get name;
  @override
  String get description;
  @override
  String get mainCategory;
  @override
  String get subcategory;
  @override
  String get subcategoryId;
  @override
  double get price;
  @override
  double get rating;
  @override
  String? get coverUrl;
  @override
  List<DietPreferencesModel> get dietPreferences;
  @override
  List<ExtraGroupModel> get extras;
  @override
  int get position;
  @override
  @JsonKey(ignore: true)
  _$$DishModelImplCopyWith<_$DishModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
