import 'package:freezed_annotation/freezed_annotation.dart';

part 'tutorial_states_update_response.freezed.dart';
part 'tutorial_states_update_response.g.dart';

@freezed
class TutorialStatesUpdateResponse with _$TutorialStatesUpdateResponse {
  const factory TutorialStatesUpdateResponse({
    required bool success,
    required String message,
  }) = _TutorialStatesUpdateResponse;

  factory TutorialStatesUpdateResponse.fromJson(Map<String, dynamic> json) =>
      _$TutorialStatesUpdateResponseFromJson(json);
}