// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tutorial_states_update_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TutorialStatesUpdateResponse _$TutorialStatesUpdateResponseFromJson(
    Map<String, dynamic> json) {
  return _TutorialStatesUpdateResponse.fromJson(json);
}

/// @nodoc
mixin _$TutorialStatesUpdateResponse {
  bool get success => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TutorialStatesUpdateResponseCopyWith<TutorialStatesUpdateResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TutorialStatesUpdateResponseCopyWith<$Res> {
  factory $TutorialStatesUpdateResponseCopyWith(
          TutorialStatesUpdateResponse value,
          $Res Function(TutorialStatesUpdateResponse) then) =
      _$TutorialStatesUpdateResponseCopyWithImpl<$Res,
          TutorialStatesUpdateResponse>;
  @useResult
  $Res call({bool success, String message});
}

/// @nodoc
class _$TutorialStatesUpdateResponseCopyWithImpl<$Res,
        $Val extends TutorialStatesUpdateResponse>
    implements $TutorialStatesUpdateResponseCopyWith<$Res> {
  _$TutorialStatesUpdateResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TutorialStatesUpdateResponseImplCopyWith<$Res>
    implements $TutorialStatesUpdateResponseCopyWith<$Res> {
  factory _$$TutorialStatesUpdateResponseImplCopyWith(
          _$TutorialStatesUpdateResponseImpl value,
          $Res Function(_$TutorialStatesUpdateResponseImpl) then) =
      __$$TutorialStatesUpdateResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, String message});
}

/// @nodoc
class __$$TutorialStatesUpdateResponseImplCopyWithImpl<$Res>
    extends _$TutorialStatesUpdateResponseCopyWithImpl<$Res,
        _$TutorialStatesUpdateResponseImpl>
    implements _$$TutorialStatesUpdateResponseImplCopyWith<$Res> {
  __$$TutorialStatesUpdateResponseImplCopyWithImpl(
      _$TutorialStatesUpdateResponseImpl _value,
      $Res Function(_$TutorialStatesUpdateResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
  }) {
    return _then(_$TutorialStatesUpdateResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TutorialStatesUpdateResponseImpl
    implements _TutorialStatesUpdateResponse {
  const _$TutorialStatesUpdateResponseImpl(
      {required this.success, required this.message});

  factory _$TutorialStatesUpdateResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$TutorialStatesUpdateResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String message;

  @override
  String toString() {
    return 'TutorialStatesUpdateResponse(success: $success, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TutorialStatesUpdateResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, success, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TutorialStatesUpdateResponseImplCopyWith<
          _$TutorialStatesUpdateResponseImpl>
      get copyWith => __$$TutorialStatesUpdateResponseImplCopyWithImpl<
          _$TutorialStatesUpdateResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TutorialStatesUpdateResponseImplToJson(
      this,
    );
  }
}

abstract class _TutorialStatesUpdateResponse
    implements TutorialStatesUpdateResponse {
  const factory _TutorialStatesUpdateResponse(
      {required final bool success,
      required final String message}) = _$TutorialStatesUpdateResponseImpl;

  factory _TutorialStatesUpdateResponse.fromJson(Map<String, dynamic> json) =
      _$TutorialStatesUpdateResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$TutorialStatesUpdateResponseImplCopyWith<
          _$TutorialStatesUpdateResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
