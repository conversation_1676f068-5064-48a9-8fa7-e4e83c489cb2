// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tutorial_states_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TutorialStatesResponse _$TutorialStatesResponseFromJson(
    Map<String, dynamic> json) {
  return _TutorialStatesResponse.fromJson(json);
}

/// @nodoc
mixin _$TutorialStatesResponse {
  bool get success => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  TutorialStatesData get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TutorialStatesResponseCopyWith<TutorialStatesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TutorialStatesResponseCopyWith<$Res> {
  factory $TutorialStatesResponseCopyWith(TutorialStatesResponse value,
          $Res Function(TutorialStatesResponse) then) =
      _$TutorialStatesResponseCopyWithImpl<$Res, TutorialStatesResponse>;
  @useResult
  $Res call({bool success, String message, TutorialStatesData data});

  $TutorialStatesDataCopyWith<$Res> get data;
}

/// @nodoc
class _$TutorialStatesResponseCopyWithImpl<$Res,
        $Val extends TutorialStatesResponse>
    implements $TutorialStatesResponseCopyWith<$Res> {
  _$TutorialStatesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as TutorialStatesData,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TutorialStatesDataCopyWith<$Res> get data {
    return $TutorialStatesDataCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TutorialStatesResponseImplCopyWith<$Res>
    implements $TutorialStatesResponseCopyWith<$Res> {
  factory _$$TutorialStatesResponseImplCopyWith(
          _$TutorialStatesResponseImpl value,
          $Res Function(_$TutorialStatesResponseImpl) then) =
      __$$TutorialStatesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, String message, TutorialStatesData data});

  @override
  $TutorialStatesDataCopyWith<$Res> get data;
}

/// @nodoc
class __$$TutorialStatesResponseImplCopyWithImpl<$Res>
    extends _$TutorialStatesResponseCopyWithImpl<$Res,
        _$TutorialStatesResponseImpl>
    implements _$$TutorialStatesResponseImplCopyWith<$Res> {
  __$$TutorialStatesResponseImplCopyWithImpl(
      _$TutorialStatesResponseImpl _value,
      $Res Function(_$TutorialStatesResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? data = null,
  }) {
    return _then(_$TutorialStatesResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as TutorialStatesData,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TutorialStatesResponseImpl implements _TutorialStatesResponse {
  const _$TutorialStatesResponseImpl(
      {required this.success, required this.message, required this.data});

  factory _$TutorialStatesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TutorialStatesResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String message;
  @override
  final TutorialStatesData data;

  @override
  String toString() {
    return 'TutorialStatesResponse(success: $success, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TutorialStatesResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, success, message, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TutorialStatesResponseImplCopyWith<_$TutorialStatesResponseImpl>
      get copyWith => __$$TutorialStatesResponseImplCopyWithImpl<
          _$TutorialStatesResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TutorialStatesResponseImplToJson(
      this,
    );
  }
}

abstract class _TutorialStatesResponse implements TutorialStatesResponse {
  const factory _TutorialStatesResponse(
      {required final bool success,
      required final String message,
      required final TutorialStatesData data}) = _$TutorialStatesResponseImpl;

  factory _TutorialStatesResponse.fromJson(Map<String, dynamic> json) =
      _$TutorialStatesResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String get message;
  @override
  TutorialStatesData get data;
  @override
  @JsonKey(ignore: true)
  _$$TutorialStatesResponseImplCopyWith<_$TutorialStatesResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TutorialStatesData _$TutorialStatesDataFromJson(Map<String, dynamic> json) {
  return _TutorialStatesData.fromJson(json);
}

/// @nodoc
mixin _$TutorialStatesData {
  @JsonKey(fromJson: _boolFromJson)
  bool get mapTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get feedTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get gastroProfileTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get userAndSettingsTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get mapFilterTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderCartButtonTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderCartItemsTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderFirstLoyaltyPointsTutorial =>
      throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderPaymentOptionsTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderPointsUsageTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderTableNumberTutorial => throw _privateConstructorUsedError;
  @JsonKey(fromJson: _boolFromJson)
  bool get orderUsedCouponsTutorial => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TutorialStatesDataCopyWith<TutorialStatesData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TutorialStatesDataCopyWith<$Res> {
  factory $TutorialStatesDataCopyWith(
          TutorialStatesData value, $Res Function(TutorialStatesData) then) =
      _$TutorialStatesDataCopyWithImpl<$Res, TutorialStatesData>;
  @useResult
  $Res call(
      {@JsonKey(fromJson: _boolFromJson) bool mapTutorial,
      @JsonKey(fromJson: _boolFromJson) bool feedTutorial,
      @JsonKey(fromJson: _boolFromJson) bool gastroProfileTutorial,
      @JsonKey(fromJson: _boolFromJson) bool userAndSettingsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool mapFilterTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderCartButtonTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderCartItemsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderFirstLoyaltyPointsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderPaymentOptionsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderPointsUsageTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderTableNumberTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderUsedCouponsTutorial});
}

/// @nodoc
class _$TutorialStatesDataCopyWithImpl<$Res, $Val extends TutorialStatesData>
    implements $TutorialStatesDataCopyWith<$Res> {
  _$TutorialStatesDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapTutorial = null,
    Object? feedTutorial = null,
    Object? gastroProfileTutorial = null,
    Object? userAndSettingsTutorial = null,
    Object? mapFilterTutorial = null,
    Object? orderCartButtonTutorial = null,
    Object? orderCartItemsTutorial = null,
    Object? orderFirstLoyaltyPointsTutorial = null,
    Object? orderPaymentOptionsTutorial = null,
    Object? orderPointsUsageTutorial = null,
    Object? orderTableNumberTutorial = null,
    Object? orderUsedCouponsTutorial = null,
  }) {
    return _then(_value.copyWith(
      mapTutorial: null == mapTutorial
          ? _value.mapTutorial
          : mapTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      feedTutorial: null == feedTutorial
          ? _value.feedTutorial
          : feedTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      gastroProfileTutorial: null == gastroProfileTutorial
          ? _value.gastroProfileTutorial
          : gastroProfileTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      userAndSettingsTutorial: null == userAndSettingsTutorial
          ? _value.userAndSettingsTutorial
          : userAndSettingsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      mapFilterTutorial: null == mapFilterTutorial
          ? _value.mapFilterTutorial
          : mapFilterTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderCartButtonTutorial: null == orderCartButtonTutorial
          ? _value.orderCartButtonTutorial
          : orderCartButtonTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderCartItemsTutorial: null == orderCartItemsTutorial
          ? _value.orderCartItemsTutorial
          : orderCartItemsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderFirstLoyaltyPointsTutorial: null == orderFirstLoyaltyPointsTutorial
          ? _value.orderFirstLoyaltyPointsTutorial
          : orderFirstLoyaltyPointsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderPaymentOptionsTutorial: null == orderPaymentOptionsTutorial
          ? _value.orderPaymentOptionsTutorial
          : orderPaymentOptionsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderPointsUsageTutorial: null == orderPointsUsageTutorial
          ? _value.orderPointsUsageTutorial
          : orderPointsUsageTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderTableNumberTutorial: null == orderTableNumberTutorial
          ? _value.orderTableNumberTutorial
          : orderTableNumberTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderUsedCouponsTutorial: null == orderUsedCouponsTutorial
          ? _value.orderUsedCouponsTutorial
          : orderUsedCouponsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TutorialStatesDataImplCopyWith<$Res>
    implements $TutorialStatesDataCopyWith<$Res> {
  factory _$$TutorialStatesDataImplCopyWith(_$TutorialStatesDataImpl value,
          $Res Function(_$TutorialStatesDataImpl) then) =
      __$$TutorialStatesDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(fromJson: _boolFromJson) bool mapTutorial,
      @JsonKey(fromJson: _boolFromJson) bool feedTutorial,
      @JsonKey(fromJson: _boolFromJson) bool gastroProfileTutorial,
      @JsonKey(fromJson: _boolFromJson) bool userAndSettingsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool mapFilterTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderCartButtonTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderCartItemsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderFirstLoyaltyPointsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderPaymentOptionsTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderPointsUsageTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderTableNumberTutorial,
      @JsonKey(fromJson: _boolFromJson) bool orderUsedCouponsTutorial});
}

/// @nodoc
class __$$TutorialStatesDataImplCopyWithImpl<$Res>
    extends _$TutorialStatesDataCopyWithImpl<$Res, _$TutorialStatesDataImpl>
    implements _$$TutorialStatesDataImplCopyWith<$Res> {
  __$$TutorialStatesDataImplCopyWithImpl(_$TutorialStatesDataImpl _value,
      $Res Function(_$TutorialStatesDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapTutorial = null,
    Object? feedTutorial = null,
    Object? gastroProfileTutorial = null,
    Object? userAndSettingsTutorial = null,
    Object? mapFilterTutorial = null,
    Object? orderCartButtonTutorial = null,
    Object? orderCartItemsTutorial = null,
    Object? orderFirstLoyaltyPointsTutorial = null,
    Object? orderPaymentOptionsTutorial = null,
    Object? orderPointsUsageTutorial = null,
    Object? orderTableNumberTutorial = null,
    Object? orderUsedCouponsTutorial = null,
  }) {
    return _then(_$TutorialStatesDataImpl(
      mapTutorial: null == mapTutorial
          ? _value.mapTutorial
          : mapTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      feedTutorial: null == feedTutorial
          ? _value.feedTutorial
          : feedTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      gastroProfileTutorial: null == gastroProfileTutorial
          ? _value.gastroProfileTutorial
          : gastroProfileTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      userAndSettingsTutorial: null == userAndSettingsTutorial
          ? _value.userAndSettingsTutorial
          : userAndSettingsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      mapFilterTutorial: null == mapFilterTutorial
          ? _value.mapFilterTutorial
          : mapFilterTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderCartButtonTutorial: null == orderCartButtonTutorial
          ? _value.orderCartButtonTutorial
          : orderCartButtonTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderCartItemsTutorial: null == orderCartItemsTutorial
          ? _value.orderCartItemsTutorial
          : orderCartItemsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderFirstLoyaltyPointsTutorial: null == orderFirstLoyaltyPointsTutorial
          ? _value.orderFirstLoyaltyPointsTutorial
          : orderFirstLoyaltyPointsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderPaymentOptionsTutorial: null == orderPaymentOptionsTutorial
          ? _value.orderPaymentOptionsTutorial
          : orderPaymentOptionsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderPointsUsageTutorial: null == orderPointsUsageTutorial
          ? _value.orderPointsUsageTutorial
          : orderPointsUsageTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderTableNumberTutorial: null == orderTableNumberTutorial
          ? _value.orderTableNumberTutorial
          : orderTableNumberTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
      orderUsedCouponsTutorial: null == orderUsedCouponsTutorial
          ? _value.orderUsedCouponsTutorial
          : orderUsedCouponsTutorial // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TutorialStatesDataImpl extends _TutorialStatesData {
  const _$TutorialStatesDataImpl(
      {@JsonKey(fromJson: _boolFromJson) this.mapTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.feedTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.gastroProfileTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.userAndSettingsTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.mapFilterTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.orderCartButtonTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.orderCartItemsTutorial = false,
      @JsonKey(fromJson: _boolFromJson)
      this.orderFirstLoyaltyPointsTutorial = false,
      @JsonKey(fromJson: _boolFromJson)
      this.orderPaymentOptionsTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.orderPointsUsageTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.orderTableNumberTutorial = false,
      @JsonKey(fromJson: _boolFromJson) this.orderUsedCouponsTutorial = false})
      : super._();

  factory _$TutorialStatesDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$TutorialStatesDataImplFromJson(json);

  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool mapTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool feedTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool gastroProfileTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool userAndSettingsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool mapFilterTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderCartButtonTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderCartItemsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderFirstLoyaltyPointsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderPaymentOptionsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderPointsUsageTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderTableNumberTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  final bool orderUsedCouponsTutorial;

  @override
  String toString() {
    return 'TutorialStatesData(mapTutorial: $mapTutorial, feedTutorial: $feedTutorial, gastroProfileTutorial: $gastroProfileTutorial, userAndSettingsTutorial: $userAndSettingsTutorial, mapFilterTutorial: $mapFilterTutorial, orderCartButtonTutorial: $orderCartButtonTutorial, orderCartItemsTutorial: $orderCartItemsTutorial, orderFirstLoyaltyPointsTutorial: $orderFirstLoyaltyPointsTutorial, orderPaymentOptionsTutorial: $orderPaymentOptionsTutorial, orderPointsUsageTutorial: $orderPointsUsageTutorial, orderTableNumberTutorial: $orderTableNumberTutorial, orderUsedCouponsTutorial: $orderUsedCouponsTutorial)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TutorialStatesDataImpl &&
            (identical(other.mapTutorial, mapTutorial) ||
                other.mapTutorial == mapTutorial) &&
            (identical(other.feedTutorial, feedTutorial) ||
                other.feedTutorial == feedTutorial) &&
            (identical(other.gastroProfileTutorial, gastroProfileTutorial) ||
                other.gastroProfileTutorial == gastroProfileTutorial) &&
            (identical(other.userAndSettingsTutorial, userAndSettingsTutorial) ||
                other.userAndSettingsTutorial == userAndSettingsTutorial) &&
            (identical(other.mapFilterTutorial, mapFilterTutorial) ||
                other.mapFilterTutorial == mapFilterTutorial) &&
            (identical(
                    other.orderCartButtonTutorial, orderCartButtonTutorial) ||
                other.orderCartButtonTutorial == orderCartButtonTutorial) &&
            (identical(other.orderCartItemsTutorial, orderCartItemsTutorial) ||
                other.orderCartItemsTutorial == orderCartItemsTutorial) &&
            (identical(other.orderFirstLoyaltyPointsTutorial,
                    orderFirstLoyaltyPointsTutorial) ||
                other.orderFirstLoyaltyPointsTutorial ==
                    orderFirstLoyaltyPointsTutorial) &&
            (identical(other.orderPaymentOptionsTutorial,
                    orderPaymentOptionsTutorial) ||
                other.orderPaymentOptionsTutorial ==
                    orderPaymentOptionsTutorial) &&
            (identical(
                    other.orderPointsUsageTutorial, orderPointsUsageTutorial) ||
                other.orderPointsUsageTutorial == orderPointsUsageTutorial) &&
            (identical(
                    other.orderTableNumberTutorial, orderTableNumberTutorial) ||
                other.orderTableNumberTutorial == orderTableNumberTutorial) &&
            (identical(
                    other.orderUsedCouponsTutorial, orderUsedCouponsTutorial) ||
                other.orderUsedCouponsTutorial == orderUsedCouponsTutorial));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      mapTutorial,
      feedTutorial,
      gastroProfileTutorial,
      userAndSettingsTutorial,
      mapFilterTutorial,
      orderCartButtonTutorial,
      orderCartItemsTutorial,
      orderFirstLoyaltyPointsTutorial,
      orderPaymentOptionsTutorial,
      orderPointsUsageTutorial,
      orderTableNumberTutorial,
      orderUsedCouponsTutorial);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TutorialStatesDataImplCopyWith<_$TutorialStatesDataImpl> get copyWith =>
      __$$TutorialStatesDataImplCopyWithImpl<_$TutorialStatesDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TutorialStatesDataImplToJson(
      this,
    );
  }
}

abstract class _TutorialStatesData extends TutorialStatesData {
  const factory _TutorialStatesData(
      {@JsonKey(fromJson: _boolFromJson) final bool mapTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool feedTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool gastroProfileTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool userAndSettingsTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool mapFilterTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool orderCartButtonTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool orderCartItemsTutorial,
      @JsonKey(fromJson: _boolFromJson)
      final bool orderFirstLoyaltyPointsTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool orderPaymentOptionsTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool orderPointsUsageTutorial,
      @JsonKey(fromJson: _boolFromJson) final bool orderTableNumberTutorial,
      @JsonKey(fromJson: _boolFromJson)
      final bool orderUsedCouponsTutorial}) = _$TutorialStatesDataImpl;
  const _TutorialStatesData._() : super._();

  factory _TutorialStatesData.fromJson(Map<String, dynamic> json) =
      _$TutorialStatesDataImpl.fromJson;

  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get mapTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get feedTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get gastroProfileTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get userAndSettingsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get mapFilterTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderCartButtonTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderCartItemsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderFirstLoyaltyPointsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderPaymentOptionsTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderPointsUsageTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderTableNumberTutorial;
  @override
  @JsonKey(fromJson: _boolFromJson)
  bool get orderUsedCouponsTutorial;
  @override
  @JsonKey(ignore: true)
  _$$TutorialStatesDataImplCopyWith<_$TutorialStatesDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
