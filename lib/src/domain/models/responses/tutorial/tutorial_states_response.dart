import 'package:freezed_annotation/freezed_annotation.dart';

part 'tutorial_states_response.freezed.dart';
part 'tutorial_states_response.g.dart';

enum TutorialKey {
  mapTutorial('mapTutorial'),
  feedTutorial('feedTutorial'),
  gastroProfileTutorial('gastroProfileTutorial'),
  userAndSettingsTutorial('userAndSettingsTutorial'),
  mapFilterTutorial('mapFilterTutorial'),
  orderCartButtonTutorial('orderCartButtonTutorial'),
  orderCartItemsTutorial('orderCartItemsTutorial'),
  orderFirstLoyaltyPointsTutorial('orderFirstLoyaltyPointsTutorial'),
  orderPaymentOptionsTutorial('orderPaymentOptionsTutorial'),
  orderPointsUsageTutorial('orderPointsUsageTutorial'),
  orderTableNumberTutorial('orderTableNumberTutorial'),
  orderUsedCouponsTutorial('orderUsedCouponsTutorial');

  const TutorialKey(this.key);
  final String key;
}

@freezed
class TutorialStatesResponse with _$TutorialStatesResponse {
  const factory TutorialStatesResponse({
    required bool success,
    required String message,
    required TutorialStatesData data,
  }) = _TutorialStatesResponse;

  factory TutorialStatesResponse.fromJson(Map<String, dynamic> json) =>
      _$TutorialStatesResponseFromJson(json);
}

@freezed
class TutorialStatesData with _$TutorialStatesData {
  const factory TutorialStatesData({
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool mapTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool feedTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool gastroProfileTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool userAndSettingsTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool mapFilterTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderCartButtonTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderCartItemsTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderFirstLoyaltyPointsTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderPaymentOptionsTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderPointsUsageTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderTableNumberTutorial,
    @JsonKey(fromJson: _boolFromJson) @Default(false) bool orderUsedCouponsTutorial,
  }) = _TutorialStatesData;

  factory TutorialStatesData.fromJson(Map<String, dynamic> json) =>
      _$TutorialStatesDataFromJson(json);
      
  const TutorialStatesData._();
  
  TutorialStatesData updateWith(Map<String, bool> updates) {
    return copyWith(
      mapTutorial: updates['mapTutorial'] ?? mapTutorial,
      feedTutorial: updates['feedTutorial'] ?? feedTutorial,
      gastroProfileTutorial: updates['gastroProfileTutorial'] ?? gastroProfileTutorial,
      userAndSettingsTutorial: updates['userAndSettingsTutorial'] ?? userAndSettingsTutorial,
      mapFilterTutorial: updates['mapFilterTutorial'] ?? mapFilterTutorial,
      orderCartButtonTutorial: updates['orderCartButtonTutorial'] ?? orderCartButtonTutorial,
      orderCartItemsTutorial: updates['orderCartItemsTutorial'] ?? orderCartItemsTutorial,
      orderFirstLoyaltyPointsTutorial: updates['orderFirstLoyaltyPointsTutorial'] ?? orderFirstLoyaltyPointsTutorial,
      orderPaymentOptionsTutorial: updates['orderPaymentOptionsTutorial'] ?? orderPaymentOptionsTutorial,
      orderPointsUsageTutorial: updates['orderPointsUsageTutorial'] ?? orderPointsUsageTutorial,
      orderTableNumberTutorial: updates['orderTableNumberTutorial'] ?? orderTableNumberTutorial,
      orderUsedCouponsTutorial: updates['orderUsedCouponsTutorial'] ?? orderUsedCouponsTutorial,
    );
  }
}

bool _boolFromJson(dynamic value) => value as bool? ?? false;