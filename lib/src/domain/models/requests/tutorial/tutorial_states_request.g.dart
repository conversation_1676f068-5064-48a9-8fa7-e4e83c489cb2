// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tutorial_states_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TutorialStatesRequestImpl _$$TutorialStatesRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$TutorialStatesRequestImpl(
      mapTutorial: json['mapTutorial'] as bool?,
      feedTutorial: json['feedTutorial'] as bool?,
      gastroProfileTutorial: json['gastroProfileTutorial'] as bool?,
      userAndSettingsTutorial: json['userAndSettingsTutorial'] as bool?,
      mapFilterTutorial: json['mapFilterTutorial'] as bool?,
      orderCartButtonTutorial: json['orderCartButtonTutorial'] as bool?,
      orderCartItemsTutorial: json['orderCartItemsTutorial'] as bool?,
      orderFirstLoyaltyPointsTutorial:
          json['orderFirstLoyaltyPointsTutorial'] as bool?,
      orderPaymentOptionsTutorial: json['orderPaymentOptionsTutorial'] as bool?,
      orderPointsUsageTutorial: json['orderPointsUsageTutorial'] as bool?,
      orderTableNumberTutorial: json['orderTableNumberTutorial'] as bool?,
      orderUsedCouponsTutorial: json['orderUsedCouponsTutorial'] as bool?,
    );

Map<String, dynamic> _$$TutorialStatesRequestImplToJson(
        _$TutorialStatesRequestImpl instance) =>
    <String, dynamic>{
      'mapTutorial': instance.mapTutorial,
      'feedTutorial': instance.feedTutorial,
      'gastroProfileTutorial': instance.gastroProfileTutorial,
      'userAndSettingsTutorial': instance.userAndSettingsTutorial,
      'mapFilterTutorial': instance.mapFilterTutorial,
      'orderCartButtonTutorial': instance.orderCartButtonTutorial,
      'orderCartItemsTutorial': instance.orderCartItemsTutorial,
      'orderFirstLoyaltyPointsTutorial':
          instance.orderFirstLoyaltyPointsTutorial,
      'orderPaymentOptionsTutorial': instance.orderPaymentOptionsTutorial,
      'orderPointsUsageTutorial': instance.orderPointsUsageTutorial,
      'orderTableNumberTutorial': instance.orderTableNumberTutorial,
      'orderUsedCouponsTutorial': instance.orderUsedCouponsTutorial,
    };
