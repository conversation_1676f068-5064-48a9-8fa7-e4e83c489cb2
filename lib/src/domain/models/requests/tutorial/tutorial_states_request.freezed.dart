// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tutorial_states_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TutorialStatesRequest _$TutorialStatesRequestFromJson(
    Map<String, dynamic> json) {
  return _TutorialStatesRequest.fromJson(json);
}

/// @nodoc
mixin _$TutorialStatesRequest {
  bool? get mapTutorial => throw _privateConstructorUsedError;
  bool? get feedTutorial => throw _privateConstructorUsedError;
  bool? get gastroProfileTutorial => throw _privateConstructorUsedError;
  bool? get userAndSettingsTutorial => throw _privateConstructorUsedError;
  bool? get mapFilterTutorial => throw _privateConstructorUsedError;
  bool? get orderCartButtonTutorial => throw _privateConstructorUsedError;
  bool? get orderCartItemsTutorial => throw _privateConstructorUsedError;
  bool? get orderFirstLoyaltyPointsTutorial =>
      throw _privateConstructorUsedError;
  bool? get orderPaymentOptionsTutorial => throw _privateConstructorUsedError;
  bool? get orderPointsUsageTutorial => throw _privateConstructorUsedError;
  bool? get orderTableNumberTutorial => throw _privateConstructorUsedError;
  bool? get orderUsedCouponsTutorial => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TutorialStatesRequestCopyWith<TutorialStatesRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TutorialStatesRequestCopyWith<$Res> {
  factory $TutorialStatesRequestCopyWith(TutorialStatesRequest value,
          $Res Function(TutorialStatesRequest) then) =
      _$TutorialStatesRequestCopyWithImpl<$Res, TutorialStatesRequest>;
  @useResult
  $Res call(
      {bool? mapTutorial,
      bool? feedTutorial,
      bool? gastroProfileTutorial,
      bool? userAndSettingsTutorial,
      bool? mapFilterTutorial,
      bool? orderCartButtonTutorial,
      bool? orderCartItemsTutorial,
      bool? orderFirstLoyaltyPointsTutorial,
      bool? orderPaymentOptionsTutorial,
      bool? orderPointsUsageTutorial,
      bool? orderTableNumberTutorial,
      bool? orderUsedCouponsTutorial});
}

/// @nodoc
class _$TutorialStatesRequestCopyWithImpl<$Res,
        $Val extends TutorialStatesRequest>
    implements $TutorialStatesRequestCopyWith<$Res> {
  _$TutorialStatesRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapTutorial = freezed,
    Object? feedTutorial = freezed,
    Object? gastroProfileTutorial = freezed,
    Object? userAndSettingsTutorial = freezed,
    Object? mapFilterTutorial = freezed,
    Object? orderCartButtonTutorial = freezed,
    Object? orderCartItemsTutorial = freezed,
    Object? orderFirstLoyaltyPointsTutorial = freezed,
    Object? orderPaymentOptionsTutorial = freezed,
    Object? orderPointsUsageTutorial = freezed,
    Object? orderTableNumberTutorial = freezed,
    Object? orderUsedCouponsTutorial = freezed,
  }) {
    return _then(_value.copyWith(
      mapTutorial: freezed == mapTutorial
          ? _value.mapTutorial
          : mapTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      feedTutorial: freezed == feedTutorial
          ? _value.feedTutorial
          : feedTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      gastroProfileTutorial: freezed == gastroProfileTutorial
          ? _value.gastroProfileTutorial
          : gastroProfileTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      userAndSettingsTutorial: freezed == userAndSettingsTutorial
          ? _value.userAndSettingsTutorial
          : userAndSettingsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      mapFilterTutorial: freezed == mapFilterTutorial
          ? _value.mapFilterTutorial
          : mapFilterTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderCartButtonTutorial: freezed == orderCartButtonTutorial
          ? _value.orderCartButtonTutorial
          : orderCartButtonTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderCartItemsTutorial: freezed == orderCartItemsTutorial
          ? _value.orderCartItemsTutorial
          : orderCartItemsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderFirstLoyaltyPointsTutorial: freezed ==
              orderFirstLoyaltyPointsTutorial
          ? _value.orderFirstLoyaltyPointsTutorial
          : orderFirstLoyaltyPointsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderPaymentOptionsTutorial: freezed == orderPaymentOptionsTutorial
          ? _value.orderPaymentOptionsTutorial
          : orderPaymentOptionsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderPointsUsageTutorial: freezed == orderPointsUsageTutorial
          ? _value.orderPointsUsageTutorial
          : orderPointsUsageTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderTableNumberTutorial: freezed == orderTableNumberTutorial
          ? _value.orderTableNumberTutorial
          : orderTableNumberTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderUsedCouponsTutorial: freezed == orderUsedCouponsTutorial
          ? _value.orderUsedCouponsTutorial
          : orderUsedCouponsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TutorialStatesRequestImplCopyWith<$Res>
    implements $TutorialStatesRequestCopyWith<$Res> {
  factory _$$TutorialStatesRequestImplCopyWith(
          _$TutorialStatesRequestImpl value,
          $Res Function(_$TutorialStatesRequestImpl) then) =
      __$$TutorialStatesRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? mapTutorial,
      bool? feedTutorial,
      bool? gastroProfileTutorial,
      bool? userAndSettingsTutorial,
      bool? mapFilterTutorial,
      bool? orderCartButtonTutorial,
      bool? orderCartItemsTutorial,
      bool? orderFirstLoyaltyPointsTutorial,
      bool? orderPaymentOptionsTutorial,
      bool? orderPointsUsageTutorial,
      bool? orderTableNumberTutorial,
      bool? orderUsedCouponsTutorial});
}

/// @nodoc
class __$$TutorialStatesRequestImplCopyWithImpl<$Res>
    extends _$TutorialStatesRequestCopyWithImpl<$Res,
        _$TutorialStatesRequestImpl>
    implements _$$TutorialStatesRequestImplCopyWith<$Res> {
  __$$TutorialStatesRequestImplCopyWithImpl(_$TutorialStatesRequestImpl _value,
      $Res Function(_$TutorialStatesRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mapTutorial = freezed,
    Object? feedTutorial = freezed,
    Object? gastroProfileTutorial = freezed,
    Object? userAndSettingsTutorial = freezed,
    Object? mapFilterTutorial = freezed,
    Object? orderCartButtonTutorial = freezed,
    Object? orderCartItemsTutorial = freezed,
    Object? orderFirstLoyaltyPointsTutorial = freezed,
    Object? orderPaymentOptionsTutorial = freezed,
    Object? orderPointsUsageTutorial = freezed,
    Object? orderTableNumberTutorial = freezed,
    Object? orderUsedCouponsTutorial = freezed,
  }) {
    return _then(_$TutorialStatesRequestImpl(
      mapTutorial: freezed == mapTutorial
          ? _value.mapTutorial
          : mapTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      feedTutorial: freezed == feedTutorial
          ? _value.feedTutorial
          : feedTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      gastroProfileTutorial: freezed == gastroProfileTutorial
          ? _value.gastroProfileTutorial
          : gastroProfileTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      userAndSettingsTutorial: freezed == userAndSettingsTutorial
          ? _value.userAndSettingsTutorial
          : userAndSettingsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      mapFilterTutorial: freezed == mapFilterTutorial
          ? _value.mapFilterTutorial
          : mapFilterTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderCartButtonTutorial: freezed == orderCartButtonTutorial
          ? _value.orderCartButtonTutorial
          : orderCartButtonTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderCartItemsTutorial: freezed == orderCartItemsTutorial
          ? _value.orderCartItemsTutorial
          : orderCartItemsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderFirstLoyaltyPointsTutorial: freezed ==
              orderFirstLoyaltyPointsTutorial
          ? _value.orderFirstLoyaltyPointsTutorial
          : orderFirstLoyaltyPointsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderPaymentOptionsTutorial: freezed == orderPaymentOptionsTutorial
          ? _value.orderPaymentOptionsTutorial
          : orderPaymentOptionsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderPointsUsageTutorial: freezed == orderPointsUsageTutorial
          ? _value.orderPointsUsageTutorial
          : orderPointsUsageTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderTableNumberTutorial: freezed == orderTableNumberTutorial
          ? _value.orderTableNumberTutorial
          : orderTableNumberTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
      orderUsedCouponsTutorial: freezed == orderUsedCouponsTutorial
          ? _value.orderUsedCouponsTutorial
          : orderUsedCouponsTutorial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TutorialStatesRequestImpl extends _TutorialStatesRequest {
  const _$TutorialStatesRequestImpl(
      {this.mapTutorial,
      this.feedTutorial,
      this.gastroProfileTutorial,
      this.userAndSettingsTutorial,
      this.mapFilterTutorial,
      this.orderCartButtonTutorial,
      this.orderCartItemsTutorial,
      this.orderFirstLoyaltyPointsTutorial,
      this.orderPaymentOptionsTutorial,
      this.orderPointsUsageTutorial,
      this.orderTableNumberTutorial,
      this.orderUsedCouponsTutorial})
      : super._();

  factory _$TutorialStatesRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TutorialStatesRequestImplFromJson(json);

  @override
  final bool? mapTutorial;
  @override
  final bool? feedTutorial;
  @override
  final bool? gastroProfileTutorial;
  @override
  final bool? userAndSettingsTutorial;
  @override
  final bool? mapFilterTutorial;
  @override
  final bool? orderCartButtonTutorial;
  @override
  final bool? orderCartItemsTutorial;
  @override
  final bool? orderFirstLoyaltyPointsTutorial;
  @override
  final bool? orderPaymentOptionsTutorial;
  @override
  final bool? orderPointsUsageTutorial;
  @override
  final bool? orderTableNumberTutorial;
  @override
  final bool? orderUsedCouponsTutorial;

  @override
  String toString() {
    return 'TutorialStatesRequest(mapTutorial: $mapTutorial, feedTutorial: $feedTutorial, gastroProfileTutorial: $gastroProfileTutorial, userAndSettingsTutorial: $userAndSettingsTutorial, mapFilterTutorial: $mapFilterTutorial, orderCartButtonTutorial: $orderCartButtonTutorial, orderCartItemsTutorial: $orderCartItemsTutorial, orderFirstLoyaltyPointsTutorial: $orderFirstLoyaltyPointsTutorial, orderPaymentOptionsTutorial: $orderPaymentOptionsTutorial, orderPointsUsageTutorial: $orderPointsUsageTutorial, orderTableNumberTutorial: $orderTableNumberTutorial, orderUsedCouponsTutorial: $orderUsedCouponsTutorial)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TutorialStatesRequestImpl &&
            (identical(other.mapTutorial, mapTutorial) ||
                other.mapTutorial == mapTutorial) &&
            (identical(other.feedTutorial, feedTutorial) ||
                other.feedTutorial == feedTutorial) &&
            (identical(other.gastroProfileTutorial, gastroProfileTutorial) ||
                other.gastroProfileTutorial == gastroProfileTutorial) &&
            (identical(other.userAndSettingsTutorial, userAndSettingsTutorial) ||
                other.userAndSettingsTutorial == userAndSettingsTutorial) &&
            (identical(other.mapFilterTutorial, mapFilterTutorial) ||
                other.mapFilterTutorial == mapFilterTutorial) &&
            (identical(
                    other.orderCartButtonTutorial, orderCartButtonTutorial) ||
                other.orderCartButtonTutorial == orderCartButtonTutorial) &&
            (identical(other.orderCartItemsTutorial, orderCartItemsTutorial) ||
                other.orderCartItemsTutorial == orderCartItemsTutorial) &&
            (identical(other.orderFirstLoyaltyPointsTutorial,
                    orderFirstLoyaltyPointsTutorial) ||
                other.orderFirstLoyaltyPointsTutorial ==
                    orderFirstLoyaltyPointsTutorial) &&
            (identical(other.orderPaymentOptionsTutorial,
                    orderPaymentOptionsTutorial) ||
                other.orderPaymentOptionsTutorial ==
                    orderPaymentOptionsTutorial) &&
            (identical(
                    other.orderPointsUsageTutorial, orderPointsUsageTutorial) ||
                other.orderPointsUsageTutorial == orderPointsUsageTutorial) &&
            (identical(
                    other.orderTableNumberTutorial, orderTableNumberTutorial) ||
                other.orderTableNumberTutorial == orderTableNumberTutorial) &&
            (identical(
                    other.orderUsedCouponsTutorial, orderUsedCouponsTutorial) ||
                other.orderUsedCouponsTutorial == orderUsedCouponsTutorial));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      mapTutorial,
      feedTutorial,
      gastroProfileTutorial,
      userAndSettingsTutorial,
      mapFilterTutorial,
      orderCartButtonTutorial,
      orderCartItemsTutorial,
      orderFirstLoyaltyPointsTutorial,
      orderPaymentOptionsTutorial,
      orderPointsUsageTutorial,
      orderTableNumberTutorial,
      orderUsedCouponsTutorial);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TutorialStatesRequestImplCopyWith<_$TutorialStatesRequestImpl>
      get copyWith => __$$TutorialStatesRequestImplCopyWithImpl<
          _$TutorialStatesRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TutorialStatesRequestImplToJson(
      this,
    );
  }
}

abstract class _TutorialStatesRequest extends TutorialStatesRequest {
  const factory _TutorialStatesRequest(
      {final bool? mapTutorial,
      final bool? feedTutorial,
      final bool? gastroProfileTutorial,
      final bool? userAndSettingsTutorial,
      final bool? mapFilterTutorial,
      final bool? orderCartButtonTutorial,
      final bool? orderCartItemsTutorial,
      final bool? orderFirstLoyaltyPointsTutorial,
      final bool? orderPaymentOptionsTutorial,
      final bool? orderPointsUsageTutorial,
      final bool? orderTableNumberTutorial,
      final bool? orderUsedCouponsTutorial}) = _$TutorialStatesRequestImpl;
  const _TutorialStatesRequest._() : super._();

  factory _TutorialStatesRequest.fromJson(Map<String, dynamic> json) =
      _$TutorialStatesRequestImpl.fromJson;

  @override
  bool? get mapTutorial;
  @override
  bool? get feedTutorial;
  @override
  bool? get gastroProfileTutorial;
  @override
  bool? get userAndSettingsTutorial;
  @override
  bool? get mapFilterTutorial;
  @override
  bool? get orderCartButtonTutorial;
  @override
  bool? get orderCartItemsTutorial;
  @override
  bool? get orderFirstLoyaltyPointsTutorial;
  @override
  bool? get orderPaymentOptionsTutorial;
  @override
  bool? get orderPointsUsageTutorial;
  @override
  bool? get orderTableNumberTutorial;
  @override
  bool? get orderUsedCouponsTutorial;
  @override
  @JsonKey(ignore: true)
  _$$TutorialStatesRequestImplCopyWith<_$TutorialStatesRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
