import 'package:freezed_annotation/freezed_annotation.dart';

import '../../responses/tutorial/tutorial_states_response.dart';

part 'tutorial_states_request.freezed.dart';
part 'tutorial_states_request.g.dart';

@freezed
class TutorialStatesRequest with _$TutorialStatesRequest {
  const factory TutorialStatesRequest({
    bool? mapTutorial,
    bool? feedTutorial,
    bool? gastroProfileTutorial,
    bool? userAndSettingsTutorial,
    bool? mapFilterTutorial,
    bool? orderCartButtonTutorial,
    bool? orderCartItemsTutorial,
    bool? orderFirstLoyaltyPointsTutorial,
    bool? orderPaymentOptionsTutorial,
    bool? orderPointsUsageTutorial,
    bool? orderTableNumberTutorial,
    bool? orderUsedCouponsTutorial,
  }) = _TutorialStatesRequest;

  factory TutorialStatesRequest.fromJson(Map<String, dynamic> json) =>
      _$TutorialStatesRequestFromJson(json);

  const TutorialStatesRequest._();

  factory TutorialStatesRequest.updateFromStates(
    TutorialStatesData states,
    Map<String, bool> updates,
  ) {
    return TutorialStatesRequest(
      mapTutorial: updates['mapTutorial'] ?? states.mapTutorial,
      feedTutorial: updates['feedTutorial'] ?? states.feedTutorial,
      gastroProfileTutorial: updates['gastroProfileTutorial'] ?? states.gastroProfileTutorial,
      userAndSettingsTutorial: updates['userAndSettingsTutorial'] ?? states.userAndSettingsTutorial,
      mapFilterTutorial: updates['mapFilterTutorial'] ?? states.mapFilterTutorial,
      orderCartButtonTutorial: updates['orderCartButtonTutorial'] ?? states.orderCartButtonTutorial,
      orderCartItemsTutorial: updates['orderCartItemsTutorial'] ?? states.orderCartItemsTutorial,
      orderFirstLoyaltyPointsTutorial: updates['orderFirstLoyaltyPointsTutorial'] ?? states.orderFirstLoyaltyPointsTutorial,
      orderPaymentOptionsTutorial: updates['orderPaymentOptionsTutorial'] ?? states.orderPaymentOptionsTutorial,
      orderPointsUsageTutorial: updates['orderPointsUsageTutorial'] ?? states.orderPointsUsageTutorial,
      orderTableNumberTutorial: updates['orderTableNumberTutorial'] ?? states.orderTableNumberTutorial,
      orderUsedCouponsTutorial: updates['orderUsedCouponsTutorial'] ?? states.orderUsedCouponsTutorial,
    );
  }

  static TutorialStatesRequest resetAll() {
    return const TutorialStatesRequest(
      mapTutorial: false,
      feedTutorial: false,
      gastroProfileTutorial: false,
      userAndSettingsTutorial: false,
      mapFilterTutorial: false,
      orderCartButtonTutorial: false,
      orderCartItemsTutorial: false,
      orderFirstLoyaltyPointsTutorial: false,
      orderPaymentOptionsTutorial: false,
      orderPointsUsageTutorial: false,
      orderTableNumberTutorial: false,
      orderUsedCouponsTutorial: false,
    );
  }
}