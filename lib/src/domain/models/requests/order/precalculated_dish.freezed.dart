// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'precalculated_dish.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PrecalculatedDish {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  List<ExtraSelectionModel> get extras => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PrecalculatedDishCopyWith<PrecalculatedDish> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PrecalculatedDishCopyWith<$Res> {
  factory $PrecalculatedDishCopyWith(
          PrecalculatedDish value, $Res Function(PrecalculatedDish) then) =
      _$PrecalculatedDishCopyWithImpl<$Res, PrecalculatedDish>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id, List<ExtraSelectionModel> extras});
}

/// @nodoc
class _$PrecalculatedDishCopyWithImpl<$Res, $Val extends PrecalculatedDish>
    implements $PrecalculatedDishCopyWith<$Res> {
  _$PrecalculatedDishCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? extras = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value.extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraSelectionModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PrecalculatedDishImplCopyWith<$Res>
    implements $PrecalculatedDishCopyWith<$Res> {
  factory _$$PrecalculatedDishImplCopyWith(_$PrecalculatedDishImpl value,
          $Res Function(_$PrecalculatedDishImpl) then) =
      __$$PrecalculatedDishImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id, List<ExtraSelectionModel> extras});
}

/// @nodoc
class __$$PrecalculatedDishImplCopyWithImpl<$Res>
    extends _$PrecalculatedDishCopyWithImpl<$Res, _$PrecalculatedDishImpl>
    implements _$$PrecalculatedDishImplCopyWith<$Res> {
  __$$PrecalculatedDishImplCopyWithImpl(_$PrecalculatedDishImpl _value,
      $Res Function(_$PrecalculatedDishImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? extras = null,
  }) {
    return _then(_$PrecalculatedDishImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value._extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraSelectionModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$PrecalculatedDishImpl implements _PrecalculatedDish {
  const _$PrecalculatedDishImpl(
      {@JsonKey(name: '_id') required this.id,
      final List<ExtraSelectionModel> extras = const []})
      : _extras = extras;

  @override
  @JsonKey(name: '_id')
  final String id;
  final List<ExtraSelectionModel> _extras;
  @override
  @JsonKey()
  List<ExtraSelectionModel> get extras {
    if (_extras is EqualUnmodifiableListView) return _extras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extras);
  }

  @override
  String toString() {
    return 'PrecalculatedDish(id: $id, extras: $extras)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PrecalculatedDishImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._extras, _extras));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_extras));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PrecalculatedDishImplCopyWith<_$PrecalculatedDishImpl> get copyWith =>
      __$$PrecalculatedDishImplCopyWithImpl<_$PrecalculatedDishImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PrecalculatedDishImplToJson(
      this,
    );
  }
}

abstract class _PrecalculatedDish implements PrecalculatedDish {
  const factory _PrecalculatedDish(
      {@JsonKey(name: '_id') required final String id,
      final List<ExtraSelectionModel> extras}) = _$PrecalculatedDishImpl;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  List<ExtraSelectionModel> get extras;
  @override
  @JsonKey(ignore: true)
  _$$PrecalculatedDishImplCopyWith<_$PrecalculatedDishImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
