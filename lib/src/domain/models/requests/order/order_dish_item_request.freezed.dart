// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_dish_item_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderDishItemRequest {
  @JsonKey(name: '_id')
  String get id => throw _privateConstructorUsedError;
  List<ExtraSelectionModel> get extras => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderDishItemRequestCopyWith<OrderDishItemRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderDishItemRequestCopyWith<$Res> {
  factory $OrderDishItemRequestCopyWith(OrderDishItemRequest value,
          $Res Function(OrderDishItemRequest) then) =
      _$OrderDishItemRequestCopyWithImpl<$Res, OrderDishItemRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id, List<ExtraSelectionModel> extras});
}

/// @nodoc
class _$OrderDishItemRequestCopyWithImpl<$Res,
        $Val extends OrderDishItemRequest>
    implements $OrderDishItemRequestCopyWith<$Res> {
  _$OrderDishItemRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? extras = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value.extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraSelectionModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderDishItemRequestImplCopyWith<$Res>
    implements $OrderDishItemRequestCopyWith<$Res> {
  factory _$$OrderDishItemRequestImplCopyWith(_$OrderDishItemRequestImpl value,
          $Res Function(_$OrderDishItemRequestImpl) then) =
      __$$OrderDishItemRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String id, List<ExtraSelectionModel> extras});
}

/// @nodoc
class __$$OrderDishItemRequestImplCopyWithImpl<$Res>
    extends _$OrderDishItemRequestCopyWithImpl<$Res, _$OrderDishItemRequestImpl>
    implements _$$OrderDishItemRequestImplCopyWith<$Res> {
  __$$OrderDishItemRequestImplCopyWithImpl(_$OrderDishItemRequestImpl _value,
      $Res Function(_$OrderDishItemRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? extras = null,
  }) {
    return _then(_$OrderDishItemRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      extras: null == extras
          ? _value._extras
          : extras // ignore: cast_nullable_to_non_nullable
              as List<ExtraSelectionModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _$OrderDishItemRequestImpl implements _OrderDishItemRequest {
  const _$OrderDishItemRequestImpl(
      {@JsonKey(name: '_id') required this.id,
      final List<ExtraSelectionModel> extras = const []})
      : _extras = extras;

  @override
  @JsonKey(name: '_id')
  final String id;
  final List<ExtraSelectionModel> _extras;
  @override
  @JsonKey()
  List<ExtraSelectionModel> get extras {
    if (_extras is EqualUnmodifiableListView) return _extras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extras);
  }

  @override
  String toString() {
    return 'OrderDishItemRequest(id: $id, extras: $extras)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderDishItemRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._extras, _extras));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_extras));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderDishItemRequestImplCopyWith<_$OrderDishItemRequestImpl>
      get copyWith =>
          __$$OrderDishItemRequestImplCopyWithImpl<_$OrderDishItemRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderDishItemRequestImplToJson(
      this,
    );
  }
}

abstract class _OrderDishItemRequest implements OrderDishItemRequest {
  const factory _OrderDishItemRequest(
      {@JsonKey(name: '_id') required final String id,
      final List<ExtraSelectionModel> extras}) = _$OrderDishItemRequestImpl;

  @override
  @JsonKey(name: '_id')
  String get id;
  @override
  List<ExtraSelectionModel> get extras;
  @override
  @JsonKey(ignore: true)
  _$$OrderDishItemRequestImplCopyWith<_$OrderDishItemRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
