import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/extras/extra_selection_model.dart';

part 'precalculated_dish.freezed.dart';
part 'precalculated_dish.g.dart';

@Freezed(fromJson: false, toJson: true)
class PrecalculatedDish with _$PrecalculatedDish {
  const factory PrecalculatedDish({
    @JsonKey(name: '_id') required String id,
    @Default([]) List<ExtraSelectionModel> extras,
  }) = _PrecalculatedDish;
}