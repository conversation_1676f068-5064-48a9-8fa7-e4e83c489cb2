import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/domain/models/extras/extra_selection_model.dart';

part 'order_dish_item_request.freezed.dart';
part 'order_dish_item_request.g.dart';

@Freezed(fromJson: false, toJson: true)
class OrderDishItemRequest with _$OrderDishItemRequest {
  const factory OrderDishItemRequest({
    @JsonKey(name: '_id') required String id,
    @Default([]) List<ExtraSelectionModel> extras,
  }) = _OrderDishItemRequest;
}
