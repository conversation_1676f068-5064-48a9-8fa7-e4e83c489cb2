import 'package:mutualz/src/domain/models/constants/order_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_selection_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/order_repository.dart';

enum ChangeOrderType { add, update }

class ChangeOrderUseCase {
  final OrderRepository _orderRepository;

  const ChangeOrderUseCase(this._orderRepository);

  Future<OrderResponse> call({
    required String restaurantId,
    required String tableNumber,
    required String? orderId,
    required List<CartItemModel> cart,
    required List<SpecialCartItemModel> specialCart,
    ChangeOrderType type = ChangeOrderType.add,
    String? couponId,
  }) async {
    final List<OrderDetailItemRequest> dishItems = cart.map((e) => OrderDetailItemRequest(
      type: OrderType.dish,
      dish: OrderDishItemRequest(
        id: e.dish.id,
        extras: e.selectedExtras.entries
          .where((entry) => entry.value.isNotEmpty)
          .map((entry) => ExtraSelectionModel(
            id: entry.key,
            items: entry.value.map((item) => item.id).toList(),
          )).toList(),
      ),
      quantity: e.quantity,
      notes: e.notes,
    )).toList();

    final List<OrderDetailItemRequest> specialItems = specialCart.map((e) => OrderDetailItemRequest(
      type: OrderType.special,
      quantity: e.quantity,
      special: SpecialOrderItemRequest(
        id: e.special.id,
        components: e.components.map((s) => SpecialComponentRequest(
          id: s.id,
          dishes: s.dishes.map((d) => OrderDishItemRequest(
            id: d.id,
            extras: [],
          )).toList(),
        )).toList(),
      ),
      notes: e.notes,
    )).toList();

    final request = OrderRequestModel(
      restaurantId: restaurantId,
      tableNumber: tableNumber,
      details: [
        OrderDetailRequest(
          items: [...dishItems, ...specialItems],
        ),
      ],
      couponId: couponId,
    );
    return switch (type) {
      ChangeOrderType.add => _orderRepository.createOrder(request),
      ChangeOrderType.update => _orderRepository.updateOrder(orderId!, request),
    };
  }
}