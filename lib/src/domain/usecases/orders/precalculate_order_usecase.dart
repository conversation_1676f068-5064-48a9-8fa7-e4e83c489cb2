import 'package:mutualz/src/domain/models/constants/order_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_selection_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/requests/order/precalculated_item_special.dart';
import 'package:mutualz/src/domain/repositories/order_repository.dart';

class PrecalculateOrderUseCase {
  final OrderRepository _orderRepository;

  const PrecalculateOrderUseCase(this._orderRepository);

  Future<PrecalculatedOrderResponse> call({
    required String restaurantId,
    required String? couponId,
    required List<CartItemModel> dishes,
    required List<SpecialCartItemModel> specials,
  }) async {
    final request = PrecalculatedOrderRequest(
      restaurantId: restaurantId,
      couponId: couponId,
      details: [
        PrecalculatedOrderDetails(
          items: [
            ...dishes.map((e) => PrecalculatedItemDish(
              type: OrderType.dish,
              dish: PrecalculatedDish(
                id: e.dish.id,
                extras: e.selectedExtras.entries
                  .where((entry) => entry.value.isNotEmpty)
                  .map((entry) => ExtraSelectionModel(
                    id: entry.key,
                    items: entry.value.map((item) => item.id).toList(),
                  )).toList(),
              ),
              quantity: e.quantity,
            )),
            ...specials.map((e) => PrecalculatedItemSpecial(
              type: OrderType.special,
              special: PrecalculatedSpecial(
                id: e.special.id,
                components: e.components.map((e) => PrecalculatedSpecialComponent(
                  id: e.id,
                  dishes: e.dishes.map((e) => PrecalculatedDish(
                    id: e.id,
                    extras: [],
                  )).toList(),
                )).toList(),
              ),
              quantity: e.quantity,
            )),
          ],
        ),
      ]
      // dishes: dishes.map((e) => PrecalculatedItemDish(
      //   type: OrderType.dish,
      //   dish: PrecalculatedDish(
      //     id: e.dish.id,
      //     extras: e.extras.map((e) => e.id).toList(),
      //   ),
      //   quantity: e.quantity,
      // )).toList(),
      // specials: specials.map((e) => PrecalculatedItemSpecial(
      //   type: OrderType.special,
      //   special: PrecalculatedSpecial(
      //     id: e.special.id,
      //     components: e.components.map((e) => PrecalculatedSpecialComponent(
      //       id: e.id,
      //       dishes: e.dishes.map((e) => PrecalculatedDish(
      //         id: e.id,
      //         extras: e.extras.map((e) => e.id).toList(),
      //       )).toList(),
      //     )).toList(),
      //   ),
      //   quantity: e.quantity,
      // )).toList(),
    );
    return _orderRepository.precalculateOrder(request);
  }
}