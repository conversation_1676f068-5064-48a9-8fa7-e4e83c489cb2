import '../../models/requests/tutorial/tutorial_states_request.dart';
import '../../models/responses/tutorial/tutorial_states_update_response.dart';
import '../../repositories/tutorial_repository.dart';

class UpdateTutorialStatesUseCase {
  final TutorialRepository _tutorialRepository;

  const UpdateTutorialStatesUseCase({
    required TutorialRepository tutorialRepository,
  }) : _tutorialRepository = tutorialRepository;

  Future<TutorialStatesUpdateResponse> call(
    TutorialStatesRequest request,
  ) async {
    return _tutorialRepository.updateTutorialStates(request);
  }
}