import '../../models/responses/tutorial/tutorial_states_response.dart';
import '../../repositories/tutorial_repository.dart';

class GetTutorialStatesUseCase {
  final TutorialRepository _tutorialRepository;

  const GetTutorialStatesUseCase({
    required TutorialRepository tutorialRepository,
  }) : _tutorialRepository = tutorialRepository;

  Future<TutorialStatesResponse> call() async {
    return _tutorialRepository.getTutorialStates();
  }
}