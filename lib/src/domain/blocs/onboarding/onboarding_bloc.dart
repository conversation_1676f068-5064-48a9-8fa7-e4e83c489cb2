import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/auth/auth_bloc.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/user_repository.dart';
import 'package:mutualz/src/domain/usecases/onboarding/apply_onboarding_usecase.dart';
import 'package:mutualz/src/domain/usecases/user/update_user_usecase.dart';

part 'onboarding_event.dart';
part 'onboarding_state.dart';
part 'onboarding_bloc.freezed.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  final ApplyOnBoardingUseCase _applyOnBoardingUseCase;
  final UpdateUserUseCase _updateUserUseCase;
  final AuthBloc _authBloc;
  final MLogger _logger;

  OnboardingBloc(
    this._applyOnBoardingUseCase,
    this._updateUserUseCase,
    this._authBloc,
    this._logger,
  ) : super(const OnboardingState()) {
    on<_ChangeGender>(_changeGender);
    on<_ChangeFirstName>(_changeFirstName);
    on<_ChangeLastName>(_changeLastName);
    on<_ChangePhoneNumber>(_changePhoneNumber);
    on<_ChangeJob>(_changeJob);
    on<_ChangeAllergy>(_changeAllergy);
    on<_ChangeDiet>(_changeDiet);
    on<_ChangeType>(_changeType);
    on<_Next>(_next);
    on<_Back>(_back);
    on<_ChangeDrinks>(_changeDrinks);
    on<_ChangeFood>(_changeFood);
    on<_ChangeEvents>(_changeEvents);
  }

  void _changeGender(_ChangeGender event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(gender: event.value));
  }

  void _changeFirstName(_ChangeFirstName event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(firstName: event.value));
  }

  void _changeLastName(_ChangeLastName event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(lastName: event.value));
  }

  void _changePhoneNumber(_ChangePhoneNumber event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(phoneNumber: event.value));
  }

  void _changeJob(_ChangeJob event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(job: event.value));
  }

  void _changeAllergy(_ChangeAllergy event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(allergy: event.value));
  }

  void _changeDiet(_ChangeDiet event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(diet: event.value));
  }

  void _changeType(_ChangeType event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(type: event.value));
  }

  void _changeDrinks(_ChangeDrinks event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(drinks: event.value));
  }

  void _changeFood(_ChangeFood event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(food: event.value));
  }

  void _changeEvents(_ChangeEvents event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(events: event.value));
  }

  void _back(_Back event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(type: OnboardingType.values[state.type.index - 1]));
  }


  Future<void> _next(_Next event, Emitter<OnboardingState> emit) async {
    if (state.type == OnboardingType.personalInfo) {
      // Just move to next screen without sending API request
      add(const _ChangeType(OnboardingType.lifestyle));
    }

    if (state.type == OnboardingType.lifestyle && !isDisabled) {
      add(const _ChangeType(OnboardingType.drink));
    }

    if (state.type == OnboardingType.drink && !isDisabled) {
      add(const _ChangeType(OnboardingType.food));
    }

    if (state.type == OnboardingType.food && !isDisabled) {
      add(const _ChangeType(OnboardingType.event));
    }

    if (state.type == OnboardingType.event && !isDisabled) {
      try {
        emit(state.copyWith(status: OnboardingStatus.loading));

        // First update personal info if we have user - fetch directly from API
        User? user;
        try {
          user = await GetUserUseCase(getIt<UserRepository>())();
        } catch (e) {
          _logger.error('OnboardingBloc: Failed to fetch user: $e');
        }
        
        if (user != null && (state.gender != null || state.firstName.isNotEmpty || 
            state.lastName.isNotEmpty || state.phoneNumber.isNotEmpty)) {
          final updatedUser = user.copyWith(
            personalInfo: user.personalInfo.copyWith(
              gender: state.gender?.name.capitalize(),
              firstName: state.firstName.isEmpty ? null : state.firstName,
              lastName: state.lastName.isEmpty ? null : state.lastName,
            ),
            contactInfo: user.contactInfo.copyWith(
              phoneNumber: state.phoneNumber.isNotEmpty ? state.phoneNumber : null,
            ),
          );

          await _updateUserUseCase(updatedUser);
          _authBloc.add(AuthEvent.setUser(updatedUser));
        }

        // Then apply onboarding preferences
        await _applyOnBoardingUseCase(OnBoardingRequest(
          occupation: OccupationRequest(occupation: state.job?.name ?? ''),
          dietary: DietaryRequest(
            isHalal: state.diet.contains(Diet.halal),
            isKosher: state.diet.contains(Diet.kosher),
            isVegan: state.diet.contains(Diet.vegan),
            isVegetarian: state.diet.contains(Diet.vegetarian)
          ),
          intolerances: IntolerancesRequest(
            isGlutenFree: state.allergy.contains(Allergy.gluten),
            isLactoseFree: state.allergy.contains(Allergy.lactose),
            hasNutAllergy: state.allergy.contains(Allergy.nutAllergy),
          ),
          preferences: UserPreferencesRequest(
            drinks: state.drinks.map((e) => e.serverName).toList(),
            events: state.events.map((e) => e.serverName).toList(),
            food: state.food.map((e) => e.serverName).toList()
          ),
        ));

        // Refresh user data in AuthBloc after all updates
        try {
          final refreshedUser = await GetUserUseCase(getIt<UserRepository>())();
          _authBloc.add(AuthEvent.setUser(refreshedUser));
        } catch (e) {
          _logger.error('OnboardingBloc: Failed to refresh user data: $e');
        }

        emit(state.copyWith(status: OnboardingStatus.success));
      } on DioException catch(e) {
        emit(state.copyWith(
          status: OnboardingStatus.error,
          error: e.message,
        ));
        _logger.error(e.toString());
      } catch(e) {
        emit(state.copyWith(
          status: OnboardingStatus.error,
          error: e.toString(),
        ));
        _logger.error(e.toString());
      }

      emit(state.copyWith(status: OnboardingStatus.idle));
    }
  }

  bool get hasBackButton {
    return state.type != OnboardingType.personalInfo;
  }

  bool get isDisabled => false;
}
