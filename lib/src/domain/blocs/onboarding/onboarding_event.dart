part of 'onboarding_bloc.dart';

@freezed
class OnboardingEvent with _$OnboardingEvent {
  const factory OnboardingEvent.changeGender(Gender value) = _ChangeGender;
  const factory OnboardingEvent.changeFirstName(String value) = _ChangeFirstName;
  const factory OnboardingEvent.changeLastName(String value) = _ChangeLastName;
  const factory OnboardingEvent.changePhoneNumber(String value) = _ChangePhoneNumber;
  const factory OnboardingEvent.changeJob(Job value) = _ChangeJob;
  const factory OnboardingEvent.changeAllergy(List<Allergy> value) = _ChangeAllergy;
  const factory OnboardingEvent.changeDiet(List<Diet> value) = _ChangeDiet;
  const factory OnboardingEvent.changeType(OnboardingType value) = _ChangeType;
  const factory OnboardingEvent.next() = _Next;
  const factory OnboardingEvent.back() = _Back;
  const factory OnboardingEvent.changeDrinks(List<Taste> value) = _ChangeDrinks;
  const factory OnboardingEvent.changeFood(List<Taste> value) = _ChangeFood;
  const factory OnboardingEvent.changeEvents(List<Taste> value) = _ChangeEvents;
}
