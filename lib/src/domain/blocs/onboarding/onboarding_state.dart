part of 'onboarding_bloc.dart';

@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
   @Default(OnboardingType.personalInfo) OnboardingType type,
   Gender? gender,
   @Default('') String firstName,
   @Default('') String lastName,
   @Default('') String phoneNumber,
   Job? job,
   @Default([]) List<Allergy> allergy,
   @Default([]) List<Diet> diet,
   @Default([]) List<Taste> drinks,
   @Default([]) List<Taste> food,
   @Default([]) List<Taste> events,
   @Default(OnboardingStatus.idle) OnboardingStatus status,
   String? error,
  }) = _OnboardingState;
}

enum OnboardingType {personalInfo, lifestyle, drink, food, event}
enum OnboardingStatus {idle, loading, success, error}
