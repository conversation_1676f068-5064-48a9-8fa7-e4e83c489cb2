// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OnboardingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingEventCopyWith<$Res> {
  factory $OnboardingEventCopyWith(
          OnboardingEvent value, $Res Function(OnboardingEvent) then) =
      _$OnboardingEventCopyWithImpl<$Res, OnboardingEvent>;
}

/// @nodoc
class _$OnboardingEventCopyWithImpl<$Res, $Val extends OnboardingEvent>
    implements $OnboardingEventCopyWith<$Res> {
  _$OnboardingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangeGenderImplCopyWith<$Res> {
  factory _$$ChangeGenderImplCopyWith(
          _$ChangeGenderImpl value, $Res Function(_$ChangeGenderImpl) then) =
      __$$ChangeGenderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Gender value});
}

/// @nodoc
class __$$ChangeGenderImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeGenderImpl>
    implements _$$ChangeGenderImplCopyWith<$Res> {
  __$$ChangeGenderImplCopyWithImpl(
      _$ChangeGenderImpl _value, $Res Function(_$ChangeGenderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeGenderImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as Gender,
    ));
  }
}

/// @nodoc

class _$ChangeGenderImpl implements _ChangeGender {
  const _$ChangeGenderImpl(this.value);

  @override
  final Gender value;

  @override
  String toString() {
    return 'OnboardingEvent.changeGender(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeGenderImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeGenderImplCopyWith<_$ChangeGenderImpl> get copyWith =>
      __$$ChangeGenderImplCopyWithImpl<_$ChangeGenderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeGender(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeGender?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeGender != null) {
      return changeGender(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeGender(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeGender?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeGender != null) {
      return changeGender(this);
    }
    return orElse();
  }
}

abstract class _ChangeGender implements OnboardingEvent {
  const factory _ChangeGender(final Gender value) = _$ChangeGenderImpl;

  Gender get value;
  @JsonKey(ignore: true)
  _$$ChangeGenderImplCopyWith<_$ChangeGenderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFirstNameImplCopyWith<$Res> {
  factory _$$ChangeFirstNameImplCopyWith(_$ChangeFirstNameImpl value,
          $Res Function(_$ChangeFirstNameImpl) then) =
      __$$ChangeFirstNameImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$ChangeFirstNameImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeFirstNameImpl>
    implements _$$ChangeFirstNameImplCopyWith<$Res> {
  __$$ChangeFirstNameImplCopyWithImpl(
      _$ChangeFirstNameImpl _value, $Res Function(_$ChangeFirstNameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeFirstNameImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeFirstNameImpl implements _ChangeFirstName {
  const _$ChangeFirstNameImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'OnboardingEvent.changeFirstName(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFirstNameImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFirstNameImplCopyWith<_$ChangeFirstNameImpl> get copyWith =>
      __$$ChangeFirstNameImplCopyWithImpl<_$ChangeFirstNameImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeFirstName(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeFirstName?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeFirstName != null) {
      return changeFirstName(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeFirstName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeFirstName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeFirstName != null) {
      return changeFirstName(this);
    }
    return orElse();
  }
}

abstract class _ChangeFirstName implements OnboardingEvent {
  const factory _ChangeFirstName(final String value) = _$ChangeFirstNameImpl;

  String get value;
  @JsonKey(ignore: true)
  _$$ChangeFirstNameImplCopyWith<_$ChangeFirstNameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeLastNameImplCopyWith<$Res> {
  factory _$$ChangeLastNameImplCopyWith(_$ChangeLastNameImpl value,
          $Res Function(_$ChangeLastNameImpl) then) =
      __$$ChangeLastNameImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$ChangeLastNameImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeLastNameImpl>
    implements _$$ChangeLastNameImplCopyWith<$Res> {
  __$$ChangeLastNameImplCopyWithImpl(
      _$ChangeLastNameImpl _value, $Res Function(_$ChangeLastNameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeLastNameImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeLastNameImpl implements _ChangeLastName {
  const _$ChangeLastNameImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'OnboardingEvent.changeLastName(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeLastNameImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeLastNameImplCopyWith<_$ChangeLastNameImpl> get copyWith =>
      __$$ChangeLastNameImplCopyWithImpl<_$ChangeLastNameImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeLastName(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeLastName?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeLastName != null) {
      return changeLastName(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeLastName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeLastName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeLastName != null) {
      return changeLastName(this);
    }
    return orElse();
  }
}

abstract class _ChangeLastName implements OnboardingEvent {
  const factory _ChangeLastName(final String value) = _$ChangeLastNameImpl;

  String get value;
  @JsonKey(ignore: true)
  _$$ChangeLastNameImplCopyWith<_$ChangeLastNameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangePhoneNumberImplCopyWith<$Res> {
  factory _$$ChangePhoneNumberImplCopyWith(_$ChangePhoneNumberImpl value,
          $Res Function(_$ChangePhoneNumberImpl) then) =
      __$$ChangePhoneNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$ChangePhoneNumberImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangePhoneNumberImpl>
    implements _$$ChangePhoneNumberImplCopyWith<$Res> {
  __$$ChangePhoneNumberImplCopyWithImpl(_$ChangePhoneNumberImpl _value,
      $Res Function(_$ChangePhoneNumberImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangePhoneNumberImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangePhoneNumberImpl implements _ChangePhoneNumber {
  const _$ChangePhoneNumberImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'OnboardingEvent.changePhoneNumber(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePhoneNumberImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePhoneNumberImplCopyWith<_$ChangePhoneNumberImpl> get copyWith =>
      __$$ChangePhoneNumberImplCopyWithImpl<_$ChangePhoneNumberImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changePhoneNumber(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changePhoneNumber?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changePhoneNumber != null) {
      return changePhoneNumber(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changePhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changePhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changePhoneNumber != null) {
      return changePhoneNumber(this);
    }
    return orElse();
  }
}

abstract class _ChangePhoneNumber implements OnboardingEvent {
  const factory _ChangePhoneNumber(final String value) =
      _$ChangePhoneNumberImpl;

  String get value;
  @JsonKey(ignore: true)
  _$$ChangePhoneNumberImplCopyWith<_$ChangePhoneNumberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeJobImplCopyWith<$Res> {
  factory _$$ChangeJobImplCopyWith(
          _$ChangeJobImpl value, $Res Function(_$ChangeJobImpl) then) =
      __$$ChangeJobImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Job value});
}

/// @nodoc
class __$$ChangeJobImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeJobImpl>
    implements _$$ChangeJobImplCopyWith<$Res> {
  __$$ChangeJobImplCopyWithImpl(
      _$ChangeJobImpl _value, $Res Function(_$ChangeJobImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeJobImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as Job,
    ));
  }
}

/// @nodoc

class _$ChangeJobImpl implements _ChangeJob {
  const _$ChangeJobImpl(this.value);

  @override
  final Job value;

  @override
  String toString() {
    return 'OnboardingEvent.changeJob(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeJobImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeJobImplCopyWith<_$ChangeJobImpl> get copyWith =>
      __$$ChangeJobImplCopyWithImpl<_$ChangeJobImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeJob(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeJob?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeJob != null) {
      return changeJob(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeJob(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeJob?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeJob != null) {
      return changeJob(this);
    }
    return orElse();
  }
}

abstract class _ChangeJob implements OnboardingEvent {
  const factory _ChangeJob(final Job value) = _$ChangeJobImpl;

  Job get value;
  @JsonKey(ignore: true)
  _$$ChangeJobImplCopyWith<_$ChangeJobImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeAllergyImplCopyWith<$Res> {
  factory _$$ChangeAllergyImplCopyWith(
          _$ChangeAllergyImpl value, $Res Function(_$ChangeAllergyImpl) then) =
      __$$ChangeAllergyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Allergy> value});
}

/// @nodoc
class __$$ChangeAllergyImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeAllergyImpl>
    implements _$$ChangeAllergyImplCopyWith<$Res> {
  __$$ChangeAllergyImplCopyWithImpl(
      _$ChangeAllergyImpl _value, $Res Function(_$ChangeAllergyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeAllergyImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<Allergy>,
    ));
  }
}

/// @nodoc

class _$ChangeAllergyImpl implements _ChangeAllergy {
  const _$ChangeAllergyImpl(final List<Allergy> value) : _value = value;

  final List<Allergy> _value;
  @override
  List<Allergy> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'OnboardingEvent.changeAllergy(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeAllergyImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeAllergyImplCopyWith<_$ChangeAllergyImpl> get copyWith =>
      __$$ChangeAllergyImplCopyWithImpl<_$ChangeAllergyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeAllergy(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeAllergy?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeAllergy != null) {
      return changeAllergy(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeAllergy(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeAllergy?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeAllergy != null) {
      return changeAllergy(this);
    }
    return orElse();
  }
}

abstract class _ChangeAllergy implements OnboardingEvent {
  const factory _ChangeAllergy(final List<Allergy> value) = _$ChangeAllergyImpl;

  List<Allergy> get value;
  @JsonKey(ignore: true)
  _$$ChangeAllergyImplCopyWith<_$ChangeAllergyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeDietImplCopyWith<$Res> {
  factory _$$ChangeDietImplCopyWith(
          _$ChangeDietImpl value, $Res Function(_$ChangeDietImpl) then) =
      __$$ChangeDietImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Diet> value});
}

/// @nodoc
class __$$ChangeDietImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeDietImpl>
    implements _$$ChangeDietImplCopyWith<$Res> {
  __$$ChangeDietImplCopyWithImpl(
      _$ChangeDietImpl _value, $Res Function(_$ChangeDietImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeDietImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<Diet>,
    ));
  }
}

/// @nodoc

class _$ChangeDietImpl implements _ChangeDiet {
  const _$ChangeDietImpl(final List<Diet> value) : _value = value;

  final List<Diet> _value;
  @override
  List<Diet> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'OnboardingEvent.changeDiet(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeDietImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeDietImplCopyWith<_$ChangeDietImpl> get copyWith =>
      __$$ChangeDietImplCopyWithImpl<_$ChangeDietImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeDiet(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeDiet?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeDiet != null) {
      return changeDiet(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeDiet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeDiet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeDiet != null) {
      return changeDiet(this);
    }
    return orElse();
  }
}

abstract class _ChangeDiet implements OnboardingEvent {
  const factory _ChangeDiet(final List<Diet> value) = _$ChangeDietImpl;

  List<Diet> get value;
  @JsonKey(ignore: true)
  _$$ChangeDietImplCopyWith<_$ChangeDietImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeTypeImplCopyWith<$Res> {
  factory _$$ChangeTypeImplCopyWith(
          _$ChangeTypeImpl value, $Res Function(_$ChangeTypeImpl) then) =
      __$$ChangeTypeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OnboardingType value});
}

/// @nodoc
class __$$ChangeTypeImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeTypeImpl>
    implements _$$ChangeTypeImplCopyWith<$Res> {
  __$$ChangeTypeImplCopyWithImpl(
      _$ChangeTypeImpl _value, $Res Function(_$ChangeTypeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeTypeImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as OnboardingType,
    ));
  }
}

/// @nodoc

class _$ChangeTypeImpl implements _ChangeType {
  const _$ChangeTypeImpl(this.value);

  @override
  final OnboardingType value;

  @override
  String toString() {
    return 'OnboardingEvent.changeType(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeTypeImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeTypeImplCopyWith<_$ChangeTypeImpl> get copyWith =>
      __$$ChangeTypeImplCopyWithImpl<_$ChangeTypeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeType(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeType?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeType != null) {
      return changeType(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeType != null) {
      return changeType(this);
    }
    return orElse();
  }
}

abstract class _ChangeType implements OnboardingEvent {
  const factory _ChangeType(final OnboardingType value) = _$ChangeTypeImpl;

  OnboardingType get value;
  @JsonKey(ignore: true)
  _$$ChangeTypeImplCopyWith<_$ChangeTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NextImplCopyWith<$Res> {
  factory _$$NextImplCopyWith(
          _$NextImpl value, $Res Function(_$NextImpl) then) =
      __$$NextImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NextImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$NextImpl>
    implements _$$NextImplCopyWith<$Res> {
  __$$NextImplCopyWithImpl(_$NextImpl _value, $Res Function(_$NextImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$NextImpl implements _Next {
  const _$NextImpl();

  @override
  String toString() {
    return 'OnboardingEvent.next()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NextImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return next();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return next?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (next != null) {
      return next();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return next(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return next?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (next != null) {
      return next(this);
    }
    return orElse();
  }
}

abstract class _Next implements OnboardingEvent {
  const factory _Next() = _$NextImpl;
}

/// @nodoc
abstract class _$$BackImplCopyWith<$Res> {
  factory _$$BackImplCopyWith(
          _$BackImpl value, $Res Function(_$BackImpl) then) =
      __$$BackImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BackImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$BackImpl>
    implements _$$BackImplCopyWith<$Res> {
  __$$BackImplCopyWithImpl(_$BackImpl _value, $Res Function(_$BackImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$BackImpl implements _Back {
  const _$BackImpl();

  @override
  String toString() {
    return 'OnboardingEvent.back()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BackImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return back();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return back?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (back != null) {
      return back();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return back(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return back?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (back != null) {
      return back(this);
    }
    return orElse();
  }
}

abstract class _Back implements OnboardingEvent {
  const factory _Back() = _$BackImpl;
}

/// @nodoc
abstract class _$$ChangeDrinksImplCopyWith<$Res> {
  factory _$$ChangeDrinksImplCopyWith(
          _$ChangeDrinksImpl value, $Res Function(_$ChangeDrinksImpl) then) =
      __$$ChangeDrinksImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Taste> value});
}

/// @nodoc
class __$$ChangeDrinksImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeDrinksImpl>
    implements _$$ChangeDrinksImplCopyWith<$Res> {
  __$$ChangeDrinksImplCopyWithImpl(
      _$ChangeDrinksImpl _value, $Res Function(_$ChangeDrinksImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeDrinksImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
    ));
  }
}

/// @nodoc

class _$ChangeDrinksImpl implements _ChangeDrinks {
  const _$ChangeDrinksImpl(final List<Taste> value) : _value = value;

  final List<Taste> _value;
  @override
  List<Taste> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'OnboardingEvent.changeDrinks(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeDrinksImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeDrinksImplCopyWith<_$ChangeDrinksImpl> get copyWith =>
      __$$ChangeDrinksImplCopyWithImpl<_$ChangeDrinksImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeDrinks(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeDrinks?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeDrinks != null) {
      return changeDrinks(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeDrinks(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeDrinks?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeDrinks != null) {
      return changeDrinks(this);
    }
    return orElse();
  }
}

abstract class _ChangeDrinks implements OnboardingEvent {
  const factory _ChangeDrinks(final List<Taste> value) = _$ChangeDrinksImpl;

  List<Taste> get value;
  @JsonKey(ignore: true)
  _$$ChangeDrinksImplCopyWith<_$ChangeDrinksImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFoodImplCopyWith<$Res> {
  factory _$$ChangeFoodImplCopyWith(
          _$ChangeFoodImpl value, $Res Function(_$ChangeFoodImpl) then) =
      __$$ChangeFoodImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Taste> value});
}

/// @nodoc
class __$$ChangeFoodImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeFoodImpl>
    implements _$$ChangeFoodImplCopyWith<$Res> {
  __$$ChangeFoodImplCopyWithImpl(
      _$ChangeFoodImpl _value, $Res Function(_$ChangeFoodImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeFoodImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
    ));
  }
}

/// @nodoc

class _$ChangeFoodImpl implements _ChangeFood {
  const _$ChangeFoodImpl(final List<Taste> value) : _value = value;

  final List<Taste> _value;
  @override
  List<Taste> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'OnboardingEvent.changeFood(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFoodImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFoodImplCopyWith<_$ChangeFoodImpl> get copyWith =>
      __$$ChangeFoodImplCopyWithImpl<_$ChangeFoodImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeFood(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeFood?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeFood != null) {
      return changeFood(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeFood(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeFood?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeFood != null) {
      return changeFood(this);
    }
    return orElse();
  }
}

abstract class _ChangeFood implements OnboardingEvent {
  const factory _ChangeFood(final List<Taste> value) = _$ChangeFoodImpl;

  List<Taste> get value;
  @JsonKey(ignore: true)
  _$$ChangeFoodImplCopyWith<_$ChangeFoodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeEventsImplCopyWith<$Res> {
  factory _$$ChangeEventsImplCopyWith(
          _$ChangeEventsImpl value, $Res Function(_$ChangeEventsImpl) then) =
      __$$ChangeEventsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Taste> value});
}

/// @nodoc
class __$$ChangeEventsImplCopyWithImpl<$Res>
    extends _$OnboardingEventCopyWithImpl<$Res, _$ChangeEventsImpl>
    implements _$$ChangeEventsImplCopyWith<$Res> {
  __$$ChangeEventsImplCopyWithImpl(
      _$ChangeEventsImpl _value, $Res Function(_$ChangeEventsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeEventsImpl(
      null == value
          ? _value._value
          : value // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
    ));
  }
}

/// @nodoc

class _$ChangeEventsImpl implements _ChangeEvents {
  const _$ChangeEventsImpl(final List<Taste> value) : _value = value;

  final List<Taste> _value;
  @override
  List<Taste> get value {
    if (_value is EqualUnmodifiableListView) return _value;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_value);
  }

  @override
  String toString() {
    return 'OnboardingEvent.changeEvents(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeEventsImpl &&
            const DeepCollectionEquality().equals(other._value, _value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_value));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeEventsImplCopyWith<_$ChangeEventsImpl> get copyWith =>
      __$$ChangeEventsImplCopyWithImpl<_$ChangeEventsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender value) changeGender,
    required TResult Function(String value) changeFirstName,
    required TResult Function(String value) changeLastName,
    required TResult Function(String value) changePhoneNumber,
    required TResult Function(Job value) changeJob,
    required TResult Function(List<Allergy> value) changeAllergy,
    required TResult Function(List<Diet> value) changeDiet,
    required TResult Function(OnboardingType value) changeType,
    required TResult Function() next,
    required TResult Function() back,
    required TResult Function(List<Taste> value) changeDrinks,
    required TResult Function(List<Taste> value) changeFood,
    required TResult Function(List<Taste> value) changeEvents,
  }) {
    return changeEvents(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender value)? changeGender,
    TResult? Function(String value)? changeFirstName,
    TResult? Function(String value)? changeLastName,
    TResult? Function(String value)? changePhoneNumber,
    TResult? Function(Job value)? changeJob,
    TResult? Function(List<Allergy> value)? changeAllergy,
    TResult? Function(List<Diet> value)? changeDiet,
    TResult? Function(OnboardingType value)? changeType,
    TResult? Function()? next,
    TResult? Function()? back,
    TResult? Function(List<Taste> value)? changeDrinks,
    TResult? Function(List<Taste> value)? changeFood,
    TResult? Function(List<Taste> value)? changeEvents,
  }) {
    return changeEvents?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender value)? changeGender,
    TResult Function(String value)? changeFirstName,
    TResult Function(String value)? changeLastName,
    TResult Function(String value)? changePhoneNumber,
    TResult Function(Job value)? changeJob,
    TResult Function(List<Allergy> value)? changeAllergy,
    TResult Function(List<Diet> value)? changeDiet,
    TResult Function(OnboardingType value)? changeType,
    TResult Function()? next,
    TResult Function()? back,
    TResult Function(List<Taste> value)? changeDrinks,
    TResult Function(List<Taste> value)? changeFood,
    TResult Function(List<Taste> value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeEvents != null) {
      return changeEvents(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangeJob value) changeJob,
    required TResult Function(_ChangeAllergy value) changeAllergy,
    required TResult Function(_ChangeDiet value) changeDiet,
    required TResult Function(_ChangeType value) changeType,
    required TResult Function(_Next value) next,
    required TResult Function(_Back value) back,
    required TResult Function(_ChangeDrinks value) changeDrinks,
    required TResult Function(_ChangeFood value) changeFood,
    required TResult Function(_ChangeEvents value) changeEvents,
  }) {
    return changeEvents(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangeJob value)? changeJob,
    TResult? Function(_ChangeAllergy value)? changeAllergy,
    TResult? Function(_ChangeDiet value)? changeDiet,
    TResult? Function(_ChangeType value)? changeType,
    TResult? Function(_Next value)? next,
    TResult? Function(_Back value)? back,
    TResult? Function(_ChangeDrinks value)? changeDrinks,
    TResult? Function(_ChangeFood value)? changeFood,
    TResult? Function(_ChangeEvents value)? changeEvents,
  }) {
    return changeEvents?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangeJob value)? changeJob,
    TResult Function(_ChangeAllergy value)? changeAllergy,
    TResult Function(_ChangeDiet value)? changeDiet,
    TResult Function(_ChangeType value)? changeType,
    TResult Function(_Next value)? next,
    TResult Function(_Back value)? back,
    TResult Function(_ChangeDrinks value)? changeDrinks,
    TResult Function(_ChangeFood value)? changeFood,
    TResult Function(_ChangeEvents value)? changeEvents,
    required TResult orElse(),
  }) {
    if (changeEvents != null) {
      return changeEvents(this);
    }
    return orElse();
  }
}

abstract class _ChangeEvents implements OnboardingEvent {
  const factory _ChangeEvents(final List<Taste> value) = _$ChangeEventsImpl;

  List<Taste> get value;
  @JsonKey(ignore: true)
  _$$ChangeEventsImplCopyWith<_$ChangeEventsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OnboardingState {
  OnboardingType get type => throw _privateConstructorUsedError;
  Gender? get gender => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  Job? get job => throw _privateConstructorUsedError;
  List<Allergy> get allergy => throw _privateConstructorUsedError;
  List<Diet> get diet => throw _privateConstructorUsedError;
  List<Taste> get drinks => throw _privateConstructorUsedError;
  List<Taste> get food => throw _privateConstructorUsedError;
  List<Taste> get events => throw _privateConstructorUsedError;
  OnboardingStatus get status => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OnboardingStateCopyWith<OnboardingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingStateCopyWith<$Res> {
  factory $OnboardingStateCopyWith(
          OnboardingState value, $Res Function(OnboardingState) then) =
      _$OnboardingStateCopyWithImpl<$Res, OnboardingState>;
  @useResult
  $Res call(
      {OnboardingType type,
      Gender? gender,
      String firstName,
      String lastName,
      String phoneNumber,
      Job? job,
      List<Allergy> allergy,
      List<Diet> diet,
      List<Taste> drinks,
      List<Taste> food,
      List<Taste> events,
      OnboardingStatus status,
      String? error});
}

/// @nodoc
class _$OnboardingStateCopyWithImpl<$Res, $Val extends OnboardingState>
    implements $OnboardingStateCopyWith<$Res> {
  _$OnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? gender = freezed,
    Object? firstName = null,
    Object? lastName = null,
    Object? phoneNumber = null,
    Object? job = freezed,
    Object? allergy = null,
    Object? diet = null,
    Object? drinks = null,
    Object? food = null,
    Object? events = null,
    Object? status = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OnboardingType,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      job: freezed == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as Job?,
      allergy: null == allergy
          ? _value.allergy
          : allergy // ignore: cast_nullable_to_non_nullable
              as List<Allergy>,
      diet: null == diet
          ? _value.diet
          : diet // ignore: cast_nullable_to_non_nullable
              as List<Diet>,
      drinks: null == drinks
          ? _value.drinks
          : drinks // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      food: null == food
          ? _value.food
          : food // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      events: null == events
          ? _value.events
          : events // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OnboardingStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OnboardingStateImplCopyWith<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  factory _$$OnboardingStateImplCopyWith(_$OnboardingStateImpl value,
          $Res Function(_$OnboardingStateImpl) then) =
      __$$OnboardingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OnboardingType type,
      Gender? gender,
      String firstName,
      String lastName,
      String phoneNumber,
      Job? job,
      List<Allergy> allergy,
      List<Diet> diet,
      List<Taste> drinks,
      List<Taste> food,
      List<Taste> events,
      OnboardingStatus status,
      String? error});
}

/// @nodoc
class __$$OnboardingStateImplCopyWithImpl<$Res>
    extends _$OnboardingStateCopyWithImpl<$Res, _$OnboardingStateImpl>
    implements _$$OnboardingStateImplCopyWith<$Res> {
  __$$OnboardingStateImplCopyWithImpl(
      _$OnboardingStateImpl _value, $Res Function(_$OnboardingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? gender = freezed,
    Object? firstName = null,
    Object? lastName = null,
    Object? phoneNumber = null,
    Object? job = freezed,
    Object? allergy = null,
    Object? diet = null,
    Object? drinks = null,
    Object? food = null,
    Object? events = null,
    Object? status = null,
    Object? error = freezed,
  }) {
    return _then(_$OnboardingStateImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OnboardingType,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      job: freezed == job
          ? _value.job
          : job // ignore: cast_nullable_to_non_nullable
              as Job?,
      allergy: null == allergy
          ? _value._allergy
          : allergy // ignore: cast_nullable_to_non_nullable
              as List<Allergy>,
      diet: null == diet
          ? _value._diet
          : diet // ignore: cast_nullable_to_non_nullable
              as List<Diet>,
      drinks: null == drinks
          ? _value._drinks
          : drinks // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      food: null == food
          ? _value._food
          : food // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      events: null == events
          ? _value._events
          : events // ignore: cast_nullable_to_non_nullable
              as List<Taste>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OnboardingStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$OnboardingStateImpl implements _OnboardingState {
  const _$OnboardingStateImpl(
      {this.type = OnboardingType.personalInfo,
      this.gender,
      this.firstName = '',
      this.lastName = '',
      this.phoneNumber = '',
      this.job,
      final List<Allergy> allergy = const [],
      final List<Diet> diet = const [],
      final List<Taste> drinks = const [],
      final List<Taste> food = const [],
      final List<Taste> events = const [],
      this.status = OnboardingStatus.idle,
      this.error})
      : _allergy = allergy,
        _diet = diet,
        _drinks = drinks,
        _food = food,
        _events = events;

  @override
  @JsonKey()
  final OnboardingType type;
  @override
  final Gender? gender;
  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final String phoneNumber;
  @override
  final Job? job;
  final List<Allergy> _allergy;
  @override
  @JsonKey()
  List<Allergy> get allergy {
    if (_allergy is EqualUnmodifiableListView) return _allergy;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allergy);
  }

  final List<Diet> _diet;
  @override
  @JsonKey()
  List<Diet> get diet {
    if (_diet is EqualUnmodifiableListView) return _diet;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_diet);
  }

  final List<Taste> _drinks;
  @override
  @JsonKey()
  List<Taste> get drinks {
    if (_drinks is EqualUnmodifiableListView) return _drinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_drinks);
  }

  final List<Taste> _food;
  @override
  @JsonKey()
  List<Taste> get food {
    if (_food is EqualUnmodifiableListView) return _food;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_food);
  }

  final List<Taste> _events;
  @override
  @JsonKey()
  List<Taste> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  @JsonKey()
  final OnboardingStatus status;
  @override
  final String? error;

  @override
  String toString() {
    return 'OnboardingState(type: $type, gender: $gender, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, job: $job, allergy: $allergy, diet: $diet, drinks: $drinks, food: $food, events: $events, status: $status, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingStateImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.job, job) || other.job == job) &&
            const DeepCollectionEquality().equals(other._allergy, _allergy) &&
            const DeepCollectionEquality().equals(other._diet, _diet) &&
            const DeepCollectionEquality().equals(other._drinks, _drinks) &&
            const DeepCollectionEquality().equals(other._food, _food) &&
            const DeepCollectionEquality().equals(other._events, _events) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      gender,
      firstName,
      lastName,
      phoneNumber,
      job,
      const DeepCollectionEquality().hash(_allergy),
      const DeepCollectionEquality().hash(_diet),
      const DeepCollectionEquality().hash(_drinks),
      const DeepCollectionEquality().hash(_food),
      const DeepCollectionEquality().hash(_events),
      status,
      error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      __$$OnboardingStateImplCopyWithImpl<_$OnboardingStateImpl>(
          this, _$identity);
}

abstract class _OnboardingState implements OnboardingState {
  const factory _OnboardingState(
      {final OnboardingType type,
      final Gender? gender,
      final String firstName,
      final String lastName,
      final String phoneNumber,
      final Job? job,
      final List<Allergy> allergy,
      final List<Diet> diet,
      final List<Taste> drinks,
      final List<Taste> food,
      final List<Taste> events,
      final OnboardingStatus status,
      final String? error}) = _$OnboardingStateImpl;

  @override
  OnboardingType get type;
  @override
  Gender? get gender;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get phoneNumber;
  @override
  Job? get job;
  @override
  List<Allergy> get allergy;
  @override
  List<Diet> get diet;
  @override
  List<Taste> get drinks;
  @override
  List<Taste> get food;
  @override
  List<Taste> get events;
  @override
  OnboardingStatus get status;
  @override
  String? get error;
  @override
  @JsonKey(ignore: true)
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
