part of 'auth_bloc.dart';

@freezed
class AuthEvent with _$AuthEvent {
  const factory AuthEvent.fetchUser() = _FetchUser;
  const factory AuthEvent.setUser(User user) = _SetUser;
  const factory AuthEvent.setAuthentication(bool value) = _SetAuthentication;
  const factory AuthEvent.setIsEmailVerified(bool value) = _SetIsEmailVerified;
  const factory AuthEvent.removeUser() = _RemoveUser;
  const factory AuthEvent.logout() = _Logout;
  const factory AuthEvent.changeResetPasswordToken(String? token) = _ChangeResetPasswordToken;
  const factory AuthEvent.initiateRestaurantSession(String token) = _InitiateRestaurantSession;
  const factory AuthEvent.fetchTutorialStates() = _FetchTutorialStates;
  const factory AuthEvent.setTutorialStates(TutorialStatesData tutorialStates) = _SetTutorialStates;
  const factory AuthEvent.updateTutorialStates(Map<String, bool> states) = _UpdateTutorialStates;
}
