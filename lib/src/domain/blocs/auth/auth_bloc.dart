import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/responses/tutorial/tutorial_states_response.dart';
import 'package:mutualz/src/domain/models/requests/tutorial/tutorial_states_request.dart';
import 'package:mutualz/src/domain/usecases/tutorial/get_tutorial_states_usecase.dart';
import 'package:mutualz/src/domain/usecases/tutorial/update_tutorial_states_usecase.dart';

part 'auth_event.dart';
part 'auth_state.dart';
part 'auth_bloc.freezed.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final TokenHandler _tokenHandler;
  final GetIsEmailVerifiedUseCase _getIsEmailVerifiedUseCase;
  final SetIsEmailVerifiedUseCase _setIsEmailVerifiedUseCase;
  late final StreamSubscription<bool> _tokenSubscription;
  final LogoutUseCase _logoutUseCase;
  final RemoveUserUseCase _removeUserUseCase;
  final DeepLinksService _deepLinksService;
  final GetUserUseCase _getUserUseCase;
  final InitiateRestaurantSessionUseCase _initiateRestaurantSessionUseCase;
  final GetTutorialStatesUseCase _getTutorialStatesUseCase;
  final UpdateTutorialStatesUseCase _updateTutorialStatesUseCase;
  final MLogger _logger;

  late final StreamSubscription<Uri?> _deepLinkSubscription;

  AuthBloc(
    this._tokenHandler,
    this._getIsEmailVerifiedUseCase,
    this._setIsEmailVerifiedUseCase,
    this._logoutUseCase,
    this._removeUserUseCase,
    this._deepLinksService,
    this._getUserUseCase,
    this._initiateRestaurantSessionUseCase,
    this._getTutorialStatesUseCase,
    this._updateTutorialStatesUseCase,
    this._logger,
  ) : super(const AuthState()) {
    on<_FetchUser>(_fetchUser);
    on<_SetUser>(_setUser);
    on<_SetAuthentication>(_setAuthentication);
    on<_SetIsEmailVerified>(_setIsEmailVerified);
    on<_Logout>(_logout);
    on<_RemoveUser>(_removeUser);
    on<_ChangeResetPasswordToken>(_changeResetPasswordToken);
    on<_InitiateRestaurantSession>(_initiateRestaurantSession);
    on<_FetchTutorialStates>(_fetchTutorialStates);
    on<_SetTutorialStates>(_setTutorialStates);
    on<_UpdateTutorialStates>(_updateTutorialStates);

    _tokenSubscription = _tokenHandler.tokenStream.listen((isAuthenticated) {
      add(_SetAuthentication(isAuthenticated));
    });

    _deepLinkSubscription = _deepLinksService.stream.listen((appLink) {
      _forgotPasswordLinkHandler(appLink);
    });
  }

  Future<void> _initiateRestaurantSession(_InitiateRestaurantSession event, Emitter<AuthState> emit) async {
    try {
      final session = await _initiateRestaurantSessionUseCase(token: event.token);
      if (state.user != null) {
        final User user = state.user!.copyWith(
          authInfo: state.user!.authInfo.copyWith(
            restaurantToken: session.token
          ),
        );

        emit(state.copyWith(user: user));
      }
    } on DioException catch (e) {
      _logger.error('Error initiating restaurant session ${e.message}');
    } catch (e, s) {
      _logger.error('Error initiating restaurant session ${e.toString()}');
      _logger.error(s.toString());
    }
  }

  Future<void> _fetchUser(_FetchUser event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(userStatus: UserStatus.loading));
      final user = await _getUserUseCase();
      emit(state.copyWith(
        user: user,
        userStatus: UserStatus.success,
      ));
      
      add(const AuthEvent.fetchTutorialStates());
    } on DioException catch (e, s) {
      _logger.error('Error fetching user ${e.message} $s');
      emit(state.copyWith(
        userStatus: UserStatus.error,
        userError: e.message,
      ));
    } catch (e, s) {
      _logger.error('_here Error fetching user ${e.toString()} $s');
      emit(state.copyWith(
        userStatus: UserStatus.error,
        userError: e.toString(),
      ));
    }
  }

  Future<void> _setUser(_SetUser event, Emitter<AuthState> emit) async {
    emit(state.copyWith(user: event.user));
  }

  Future<void> getInitialDeepLink() async {
    final initialLink = await _deepLinksService.getInitialDeepLink();
    _forgotPasswordLinkHandler(initialLink);
  }

  Future<void> _forgotPasswordLinkHandler(Uri? appLink) async {
    if (appLink != null) {
      final parsedLink = appLink;
      final token = parsedLink.pathSegments.contains('reset-password') ?
        parsedLink.queryParameters['token'] : null;
      add(_ChangeResetPasswordToken(token));
    }
  }

  Future<void> _changeResetPasswordToken(_ChangeResetPasswordToken event, Emitter<AuthState> emit) async {
    emit(state.copyWith(resetPasswordToken: event.token));
  }

  Future<void> _setAuthentication(_SetAuthentication event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isAuthenticated: event.value));
  }

  Future<void> _setIsEmailVerified(_SetIsEmailVerified event, Emitter<AuthState> emit) async {
    emit(state.copyWith(isEmailVerified: event.value));
    _setIsEmailVerifiedUseCase(isEmailVerified: event.value);
  }

  Future<void> _logout(_Logout event, Emitter<AuthState> emit) async {
    emit(state.copyWith(
      isAuthenticated: false,
      isEmailVerified: false,
      resetPasswordToken: null,
      user: null,
      tutorialStates: null,
      tutorialStatus: TutorialStatus.idle,
    ));
    await _logoutUseCase();
    await _setIsEmailVerifiedUseCase(isEmailVerified: false);
    await _tokenHandler.clear();
  }

  Future<void> _removeUser(_RemoveUser event, Emitter<AuthState> emit) async {
    emit(state.copyWith(
      isAuthenticated: false,
      isEmailVerified: false,
      resetPasswordToken: null,
      user: null,
      tutorialStates: null,
      tutorialStatus: TutorialStatus.idle,
    ));
    await _removeUserUseCase();
    await _setIsEmailVerifiedUseCase(isEmailVerified: false);
    await _tokenHandler.clear();
  }

  Future<(bool, String?)> getUserEmail() async {
    final email = await _tokenHandler.getUserEmailFromAccessToken();
    final isEmailVerified = await _getIsEmailVerifiedUseCase();
    return (isEmailVerified, email);
  }

  Future<void> _fetchTutorialStates(_FetchTutorialStates event, Emitter<AuthState> emit) async {
    try {
      emit(state.copyWith(tutorialStatus: TutorialStatus.loading));
      final tutorialResponse = await _getTutorialStatesUseCase();
      emit(state.copyWith(
        tutorialStates: tutorialResponse.data,
        tutorialStatus: TutorialStatus.success,
      ));
    } on DioException catch (e, s) {
      _logger.error('Error fetching tutorial states ${e.message} $s');
      emit(state.copyWith(tutorialStatus: TutorialStatus.error));
    } catch (e, s) {
      _logger.error('Error fetching tutorial states ${e.toString()} $s');
      emit(state.copyWith(tutorialStatus: TutorialStatus.error));
    }
  }

  Future<void> _setTutorialStates(_SetTutorialStates event, Emitter<AuthState> emit) async {
    emit(state.copyWith(
      tutorialStates: event.tutorialStates,
      tutorialStatus: TutorialStatus.success,
    ));
  }

  Future<void> _updateTutorialStates(_UpdateTutorialStates event, Emitter<AuthState> emit) async {
    if (state.tutorialStates == null) return;

    try {
      final currentStates = state.tutorialStates!;
      final updatedStates = currentStates.updateWith(event.states);
      
      emit(state.copyWith(tutorialStates: updatedStates));

      await _updateTutorialStatesUseCase(TutorialStatesRequest.updateFromStates(updatedStates, event.states));
    } catch (e, s) {
      _logger.error('Error updating tutorial states ${e.toString()} $s');
      emit(state.copyWith(tutorialStatus: TutorialStatus.error));
    }
  }

  void dispose() {
    _tokenSubscription.cancel();
    _deepLinkSubscription.cancel();
    super.close();
  }
}
