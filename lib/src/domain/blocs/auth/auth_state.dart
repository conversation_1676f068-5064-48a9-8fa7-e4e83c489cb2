part of 'auth_bloc.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isAuthenticated,
    @Default(false) bool isEmailVerified,
    User? user,
    @Default(UserStatus.idle) UserStatus userStatus,
    String? resetPasswordToken,
    String? userError,
    TutorialStatesData? tutorialStates,
    @Default(TutorialStatus.idle) TutorialStatus tutorialStatus,
  }) = _AuthState;
}

enum UserStatus { idle, loading, success, error }
enum TutorialStatus { idle, loading, success, error }