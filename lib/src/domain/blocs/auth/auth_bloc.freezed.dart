// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthEventCopyWith<$Res> {
  factory $AuthEventCopyWith(AuthEvent value, $Res Function(AuthEvent) then) =
      _$AuthEventCopyWithImpl<$Res, AuthEvent>;
}

/// @nodoc
class _$AuthEventCopyWithImpl<$Res, $Val extends AuthEvent>
    implements $AuthEventCopyWith<$Res> {
  _$AuthEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$FetchUserImplCopyWith<$Res> {
  factory _$$FetchUserImplCopyWith(
          _$FetchUserImpl value, $Res Function(_$FetchUserImpl) then) =
      __$$FetchUserImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FetchUserImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$FetchUserImpl>
    implements _$$FetchUserImplCopyWith<$Res> {
  __$$FetchUserImplCopyWithImpl(
      _$FetchUserImpl _value, $Res Function(_$FetchUserImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FetchUserImpl implements _FetchUser {
  const _$FetchUserImpl();

  @override
  String toString() {
    return 'AuthEvent.fetchUser()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FetchUserImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return fetchUser();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return fetchUser?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (fetchUser != null) {
      return fetchUser();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return fetchUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return fetchUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (fetchUser != null) {
      return fetchUser(this);
    }
    return orElse();
  }
}

abstract class _FetchUser implements AuthEvent {
  const factory _FetchUser() = _$FetchUserImpl;
}

/// @nodoc
abstract class _$$SetUserImplCopyWith<$Res> {
  factory _$$SetUserImplCopyWith(
          _$SetUserImpl value, $Res Function(_$SetUserImpl) then) =
      __$$SetUserImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User user});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$SetUserImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SetUserImpl>
    implements _$$SetUserImplCopyWith<$Res> {
  __$$SetUserImplCopyWithImpl(
      _$SetUserImpl _value, $Res Function(_$SetUserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$SetUserImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$SetUserImpl implements _SetUser {
  const _$SetUserImpl(this.user);

  @override
  final User user;

  @override
  String toString() {
    return 'AuthEvent.setUser(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUserImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUserImplCopyWith<_$SetUserImpl> get copyWith =>
      __$$SetUserImplCopyWithImpl<_$SetUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return setUser(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return setUser?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setUser != null) {
      return setUser(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return setUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return setUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setUser != null) {
      return setUser(this);
    }
    return orElse();
  }
}

abstract class _SetUser implements AuthEvent {
  const factory _SetUser(final User user) = _$SetUserImpl;

  User get user;
  @JsonKey(ignore: true)
  _$$SetUserImplCopyWith<_$SetUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetAuthenticationImplCopyWith<$Res> {
  factory _$$SetAuthenticationImplCopyWith(_$SetAuthenticationImpl value,
          $Res Function(_$SetAuthenticationImpl) then) =
      __$$SetAuthenticationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetAuthenticationImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SetAuthenticationImpl>
    implements _$$SetAuthenticationImplCopyWith<$Res> {
  __$$SetAuthenticationImplCopyWithImpl(_$SetAuthenticationImpl _value,
      $Res Function(_$SetAuthenticationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetAuthenticationImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetAuthenticationImpl implements _SetAuthentication {
  const _$SetAuthenticationImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'AuthEvent.setAuthentication(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetAuthenticationImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetAuthenticationImplCopyWith<_$SetAuthenticationImpl> get copyWith =>
      __$$SetAuthenticationImplCopyWithImpl<_$SetAuthenticationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return setAuthentication(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return setAuthentication?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setAuthentication != null) {
      return setAuthentication(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return setAuthentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return setAuthentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setAuthentication != null) {
      return setAuthentication(this);
    }
    return orElse();
  }
}

abstract class _SetAuthentication implements AuthEvent {
  const factory _SetAuthentication(final bool value) = _$SetAuthenticationImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetAuthenticationImplCopyWith<_$SetAuthenticationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetIsEmailVerifiedImplCopyWith<$Res> {
  factory _$$SetIsEmailVerifiedImplCopyWith(_$SetIsEmailVerifiedImpl value,
          $Res Function(_$SetIsEmailVerifiedImpl) then) =
      __$$SetIsEmailVerifiedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetIsEmailVerifiedImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SetIsEmailVerifiedImpl>
    implements _$$SetIsEmailVerifiedImplCopyWith<$Res> {
  __$$SetIsEmailVerifiedImplCopyWithImpl(_$SetIsEmailVerifiedImpl _value,
      $Res Function(_$SetIsEmailVerifiedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetIsEmailVerifiedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetIsEmailVerifiedImpl implements _SetIsEmailVerified {
  const _$SetIsEmailVerifiedImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'AuthEvent.setIsEmailVerified(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetIsEmailVerifiedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetIsEmailVerifiedImplCopyWith<_$SetIsEmailVerifiedImpl> get copyWith =>
      __$$SetIsEmailVerifiedImplCopyWithImpl<_$SetIsEmailVerifiedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return setIsEmailVerified(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return setIsEmailVerified?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setIsEmailVerified != null) {
      return setIsEmailVerified(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return setIsEmailVerified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return setIsEmailVerified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setIsEmailVerified != null) {
      return setIsEmailVerified(this);
    }
    return orElse();
  }
}

abstract class _SetIsEmailVerified implements AuthEvent {
  const factory _SetIsEmailVerified(final bool value) =
      _$SetIsEmailVerifiedImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetIsEmailVerifiedImplCopyWith<_$SetIsEmailVerifiedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveUserImplCopyWith<$Res> {
  factory _$$RemoveUserImplCopyWith(
          _$RemoveUserImpl value, $Res Function(_$RemoveUserImpl) then) =
      __$$RemoveUserImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RemoveUserImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$RemoveUserImpl>
    implements _$$RemoveUserImplCopyWith<$Res> {
  __$$RemoveUserImplCopyWithImpl(
      _$RemoveUserImpl _value, $Res Function(_$RemoveUserImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RemoveUserImpl implements _RemoveUser {
  const _$RemoveUserImpl();

  @override
  String toString() {
    return 'AuthEvent.removeUser()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RemoveUserImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return removeUser();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return removeUser?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (removeUser != null) {
      return removeUser();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return removeUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return removeUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (removeUser != null) {
      return removeUser(this);
    }
    return orElse();
  }
}

abstract class _RemoveUser implements AuthEvent {
  const factory _RemoveUser() = _$RemoveUserImpl;
}

/// @nodoc
abstract class _$$LogoutImplCopyWith<$Res> {
  factory _$$LogoutImplCopyWith(
          _$LogoutImpl value, $Res Function(_$LogoutImpl) then) =
      __$$LogoutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LogoutImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$LogoutImpl>
    implements _$$LogoutImplCopyWith<$Res> {
  __$$LogoutImplCopyWithImpl(
      _$LogoutImpl _value, $Res Function(_$LogoutImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$LogoutImpl implements _Logout {
  const _$LogoutImpl();

  @override
  String toString() {
    return 'AuthEvent.logout()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LogoutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return logout();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return logout?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return logout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return logout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout(this);
    }
    return orElse();
  }
}

abstract class _Logout implements AuthEvent {
  const factory _Logout() = _$LogoutImpl;
}

/// @nodoc
abstract class _$$ChangeResetPasswordTokenImplCopyWith<$Res> {
  factory _$$ChangeResetPasswordTokenImplCopyWith(
          _$ChangeResetPasswordTokenImpl value,
          $Res Function(_$ChangeResetPasswordTokenImpl) then) =
      __$$ChangeResetPasswordTokenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? token});
}

/// @nodoc
class __$$ChangeResetPasswordTokenImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$ChangeResetPasswordTokenImpl>
    implements _$$ChangeResetPasswordTokenImplCopyWith<$Res> {
  __$$ChangeResetPasswordTokenImplCopyWithImpl(
      _$ChangeResetPasswordTokenImpl _value,
      $Res Function(_$ChangeResetPasswordTokenImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
  }) {
    return _then(_$ChangeResetPasswordTokenImpl(
      freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ChangeResetPasswordTokenImpl implements _ChangeResetPasswordToken {
  const _$ChangeResetPasswordTokenImpl(this.token);

  @override
  final String? token;

  @override
  String toString() {
    return 'AuthEvent.changeResetPasswordToken(token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeResetPasswordTokenImpl &&
            (identical(other.token, token) || other.token == token));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeResetPasswordTokenImplCopyWith<_$ChangeResetPasswordTokenImpl>
      get copyWith => __$$ChangeResetPasswordTokenImplCopyWithImpl<
          _$ChangeResetPasswordTokenImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return changeResetPasswordToken(token);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return changeResetPasswordToken?.call(token);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (changeResetPasswordToken != null) {
      return changeResetPasswordToken(token);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return changeResetPasswordToken(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return changeResetPasswordToken?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (changeResetPasswordToken != null) {
      return changeResetPasswordToken(this);
    }
    return orElse();
  }
}

abstract class _ChangeResetPasswordToken implements AuthEvent {
  const factory _ChangeResetPasswordToken(final String? token) =
      _$ChangeResetPasswordTokenImpl;

  String? get token;
  @JsonKey(ignore: true)
  _$$ChangeResetPasswordTokenImplCopyWith<_$ChangeResetPasswordTokenImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitiateRestaurantSessionImplCopyWith<$Res> {
  factory _$$InitiateRestaurantSessionImplCopyWith(
          _$InitiateRestaurantSessionImpl value,
          $Res Function(_$InitiateRestaurantSessionImpl) then) =
      __$$InitiateRestaurantSessionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String token});
}

/// @nodoc
class __$$InitiateRestaurantSessionImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$InitiateRestaurantSessionImpl>
    implements _$$InitiateRestaurantSessionImplCopyWith<$Res> {
  __$$InitiateRestaurantSessionImplCopyWithImpl(
      _$InitiateRestaurantSessionImpl _value,
      $Res Function(_$InitiateRestaurantSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
  }) {
    return _then(_$InitiateRestaurantSessionImpl(
      null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$InitiateRestaurantSessionImpl implements _InitiateRestaurantSession {
  const _$InitiateRestaurantSessionImpl(this.token);

  @override
  final String token;

  @override
  String toString() {
    return 'AuthEvent.initiateRestaurantSession(token: $token)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitiateRestaurantSessionImpl &&
            (identical(other.token, token) || other.token == token));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InitiateRestaurantSessionImplCopyWith<_$InitiateRestaurantSessionImpl>
      get copyWith => __$$InitiateRestaurantSessionImplCopyWithImpl<
          _$InitiateRestaurantSessionImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return initiateRestaurantSession(token);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return initiateRestaurantSession?.call(token);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (initiateRestaurantSession != null) {
      return initiateRestaurantSession(token);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return initiateRestaurantSession(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return initiateRestaurantSession?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (initiateRestaurantSession != null) {
      return initiateRestaurantSession(this);
    }
    return orElse();
  }
}

abstract class _InitiateRestaurantSession implements AuthEvent {
  const factory _InitiateRestaurantSession(final String token) =
      _$InitiateRestaurantSessionImpl;

  String get token;
  @JsonKey(ignore: true)
  _$$InitiateRestaurantSessionImplCopyWith<_$InitiateRestaurantSessionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FetchTutorialStatesImplCopyWith<$Res> {
  factory _$$FetchTutorialStatesImplCopyWith(_$FetchTutorialStatesImpl value,
          $Res Function(_$FetchTutorialStatesImpl) then) =
      __$$FetchTutorialStatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FetchTutorialStatesImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$FetchTutorialStatesImpl>
    implements _$$FetchTutorialStatesImplCopyWith<$Res> {
  __$$FetchTutorialStatesImplCopyWithImpl(_$FetchTutorialStatesImpl _value,
      $Res Function(_$FetchTutorialStatesImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$FetchTutorialStatesImpl implements _FetchTutorialStates {
  const _$FetchTutorialStatesImpl();

  @override
  String toString() {
    return 'AuthEvent.fetchTutorialStates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchTutorialStatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return fetchTutorialStates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return fetchTutorialStates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (fetchTutorialStates != null) {
      return fetchTutorialStates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return fetchTutorialStates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return fetchTutorialStates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (fetchTutorialStates != null) {
      return fetchTutorialStates(this);
    }
    return orElse();
  }
}

abstract class _FetchTutorialStates implements AuthEvent {
  const factory _FetchTutorialStates() = _$FetchTutorialStatesImpl;
}

/// @nodoc
abstract class _$$SetTutorialStatesImplCopyWith<$Res> {
  factory _$$SetTutorialStatesImplCopyWith(_$SetTutorialStatesImpl value,
          $Res Function(_$SetTutorialStatesImpl) then) =
      __$$SetTutorialStatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TutorialStatesData tutorialStates});

  $TutorialStatesDataCopyWith<$Res> get tutorialStates;
}

/// @nodoc
class __$$SetTutorialStatesImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$SetTutorialStatesImpl>
    implements _$$SetTutorialStatesImplCopyWith<$Res> {
  __$$SetTutorialStatesImplCopyWithImpl(_$SetTutorialStatesImpl _value,
      $Res Function(_$SetTutorialStatesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tutorialStates = null,
  }) {
    return _then(_$SetTutorialStatesImpl(
      null == tutorialStates
          ? _value.tutorialStates
          : tutorialStates // ignore: cast_nullable_to_non_nullable
              as TutorialStatesData,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $TutorialStatesDataCopyWith<$Res> get tutorialStates {
    return $TutorialStatesDataCopyWith<$Res>(_value.tutorialStates, (value) {
      return _then(_value.copyWith(tutorialStates: value));
    });
  }
}

/// @nodoc

class _$SetTutorialStatesImpl implements _SetTutorialStates {
  const _$SetTutorialStatesImpl(this.tutorialStates);

  @override
  final TutorialStatesData tutorialStates;

  @override
  String toString() {
    return 'AuthEvent.setTutorialStates(tutorialStates: $tutorialStates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetTutorialStatesImpl &&
            (identical(other.tutorialStates, tutorialStates) ||
                other.tutorialStates == tutorialStates));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tutorialStates);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetTutorialStatesImplCopyWith<_$SetTutorialStatesImpl> get copyWith =>
      __$$SetTutorialStatesImplCopyWithImpl<_$SetTutorialStatesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return setTutorialStates(tutorialStates);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return setTutorialStates?.call(tutorialStates);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setTutorialStates != null) {
      return setTutorialStates(tutorialStates);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return setTutorialStates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return setTutorialStates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (setTutorialStates != null) {
      return setTutorialStates(this);
    }
    return orElse();
  }
}

abstract class _SetTutorialStates implements AuthEvent {
  const factory _SetTutorialStates(final TutorialStatesData tutorialStates) =
      _$SetTutorialStatesImpl;

  TutorialStatesData get tutorialStates;
  @JsonKey(ignore: true)
  _$$SetTutorialStatesImplCopyWith<_$SetTutorialStatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateTutorialStatesImplCopyWith<$Res> {
  factory _$$UpdateTutorialStatesImplCopyWith(_$UpdateTutorialStatesImpl value,
          $Res Function(_$UpdateTutorialStatesImpl) then) =
      __$$UpdateTutorialStatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, bool> states});
}

/// @nodoc
class __$$UpdateTutorialStatesImplCopyWithImpl<$Res>
    extends _$AuthEventCopyWithImpl<$Res, _$UpdateTutorialStatesImpl>
    implements _$$UpdateTutorialStatesImplCopyWith<$Res> {
  __$$UpdateTutorialStatesImplCopyWithImpl(_$UpdateTutorialStatesImpl _value,
      $Res Function(_$UpdateTutorialStatesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? states = null,
  }) {
    return _then(_$UpdateTutorialStatesImpl(
      null == states
          ? _value._states
          : states // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// @nodoc

class _$UpdateTutorialStatesImpl implements _UpdateTutorialStates {
  const _$UpdateTutorialStatesImpl(final Map<String, bool> states)
      : _states = states;

  final Map<String, bool> _states;
  @override
  Map<String, bool> get states {
    if (_states is EqualUnmodifiableMapView) return _states;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_states);
  }

  @override
  String toString() {
    return 'AuthEvent.updateTutorialStates(states: $states)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTutorialStatesImpl &&
            const DeepCollectionEquality().equals(other._states, _states));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_states));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTutorialStatesImplCopyWith<_$UpdateTutorialStatesImpl>
      get copyWith =>
          __$$UpdateTutorialStatesImplCopyWithImpl<_$UpdateTutorialStatesImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetchUser,
    required TResult Function(User user) setUser,
    required TResult Function(bool value) setAuthentication,
    required TResult Function(bool value) setIsEmailVerified,
    required TResult Function() removeUser,
    required TResult Function() logout,
    required TResult Function(String? token) changeResetPasswordToken,
    required TResult Function(String token) initiateRestaurantSession,
    required TResult Function() fetchTutorialStates,
    required TResult Function(TutorialStatesData tutorialStates)
        setTutorialStates,
    required TResult Function(Map<String, bool> states) updateTutorialStates,
  }) {
    return updateTutorialStates(states);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetchUser,
    TResult? Function(User user)? setUser,
    TResult? Function(bool value)? setAuthentication,
    TResult? Function(bool value)? setIsEmailVerified,
    TResult? Function()? removeUser,
    TResult? Function()? logout,
    TResult? Function(String? token)? changeResetPasswordToken,
    TResult? Function(String token)? initiateRestaurantSession,
    TResult? Function()? fetchTutorialStates,
    TResult? Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult? Function(Map<String, bool> states)? updateTutorialStates,
  }) {
    return updateTutorialStates?.call(states);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetchUser,
    TResult Function(User user)? setUser,
    TResult Function(bool value)? setAuthentication,
    TResult Function(bool value)? setIsEmailVerified,
    TResult Function()? removeUser,
    TResult Function()? logout,
    TResult Function(String? token)? changeResetPasswordToken,
    TResult Function(String token)? initiateRestaurantSession,
    TResult Function()? fetchTutorialStates,
    TResult Function(TutorialStatesData tutorialStates)? setTutorialStates,
    TResult Function(Map<String, bool> states)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (updateTutorialStates != null) {
      return updateTutorialStates(states);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchUser value) fetchUser,
    required TResult Function(_SetUser value) setUser,
    required TResult Function(_SetAuthentication value) setAuthentication,
    required TResult Function(_SetIsEmailVerified value) setIsEmailVerified,
    required TResult Function(_RemoveUser value) removeUser,
    required TResult Function(_Logout value) logout,
    required TResult Function(_ChangeResetPasswordToken value)
        changeResetPasswordToken,
    required TResult Function(_InitiateRestaurantSession value)
        initiateRestaurantSession,
    required TResult Function(_FetchTutorialStates value) fetchTutorialStates,
    required TResult Function(_SetTutorialStates value) setTutorialStates,
    required TResult Function(_UpdateTutorialStates value) updateTutorialStates,
  }) {
    return updateTutorialStates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchUser value)? fetchUser,
    TResult? Function(_SetUser value)? setUser,
    TResult? Function(_SetAuthentication value)? setAuthentication,
    TResult? Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult? Function(_RemoveUser value)? removeUser,
    TResult? Function(_Logout value)? logout,
    TResult? Function(_ChangeResetPasswordToken value)?
        changeResetPasswordToken,
    TResult? Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult? Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult? Function(_SetTutorialStates value)? setTutorialStates,
    TResult? Function(_UpdateTutorialStates value)? updateTutorialStates,
  }) {
    return updateTutorialStates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchUser value)? fetchUser,
    TResult Function(_SetUser value)? setUser,
    TResult Function(_SetAuthentication value)? setAuthentication,
    TResult Function(_SetIsEmailVerified value)? setIsEmailVerified,
    TResult Function(_RemoveUser value)? removeUser,
    TResult Function(_Logout value)? logout,
    TResult Function(_ChangeResetPasswordToken value)? changeResetPasswordToken,
    TResult Function(_InitiateRestaurantSession value)?
        initiateRestaurantSession,
    TResult Function(_FetchTutorialStates value)? fetchTutorialStates,
    TResult Function(_SetTutorialStates value)? setTutorialStates,
    TResult Function(_UpdateTutorialStates value)? updateTutorialStates,
    required TResult orElse(),
  }) {
    if (updateTutorialStates != null) {
      return updateTutorialStates(this);
    }
    return orElse();
  }
}

abstract class _UpdateTutorialStates implements AuthEvent {
  const factory _UpdateTutorialStates(final Map<String, bool> states) =
      _$UpdateTutorialStatesImpl;

  Map<String, bool> get states;
  @JsonKey(ignore: true)
  _$$UpdateTutorialStatesImplCopyWith<_$UpdateTutorialStatesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AuthState {
  bool get isAuthenticated => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  User? get user => throw _privateConstructorUsedError;
  UserStatus get userStatus => throw _privateConstructorUsedError;
  String? get resetPasswordToken => throw _privateConstructorUsedError;
  String? get userError => throw _privateConstructorUsedError;
  TutorialStatesData? get tutorialStates => throw _privateConstructorUsedError;
  TutorialStatus get tutorialStatus => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AuthStateCopyWith<AuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthStateCopyWith<$Res> {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) then) =
      _$AuthStateCopyWithImpl<$Res, AuthState>;
  @useResult
  $Res call(
      {bool isAuthenticated,
      bool isEmailVerified,
      User? user,
      UserStatus userStatus,
      String? resetPasswordToken,
      String? userError,
      TutorialStatesData? tutorialStates,
      TutorialStatus tutorialStatus});

  $UserCopyWith<$Res>? get user;
  $TutorialStatesDataCopyWith<$Res>? get tutorialStates;
}

/// @nodoc
class _$AuthStateCopyWithImpl<$Res, $Val extends AuthState>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAuthenticated = null,
    Object? isEmailVerified = null,
    Object? user = freezed,
    Object? userStatus = null,
    Object? resetPasswordToken = freezed,
    Object? userError = freezed,
    Object? tutorialStates = freezed,
    Object? tutorialStatus = null,
  }) {
    return _then(_value.copyWith(
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userStatus: null == userStatus
          ? _value.userStatus
          : userStatus // ignore: cast_nullable_to_non_nullable
              as UserStatus,
      resetPasswordToken: freezed == resetPasswordToken
          ? _value.resetPasswordToken
          : resetPasswordToken // ignore: cast_nullable_to_non_nullable
              as String?,
      userError: freezed == userError
          ? _value.userError
          : userError // ignore: cast_nullable_to_non_nullable
              as String?,
      tutorialStates: freezed == tutorialStates
          ? _value.tutorialStates
          : tutorialStates // ignore: cast_nullable_to_non_nullable
              as TutorialStatesData?,
      tutorialStatus: null == tutorialStatus
          ? _value.tutorialStatus
          : tutorialStatus // ignore: cast_nullable_to_non_nullable
              as TutorialStatus,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $TutorialStatesDataCopyWith<$Res>? get tutorialStates {
    if (_value.tutorialStates == null) {
      return null;
    }

    return $TutorialStatesDataCopyWith<$Res>(_value.tutorialStates!, (value) {
      return _then(_value.copyWith(tutorialStates: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthStateImplCopyWith<$Res>
    implements $AuthStateCopyWith<$Res> {
  factory _$$AuthStateImplCopyWith(
          _$AuthStateImpl value, $Res Function(_$AuthStateImpl) then) =
      __$$AuthStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isAuthenticated,
      bool isEmailVerified,
      User? user,
      UserStatus userStatus,
      String? resetPasswordToken,
      String? userError,
      TutorialStatesData? tutorialStates,
      TutorialStatus tutorialStatus});

  @override
  $UserCopyWith<$Res>? get user;
  @override
  $TutorialStatesDataCopyWith<$Res>? get tutorialStates;
}

/// @nodoc
class __$$AuthStateImplCopyWithImpl<$Res>
    extends _$AuthStateCopyWithImpl<$Res, _$AuthStateImpl>
    implements _$$AuthStateImplCopyWith<$Res> {
  __$$AuthStateImplCopyWithImpl(
      _$AuthStateImpl _value, $Res Function(_$AuthStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAuthenticated = null,
    Object? isEmailVerified = null,
    Object? user = freezed,
    Object? userStatus = null,
    Object? resetPasswordToken = freezed,
    Object? userError = freezed,
    Object? tutorialStates = freezed,
    Object? tutorialStatus = null,
  }) {
    return _then(_$AuthStateImpl(
      isAuthenticated: null == isAuthenticated
          ? _value.isAuthenticated
          : isAuthenticated // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      userStatus: null == userStatus
          ? _value.userStatus
          : userStatus // ignore: cast_nullable_to_non_nullable
              as UserStatus,
      resetPasswordToken: freezed == resetPasswordToken
          ? _value.resetPasswordToken
          : resetPasswordToken // ignore: cast_nullable_to_non_nullable
              as String?,
      userError: freezed == userError
          ? _value.userError
          : userError // ignore: cast_nullable_to_non_nullable
              as String?,
      tutorialStates: freezed == tutorialStates
          ? _value.tutorialStates
          : tutorialStates // ignore: cast_nullable_to_non_nullable
              as TutorialStatesData?,
      tutorialStatus: null == tutorialStatus
          ? _value.tutorialStatus
          : tutorialStatus // ignore: cast_nullable_to_non_nullable
              as TutorialStatus,
    ));
  }
}

/// @nodoc

class _$AuthStateImpl implements _AuthState {
  const _$AuthStateImpl(
      {this.isAuthenticated = false,
      this.isEmailVerified = false,
      this.user,
      this.userStatus = UserStatus.idle,
      this.resetPasswordToken,
      this.userError,
      this.tutorialStates,
      this.tutorialStatus = TutorialStatus.idle});

  @override
  @JsonKey()
  final bool isAuthenticated;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  final User? user;
  @override
  @JsonKey()
  final UserStatus userStatus;
  @override
  final String? resetPasswordToken;
  @override
  final String? userError;
  @override
  final TutorialStatesData? tutorialStates;
  @override
  @JsonKey()
  final TutorialStatus tutorialStatus;

  @override
  String toString() {
    return 'AuthState(isAuthenticated: $isAuthenticated, isEmailVerified: $isEmailVerified, user: $user, userStatus: $userStatus, resetPasswordToken: $resetPasswordToken, userError: $userError, tutorialStates: $tutorialStates, tutorialStatus: $tutorialStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthStateImpl &&
            (identical(other.isAuthenticated, isAuthenticated) ||
                other.isAuthenticated == isAuthenticated) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.userStatus, userStatus) ||
                other.userStatus == userStatus) &&
            (identical(other.resetPasswordToken, resetPasswordToken) ||
                other.resetPasswordToken == resetPasswordToken) &&
            (identical(other.userError, userError) ||
                other.userError == userError) &&
            (identical(other.tutorialStates, tutorialStates) ||
                other.tutorialStates == tutorialStates) &&
            (identical(other.tutorialStatus, tutorialStatus) ||
                other.tutorialStatus == tutorialStatus));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isAuthenticated,
      isEmailVerified,
      user,
      userStatus,
      resetPasswordToken,
      userError,
      tutorialStates,
      tutorialStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      __$$AuthStateImplCopyWithImpl<_$AuthStateImpl>(this, _$identity);
}

abstract class _AuthState implements AuthState {
  const factory _AuthState(
      {final bool isAuthenticated,
      final bool isEmailVerified,
      final User? user,
      final UserStatus userStatus,
      final String? resetPasswordToken,
      final String? userError,
      final TutorialStatesData? tutorialStates,
      final TutorialStatus tutorialStatus}) = _$AuthStateImpl;

  @override
  bool get isAuthenticated;
  @override
  bool get isEmailVerified;
  @override
  User? get user;
  @override
  UserStatus get userStatus;
  @override
  String? get resetPasswordToken;
  @override
  String? get userError;
  @override
  TutorialStatesData? get tutorialStates;
  @override
  TutorialStatus get tutorialStatus;
  @override
  @JsonKey(ignore: true)
  _$$AuthStateImplCopyWith<_$AuthStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
