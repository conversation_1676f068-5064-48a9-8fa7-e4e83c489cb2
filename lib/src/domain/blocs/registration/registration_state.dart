part of 'registration_bloc.dart';

@freezed
class RegistrationState with _$RegistrationState {
  const factory RegistrationState({
    @Default(RegistrationStatus.idle) RegistrationStatus status,
    @Default(RegistrationWithSocialStatus.idle) RegistrationWithSocialStatus socialStatus,
    Gender? gender,
    @Default('') String firstName,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus firstNameValidationStatus,
    @Default('') String lastName,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus lastNameValidationStatus,
    @Default('') String userName,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus userNameValidationStatus,
    @Default('') String birthDate,
    @Default((code: '', number: '')) ({String code, String number}) phoneNumber,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus phoneNumberValidationStatus,
    @Default('') String email,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus emailValidationStatus,
    @Default('') String password,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus passwordValidationStatus,
    @Default('') String confirmPassword,
    @Default(ValidationStatus.idleOrInvalid) ValidationStatus confirmPasswordValidationStatus,
    @Default(true) bool isObscured,
    @Default(true) bool isConfirmObscured,
    @Default(false) bool isPrivacyAccepted,
    SocialAuthOnBoardingRequest? socialAuthOnBoardingRequest,
    @Default(false) bool isRegistered,
    String? firstNameError,
    String? lastNameError,
    String? userNameError,
    String? birthDateError,
    String? phoneNumberError,
    String? emailError,
    String? passwordError,
    String? confirmPasswordError,
    String? privacyError,
    String? registrationError,
    String? socialRegistrationError,
  }) = _RegistrationState;
}

enum RegistrationStatus { idle, loading, success, error }
enum RegistrationWithSocialStatus { idle, loading, success, error }