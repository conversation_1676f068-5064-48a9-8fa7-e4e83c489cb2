part of 'registration_bloc.dart';

@freezed
class RegistrationEvent with _$RegistrationEvent {
  const factory RegistrationEvent.changeGender(Gender gender) = _ChangeGender;
  const factory RegistrationEvent.changeFirstName(String firstName) = _ChangeFirstName;
  const factory RegistrationEvent.changeLastName(String lastName) = _ChangeLastName;
  const factory RegistrationEvent.changeUserName(String userName) = _ChangeUserName;
  const factory RegistrationEvent.changeDateOfBirth(String date) = _ChangeDateOfBirth;
  const factory RegistrationEvent.changeEmail(String email) = _ChangeEmail;
  const factory RegistrationEvent.changePhoneNumber(({String code, String number}) phoneNumber) = _ChangePhoneNumber;
  const factory RegistrationEvent.changePassword(String password) = _ChangePassword;
  const factory RegistrationEvent.changeConfirmPassword(String confirmPassword) = _ChangeConfirmPassword;
  const factory RegistrationEvent.changeObscureText() = _ChangeObscureText;
  const factory RegistrationEvent.changeConfirmObscureText() = _ChangeConfirmObscureText;
  const factory RegistrationEvent.validateEmail() = _ValidateEmail;
  const factory RegistrationEvent.validatePassword() = _ValidatePassword;
  const factory RegistrationEvent.validateConfirmPassword() = _ValidateConfirmPassword;
  const factory RegistrationEvent.validateFirstName() = _ValidateFirstName;
  const factory RegistrationEvent.validateLastName() = _ValidateLastName;
  const factory RegistrationEvent.validateUserName() = _ValidateUserName;
  const factory RegistrationEvent.validateBirthDate() = _ValidateBirthDate;
  const factory RegistrationEvent.validatePhoneNumber() = _ValidatePhoneNumber;
  const factory RegistrationEvent.acceptPrivacyPolicy(bool value) = _AcceptPrivacyPolicy;
  const factory RegistrationEvent.submit() = _Submit;
  const factory RegistrationEvent.registerWithSocial(SocialAuthType type) = _RegisterWithSocial;
}