import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

part 'registration_event.dart';
part 'registration_state.dart';
part 'registration_bloc.freezed.dart';

class RegistrationBloc extends Bloc<RegistrationEvent, RegistrationState> with ValidationMixin {
  final RegistrationUseCase _registrationUseCase;
  final SignInWithSocialUseCase _signInWithSocialUseCase;
  final TokenHandler _tokenHandler;
  final SetIsEmailVerifiedUseCase _setIsEmailVerifiedUseCase;
  final MLogger _logger;

  RegistrationBloc(
    this._registrationUseCase,
    this._signInWithSocialUseCase,
    this._tokenHandler,
    this._setIsEmailVerifiedUseCase,
    this._logger
  ) : super(const RegistrationState()) {
    on<_ChangeGender>(_changeGender);
    on<_ChangeFirstName>(_changeFirstName);
    on<_ChangeLastName>(_changeLastName);
    on<_ChangeUserName>(_changeUserName);
    on<_ChangeDateOfBirth>(_changeDateOfBirth);
    on<_ChangeEmail>(_changeEmail);
    on<_ChangePhoneNumber>(_changePhoneNumber);
    on<_ChangePassword>(_changePassword);
    on<_ChangeConfirmPassword>(_changeConfirmPassword);
    on<_ChangeObscureText>(_changeObscureText);
    on<_ChangeConfirmObscureText>(_changeConfirmObscureText);

    on<_ValidateFirstName>(_validateFirstName);
    on<_ValidateLastName>(_validateLastName);
    on<_ValidateUserName>(_validateUserName);
    on<_ValidateBirthDate>(_validateBirthDate);
    on<_ValidatePhoneNumber>(_validatePhoneNumber);
    on<_ValidateEmail>(_validateEmail);
    on<_ValidatePassword>(_validatePassword);
    on<_ValidateConfirmPassword>(_validateConfirmPassword);

    on<_AcceptPrivacyPolicy>(_acceptPrivacyPolicy);

    on<_Submit>(_submit);
    on<_RegisterWithSocial>(_registerWithSocial);
  }

  void _changeGender(_ChangeGender event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(gender: event.gender));
  }

  void _changeFirstName(_ChangeFirstName event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      firstName: event.firstName,
      firstNameError: null,
      firstNameValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validateFirstName(_ValidateFirstName event, Emitter<RegistrationState> emit) {
    final firstNameError = state.firstName.isNotEmpty ?
      validateTextField(state.firstName) : null;
    emit(state.copyWith(
      firstNameError: firstNameError,
      firstNameValidationStatus: firstNameError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changeLastName(_ChangeLastName event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      lastName: event.lastName,
      lastNameError: null,
      lastNameValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validateLastName(_ValidateLastName event, Emitter<RegistrationState> emit) {
    final lastNameError = state.lastName.isNotEmpty ?
      validateTextField(state.lastName) : null;
    emit(state.copyWith(
      lastNameError: lastNameError,
      lastNameValidationStatus: lastNameError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changeUserName(_ChangeUserName event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      userName: event.userName,
      userNameError: null,
      userNameValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validatePhoneNumber(_ValidatePhoneNumber event, Emitter<RegistrationState> emit) {
    final phoneNumberError = state.phoneNumber.number.isNotEmpty ?
      validatePhoneNumberField('${state.phoneNumber.code}${state.phoneNumber.number}') : null;
    emit(state.copyWith(
      phoneNumberError: phoneNumberError,
      phoneNumberValidationStatus: phoneNumberError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _validateUserName(_ValidateUserName event, Emitter<RegistrationState> emit) {
    final userNameError = validateTextField(state.userName);
    emit(state.copyWith(
      userNameError: userNameError,
      userNameValidationStatus: userNameError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changeDateOfBirth(_ChangeDateOfBirth event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      birthDate: event.date,
      birthDateError: null
    ));
  }

  void _validateBirthDate(_ValidateBirthDate event, Emitter<RegistrationState> emit) {
    final birthDateError = validatePlainValue(state.birthDate);
    emit(state.copyWith(birthDateError: birthDateError));
  }

  void _changeEmail(_ChangeEmail event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      email: event.email,
      emailError: null,
      emailValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validateEmail(_ValidateEmail event, Emitter<RegistrationState> emit) {
    final emailError = validateEmailField(state.email);
    emit(state.copyWith(
      emailError: emailError,
      emailValidationStatus: emailError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changePhoneNumber(_ChangePhoneNumber event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      phoneNumber: event.phoneNumber,
      phoneNumberError: null,
      phoneNumberValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _changePassword(_ChangePassword event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      password: event.password,
      passwordError: null,
      passwordValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validatePassword(_ValidatePassword event, Emitter<RegistrationState> emit) {
    final passwordError = validatePasswordField(state.password);
    emit(state.copyWith(
      passwordError: passwordError,
      passwordValidationStatus: passwordError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changeConfirmPassword(_ChangeConfirmPassword event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      confirmPassword: event.confirmPassword,
      confirmPasswordError: null,
      confirmPasswordValidationStatus: ValidationStatus.idleOrInvalid
    ));
  }

  void _validateConfirmPassword(_ValidateConfirmPassword event, Emitter<RegistrationState> emit) {
    final confirmPasswordError = validatePasswordField(state.confirmPassword) ??
      validateMatchPasswords(state.confirmPassword, state.password);
    emit(state.copyWith(
      confirmPasswordError: confirmPasswordError,
      confirmPasswordValidationStatus: confirmPasswordError == null ?
        ValidationStatus.valid : ValidationStatus.idleOrInvalid
    ));
  }

  void _changeObscureText(_ChangeObscureText event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(isObscured: !state.isObscured));
  }

  void _changeConfirmObscureText(_ChangeConfirmObscureText event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(isConfirmObscured: !state.isConfirmObscured));
  }

  void _acceptPrivacyPolicy(_AcceptPrivacyPolicy event, Emitter<RegistrationState> emit) {
    emit(state.copyWith(
      isPrivacyAccepted: event.value,
      privacyError: null
    ));
  }

  Future<void> _submit(_Submit event, Emitter<RegistrationState> emit) async {
    final emailError = validateEmailField(state.email);
    final passwordError = validatePasswordField(state.password) ??
      validateMatchPasswords(state.password, state.confirmPassword);
    final confirmPasswordError = validatePasswordField(state.confirmPassword) ??
      validateMatchPasswords(state.confirmPassword, state.password);
    final privacyError = validatePrivacyCheckbox(state.isPrivacyAccepted);

    final firstNameError = state.firstName.isNotEmpty ?
      validateTextField(state.firstName) : null;
    final lastNameError = state.lastName.isNotEmpty ?
      validateTextField(state.lastName) : null;
    final userNameError = validateTextField(state.userName);

    final phoneNumberError = state.phoneNumber.number.isNotEmpty ?
      validatePhoneNumberField('${state.phoneNumber.code}${state.phoneNumber.number}') : null;
    final birthDateError = validatePlainValue(state.birthDate);

    if (emailError != null || passwordError != null || confirmPasswordError != null ||
    privacyError != null || firstNameError != null || lastNameError != null ||
    userNameError != null || phoneNumberError != null || birthDateError != null) {
      emit(state.copyWith(
        emailError: emailError,
        passwordError: passwordError,
        confirmPasswordError: confirmPasswordError,
        privacyError: privacyError,
        firstNameError: firstNameError,
        lastNameError: lastNameError,
        userNameError: userNameError,
        birthDateError: birthDateError,
        phoneNumberError: phoneNumberError,
      ));
      return;
    }

    try {
      emit(state.copyWith(
        status: RegistrationStatus.loading,
        emailError: null,
        passwordError: null,
        confirmPasswordError: null,
        privacyError: null
      ));

      final tokens = await _registrationUseCase(RegistrationRequest(
        gender: state.gender?.name.capitalize(),
        firstName: state.firstName.isNotEmpty ?
          state.firstName.trim() : null,
        lastName: state.lastName.isNotEmpty ?
          state.lastName.trim() : null,
        userName: state.userName.trim(),
        email: state.email.trim(),
        birthDate: state.birthDate.isNotEmpty ?
          state.birthDate.trim() : null,
        phoneNumber: state.phoneNumber.number.isNotEmpty ?
          '${state.phoneNumber.code}${state.phoneNumber.number}' : null,
        password: state.password.trim(),
        passwordConfirmation: state.confirmPassword.trim(),
      ));

      await _tokenHandler.saveAccessToken(tokens.data.accessToken);
      await _tokenHandler.saveRefreshToken(tokens.data.refreshToken);
      await _setIsEmailVerifiedUseCase(isEmailVerified: tokens.data.isEmailVerified);

      _tokenHandler.changeTokenValidity(true);

      emit(state.copyWith(status: RegistrationStatus.success));
    } on DioException catch (e, s) {
      emit(state.copyWith(
        status: RegistrationStatus.error,
        registrationError: e.message
      ));
      _logger.error('Problem with registration: ${e.message} $s');
    } catch (e, s) {
      emit(state.copyWith(
        status: RegistrationStatus.error,
        registrationError: e.toString()
      ));
      _logger.error('Problem with registration: $e $s');
    }

    emit(state.copyWith(status: RegistrationStatus.idle));
  }

  Future<void> _registerWithSocial(_RegisterWithSocial event, Emitter<RegistrationState> emit) async {
    emit(state.copyWith(socialStatus: RegistrationWithSocialStatus.loading));

    try {
      final (tokens, request) = await _signInWithSocialUseCase(event.type);

      await _tokenHandler.saveAccessToken(tokens.data.accessToken);
      await _tokenHandler.saveRefreshToken(tokens.data.refreshToken);
      await _setIsEmailVerifiedUseCase(isEmailVerified: tokens.data.isEmailVerified);

      _tokenHandler.changeTokenValidity(true);

      emit(state.copyWith(
        socialStatus: RegistrationWithSocialStatus.success,
        socialAuthOnBoardingRequest: request,
        isRegistered: tokens.data.isRegistered,
      ));
    } on DioException catch (e, s) {
      emit(state.copyWith(
        socialStatus: RegistrationWithSocialStatus.error,
        socialRegistrationError: e.message
      ));
      _logger.error('Problem with social registration: $e $s');
    } on SignInWithAppleAuthorizationException catch (e, s) {
      _logger.error('Problem with social registration: ${e.toString()} $s');
      if (e.code == AuthorizationErrorCode.canceled) {
        emit(state.copyWith(socialStatus: RegistrationWithSocialStatus.error, socialRegistrationError: ErrorCode.errorAuthCancelled.message));
      } else {
        emit(state.copyWith(socialStatus: RegistrationWithSocialStatus.error, socialRegistrationError: ErrorCode.errorSocialAuthFailed.message));
      }
    } catch (e,s) {
      emit(state.copyWith(
        socialStatus: RegistrationWithSocialStatus.error,
        socialRegistrationError: e.toString()
      ));
      _logger.error('Problem with social registration: $e $s');
    }

    emit(state.copyWith(socialStatus: RegistrationWithSocialStatus.idle));
  }
}
