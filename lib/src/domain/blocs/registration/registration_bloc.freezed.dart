// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'registration_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegistrationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegistrationEventCopyWith<$Res> {
  factory $RegistrationEventCopyWith(
          RegistrationEvent value, $Res Function(RegistrationEvent) then) =
      _$RegistrationEventCopyWithImpl<$Res, RegistrationEvent>;
}

/// @nodoc
class _$RegistrationEventCopyWithImpl<$Res, $Val extends RegistrationEvent>
    implements $RegistrationEventCopyWith<$Res> {
  _$RegistrationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangeGenderImplCopyWith<$Res> {
  factory _$$ChangeGenderImplCopyWith(
          _$ChangeGenderImpl value, $Res Function(_$ChangeGenderImpl) then) =
      __$$ChangeGenderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Gender gender});
}

/// @nodoc
class __$$ChangeGenderImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeGenderImpl>
    implements _$$ChangeGenderImplCopyWith<$Res> {
  __$$ChangeGenderImplCopyWithImpl(
      _$ChangeGenderImpl _value, $Res Function(_$ChangeGenderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gender = null,
  }) {
    return _then(_$ChangeGenderImpl(
      null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender,
    ));
  }
}

/// @nodoc

class _$ChangeGenderImpl implements _ChangeGender {
  const _$ChangeGenderImpl(this.gender);

  @override
  final Gender gender;

  @override
  String toString() {
    return 'RegistrationEvent.changeGender(gender: $gender)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeGenderImpl &&
            (identical(other.gender, gender) || other.gender == gender));
  }

  @override
  int get hashCode => Object.hash(runtimeType, gender);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeGenderImplCopyWith<_$ChangeGenderImpl> get copyWith =>
      __$$ChangeGenderImplCopyWithImpl<_$ChangeGenderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeGender(gender);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeGender?.call(gender);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeGender != null) {
      return changeGender(gender);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeGender(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeGender?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeGender != null) {
      return changeGender(this);
    }
    return orElse();
  }
}

abstract class _ChangeGender implements RegistrationEvent {
  const factory _ChangeGender(final Gender gender) = _$ChangeGenderImpl;

  Gender get gender;
  @JsonKey(ignore: true)
  _$$ChangeGenderImplCopyWith<_$ChangeGenderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFirstNameImplCopyWith<$Res> {
  factory _$$ChangeFirstNameImplCopyWith(_$ChangeFirstNameImpl value,
          $Res Function(_$ChangeFirstNameImpl) then) =
      __$$ChangeFirstNameImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String firstName});
}

/// @nodoc
class __$$ChangeFirstNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeFirstNameImpl>
    implements _$$ChangeFirstNameImplCopyWith<$Res> {
  __$$ChangeFirstNameImplCopyWithImpl(
      _$ChangeFirstNameImpl _value, $Res Function(_$ChangeFirstNameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
  }) {
    return _then(_$ChangeFirstNameImpl(
      null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeFirstNameImpl implements _ChangeFirstName {
  const _$ChangeFirstNameImpl(this.firstName);

  @override
  final String firstName;

  @override
  String toString() {
    return 'RegistrationEvent.changeFirstName(firstName: $firstName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFirstNameImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firstName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFirstNameImplCopyWith<_$ChangeFirstNameImpl> get copyWith =>
      __$$ChangeFirstNameImplCopyWithImpl<_$ChangeFirstNameImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeFirstName(firstName);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeFirstName?.call(firstName);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeFirstName != null) {
      return changeFirstName(firstName);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeFirstName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeFirstName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeFirstName != null) {
      return changeFirstName(this);
    }
    return orElse();
  }
}

abstract class _ChangeFirstName implements RegistrationEvent {
  const factory _ChangeFirstName(final String firstName) =
      _$ChangeFirstNameImpl;

  String get firstName;
  @JsonKey(ignore: true)
  _$$ChangeFirstNameImplCopyWith<_$ChangeFirstNameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeLastNameImplCopyWith<$Res> {
  factory _$$ChangeLastNameImplCopyWith(_$ChangeLastNameImpl value,
          $Res Function(_$ChangeLastNameImpl) then) =
      __$$ChangeLastNameImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String lastName});
}

/// @nodoc
class __$$ChangeLastNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeLastNameImpl>
    implements _$$ChangeLastNameImplCopyWith<$Res> {
  __$$ChangeLastNameImplCopyWithImpl(
      _$ChangeLastNameImpl _value, $Res Function(_$ChangeLastNameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = null,
  }) {
    return _then(_$ChangeLastNameImpl(
      null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeLastNameImpl implements _ChangeLastName {
  const _$ChangeLastNameImpl(this.lastName);

  @override
  final String lastName;

  @override
  String toString() {
    return 'RegistrationEvent.changeLastName(lastName: $lastName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeLastNameImpl &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, lastName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeLastNameImplCopyWith<_$ChangeLastNameImpl> get copyWith =>
      __$$ChangeLastNameImplCopyWithImpl<_$ChangeLastNameImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeLastName(lastName);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeLastName?.call(lastName);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeLastName != null) {
      return changeLastName(lastName);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeLastName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeLastName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeLastName != null) {
      return changeLastName(this);
    }
    return orElse();
  }
}

abstract class _ChangeLastName implements RegistrationEvent {
  const factory _ChangeLastName(final String lastName) = _$ChangeLastNameImpl;

  String get lastName;
  @JsonKey(ignore: true)
  _$$ChangeLastNameImplCopyWith<_$ChangeLastNameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeUserNameImplCopyWith<$Res> {
  factory _$$ChangeUserNameImplCopyWith(_$ChangeUserNameImpl value,
          $Res Function(_$ChangeUserNameImpl) then) =
      __$$ChangeUserNameImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String userName});
}

/// @nodoc
class __$$ChangeUserNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeUserNameImpl>
    implements _$$ChangeUserNameImplCopyWith<$Res> {
  __$$ChangeUserNameImplCopyWithImpl(
      _$ChangeUserNameImpl _value, $Res Function(_$ChangeUserNameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userName = null,
  }) {
    return _then(_$ChangeUserNameImpl(
      null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeUserNameImpl implements _ChangeUserName {
  const _$ChangeUserNameImpl(this.userName);

  @override
  final String userName;

  @override
  String toString() {
    return 'RegistrationEvent.changeUserName(userName: $userName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeUserNameImpl &&
            (identical(other.userName, userName) ||
                other.userName == userName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeUserNameImplCopyWith<_$ChangeUserNameImpl> get copyWith =>
      __$$ChangeUserNameImplCopyWithImpl<_$ChangeUserNameImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeUserName(userName);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeUserName?.call(userName);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeUserName != null) {
      return changeUserName(userName);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeUserName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeUserName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeUserName != null) {
      return changeUserName(this);
    }
    return orElse();
  }
}

abstract class _ChangeUserName implements RegistrationEvent {
  const factory _ChangeUserName(final String userName) = _$ChangeUserNameImpl;

  String get userName;
  @JsonKey(ignore: true)
  _$$ChangeUserNameImplCopyWith<_$ChangeUserNameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeDateOfBirthImplCopyWith<$Res> {
  factory _$$ChangeDateOfBirthImplCopyWith(_$ChangeDateOfBirthImpl value,
          $Res Function(_$ChangeDateOfBirthImpl) then) =
      __$$ChangeDateOfBirthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String date});
}

/// @nodoc
class __$$ChangeDateOfBirthImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeDateOfBirthImpl>
    implements _$$ChangeDateOfBirthImplCopyWith<$Res> {
  __$$ChangeDateOfBirthImplCopyWithImpl(_$ChangeDateOfBirthImpl _value,
      $Res Function(_$ChangeDateOfBirthImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$ChangeDateOfBirthImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeDateOfBirthImpl implements _ChangeDateOfBirth {
  const _$ChangeDateOfBirthImpl(this.date);

  @override
  final String date;

  @override
  String toString() {
    return 'RegistrationEvent.changeDateOfBirth(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeDateOfBirthImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeDateOfBirthImplCopyWith<_$ChangeDateOfBirthImpl> get copyWith =>
      __$$ChangeDateOfBirthImplCopyWithImpl<_$ChangeDateOfBirthImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeDateOfBirth(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeDateOfBirth?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeDateOfBirth != null) {
      return changeDateOfBirth(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeDateOfBirth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeDateOfBirth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeDateOfBirth != null) {
      return changeDateOfBirth(this);
    }
    return orElse();
  }
}

abstract class _ChangeDateOfBirth implements RegistrationEvent {
  const factory _ChangeDateOfBirth(final String date) = _$ChangeDateOfBirthImpl;

  String get date;
  @JsonKey(ignore: true)
  _$$ChangeDateOfBirthImplCopyWith<_$ChangeDateOfBirthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeEmailImplCopyWith<$Res> {
  factory _$$ChangeEmailImplCopyWith(
          _$ChangeEmailImpl value, $Res Function(_$ChangeEmailImpl) then) =
      __$$ChangeEmailImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String email});
}

/// @nodoc
class __$$ChangeEmailImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeEmailImpl>
    implements _$$ChangeEmailImplCopyWith<$Res> {
  __$$ChangeEmailImplCopyWithImpl(
      _$ChangeEmailImpl _value, $Res Function(_$ChangeEmailImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
  }) {
    return _then(_$ChangeEmailImpl(
      null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeEmailImpl implements _ChangeEmail {
  const _$ChangeEmailImpl(this.email);

  @override
  final String email;

  @override
  String toString() {
    return 'RegistrationEvent.changeEmail(email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeEmailImpl &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeEmailImplCopyWith<_$ChangeEmailImpl> get copyWith =>
      __$$ChangeEmailImplCopyWithImpl<_$ChangeEmailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeEmail(email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeEmail?.call(email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeEmail != null) {
      return changeEmail(email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeEmail != null) {
      return changeEmail(this);
    }
    return orElse();
  }
}

abstract class _ChangeEmail implements RegistrationEvent {
  const factory _ChangeEmail(final String email) = _$ChangeEmailImpl;

  String get email;
  @JsonKey(ignore: true)
  _$$ChangeEmailImplCopyWith<_$ChangeEmailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangePhoneNumberImplCopyWith<$Res> {
  factory _$$ChangePhoneNumberImplCopyWith(_$ChangePhoneNumberImpl value,
          $Res Function(_$ChangePhoneNumberImpl) then) =
      __$$ChangePhoneNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({({String code, String number}) phoneNumber});
}

/// @nodoc
class __$$ChangePhoneNumberImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangePhoneNumberImpl>
    implements _$$ChangePhoneNumberImplCopyWith<$Res> {
  __$$ChangePhoneNumberImplCopyWithImpl(_$ChangePhoneNumberImpl _value,
      $Res Function(_$ChangePhoneNumberImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$ChangePhoneNumberImpl(
      null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as ({String code, String number}),
    ));
  }
}

/// @nodoc

class _$ChangePhoneNumberImpl implements _ChangePhoneNumber {
  const _$ChangePhoneNumberImpl(this.phoneNumber);

  @override
  final ({String code, String number}) phoneNumber;

  @override
  String toString() {
    return 'RegistrationEvent.changePhoneNumber(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePhoneNumberImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePhoneNumberImplCopyWith<_$ChangePhoneNumberImpl> get copyWith =>
      __$$ChangePhoneNumberImplCopyWithImpl<_$ChangePhoneNumberImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changePhoneNumber(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changePhoneNumber?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changePhoneNumber != null) {
      return changePhoneNumber(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changePhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changePhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changePhoneNumber != null) {
      return changePhoneNumber(this);
    }
    return orElse();
  }
}

abstract class _ChangePhoneNumber implements RegistrationEvent {
  const factory _ChangePhoneNumber(
          final ({String code, String number}) phoneNumber) =
      _$ChangePhoneNumberImpl;

  ({String code, String number}) get phoneNumber;
  @JsonKey(ignore: true)
  _$$ChangePhoneNumberImplCopyWith<_$ChangePhoneNumberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangePasswordImplCopyWith<$Res> {
  factory _$$ChangePasswordImplCopyWith(_$ChangePasswordImpl value,
          $Res Function(_$ChangePasswordImpl) then) =
      __$$ChangePasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String password});
}

/// @nodoc
class __$$ChangePasswordImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangePasswordImpl>
    implements _$$ChangePasswordImplCopyWith<$Res> {
  __$$ChangePasswordImplCopyWithImpl(
      _$ChangePasswordImpl _value, $Res Function(_$ChangePasswordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? password = null,
  }) {
    return _then(_$ChangePasswordImpl(
      null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangePasswordImpl implements _ChangePassword {
  const _$ChangePasswordImpl(this.password);

  @override
  final String password;

  @override
  String toString() {
    return 'RegistrationEvent.changePassword(password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePasswordImpl &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, password);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePasswordImplCopyWith<_$ChangePasswordImpl> get copyWith =>
      __$$ChangePasswordImplCopyWithImpl<_$ChangePasswordImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changePassword(password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changePassword?.call(password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changePassword != null) {
      return changePassword(password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changePassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changePassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changePassword != null) {
      return changePassword(this);
    }
    return orElse();
  }
}

abstract class _ChangePassword implements RegistrationEvent {
  const factory _ChangePassword(final String password) = _$ChangePasswordImpl;

  String get password;
  @JsonKey(ignore: true)
  _$$ChangePasswordImplCopyWith<_$ChangePasswordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeConfirmPasswordImplCopyWith<$Res> {
  factory _$$ChangeConfirmPasswordImplCopyWith(
          _$ChangeConfirmPasswordImpl value,
          $Res Function(_$ChangeConfirmPasswordImpl) then) =
      __$$ChangeConfirmPasswordImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String confirmPassword});
}

/// @nodoc
class __$$ChangeConfirmPasswordImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeConfirmPasswordImpl>
    implements _$$ChangeConfirmPasswordImplCopyWith<$Res> {
  __$$ChangeConfirmPasswordImplCopyWithImpl(_$ChangeConfirmPasswordImpl _value,
      $Res Function(_$ChangeConfirmPasswordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? confirmPassword = null,
  }) {
    return _then(_$ChangeConfirmPasswordImpl(
      null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeConfirmPasswordImpl implements _ChangeConfirmPassword {
  const _$ChangeConfirmPasswordImpl(this.confirmPassword);

  @override
  final String confirmPassword;

  @override
  String toString() {
    return 'RegistrationEvent.changeConfirmPassword(confirmPassword: $confirmPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeConfirmPasswordImpl &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword));
  }

  @override
  int get hashCode => Object.hash(runtimeType, confirmPassword);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeConfirmPasswordImplCopyWith<_$ChangeConfirmPasswordImpl>
      get copyWith => __$$ChangeConfirmPasswordImplCopyWithImpl<
          _$ChangeConfirmPasswordImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeConfirmPassword(confirmPassword);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeConfirmPassword?.call(confirmPassword);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeConfirmPassword != null) {
      return changeConfirmPassword(confirmPassword);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeConfirmPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeConfirmPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeConfirmPassword != null) {
      return changeConfirmPassword(this);
    }
    return orElse();
  }
}

abstract class _ChangeConfirmPassword implements RegistrationEvent {
  const factory _ChangeConfirmPassword(final String confirmPassword) =
      _$ChangeConfirmPasswordImpl;

  String get confirmPassword;
  @JsonKey(ignore: true)
  _$$ChangeConfirmPasswordImplCopyWith<_$ChangeConfirmPasswordImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeObscureTextImplCopyWith<$Res> {
  factory _$$ChangeObscureTextImplCopyWith(_$ChangeObscureTextImpl value,
          $Res Function(_$ChangeObscureTextImpl) then) =
      __$$ChangeObscureTextImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeObscureTextImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ChangeObscureTextImpl>
    implements _$$ChangeObscureTextImplCopyWith<$Res> {
  __$$ChangeObscureTextImplCopyWithImpl(_$ChangeObscureTextImpl _value,
      $Res Function(_$ChangeObscureTextImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ChangeObscureTextImpl implements _ChangeObscureText {
  const _$ChangeObscureTextImpl();

  @override
  String toString() {
    return 'RegistrationEvent.changeObscureText()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ChangeObscureTextImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeObscureText();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeObscureText?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeObscureText != null) {
      return changeObscureText();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeObscureText(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeObscureText?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeObscureText != null) {
      return changeObscureText(this);
    }
    return orElse();
  }
}

abstract class _ChangeObscureText implements RegistrationEvent {
  const factory _ChangeObscureText() = _$ChangeObscureTextImpl;
}

/// @nodoc
abstract class _$$ChangeConfirmObscureTextImplCopyWith<$Res> {
  factory _$$ChangeConfirmObscureTextImplCopyWith(
          _$ChangeConfirmObscureTextImpl value,
          $Res Function(_$ChangeConfirmObscureTextImpl) then) =
      __$$ChangeConfirmObscureTextImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ChangeConfirmObscureTextImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res,
        _$ChangeConfirmObscureTextImpl>
    implements _$$ChangeConfirmObscureTextImplCopyWith<$Res> {
  __$$ChangeConfirmObscureTextImplCopyWithImpl(
      _$ChangeConfirmObscureTextImpl _value,
      $Res Function(_$ChangeConfirmObscureTextImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ChangeConfirmObscureTextImpl implements _ChangeConfirmObscureText {
  const _$ChangeConfirmObscureTextImpl();

  @override
  String toString() {
    return 'RegistrationEvent.changeConfirmObscureText()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeConfirmObscureTextImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return changeConfirmObscureText();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return changeConfirmObscureText?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeConfirmObscureText != null) {
      return changeConfirmObscureText();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return changeConfirmObscureText(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return changeConfirmObscureText?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (changeConfirmObscureText != null) {
      return changeConfirmObscureText(this);
    }
    return orElse();
  }
}

abstract class _ChangeConfirmObscureText implements RegistrationEvent {
  const factory _ChangeConfirmObscureText() = _$ChangeConfirmObscureTextImpl;
}

/// @nodoc
abstract class _$$ValidateEmailImplCopyWith<$Res> {
  factory _$$ValidateEmailImplCopyWith(
          _$ValidateEmailImpl value, $Res Function(_$ValidateEmailImpl) then) =
      __$$ValidateEmailImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateEmailImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateEmailImpl>
    implements _$$ValidateEmailImplCopyWith<$Res> {
  __$$ValidateEmailImplCopyWithImpl(
      _$ValidateEmailImpl _value, $Res Function(_$ValidateEmailImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateEmailImpl implements _ValidateEmail {
  const _$ValidateEmailImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateEmail()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateEmailImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateEmail();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateEmail?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateEmail != null) {
      return validateEmail();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateEmail != null) {
      return validateEmail(this);
    }
    return orElse();
  }
}

abstract class _ValidateEmail implements RegistrationEvent {
  const factory _ValidateEmail() = _$ValidateEmailImpl;
}

/// @nodoc
abstract class _$$ValidatePasswordImplCopyWith<$Res> {
  factory _$$ValidatePasswordImplCopyWith(_$ValidatePasswordImpl value,
          $Res Function(_$ValidatePasswordImpl) then) =
      __$$ValidatePasswordImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidatePasswordImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidatePasswordImpl>
    implements _$$ValidatePasswordImplCopyWith<$Res> {
  __$$ValidatePasswordImplCopyWithImpl(_$ValidatePasswordImpl _value,
      $Res Function(_$ValidatePasswordImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidatePasswordImpl implements _ValidatePassword {
  const _$ValidatePasswordImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validatePassword()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidatePasswordImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validatePassword();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validatePassword?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validatePassword != null) {
      return validatePassword();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validatePassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validatePassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validatePassword != null) {
      return validatePassword(this);
    }
    return orElse();
  }
}

abstract class _ValidatePassword implements RegistrationEvent {
  const factory _ValidatePassword() = _$ValidatePasswordImpl;
}

/// @nodoc
abstract class _$$ValidateConfirmPasswordImplCopyWith<$Res> {
  factory _$$ValidateConfirmPasswordImplCopyWith(
          _$ValidateConfirmPasswordImpl value,
          $Res Function(_$ValidateConfirmPasswordImpl) then) =
      __$$ValidateConfirmPasswordImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateConfirmPasswordImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateConfirmPasswordImpl>
    implements _$$ValidateConfirmPasswordImplCopyWith<$Res> {
  __$$ValidateConfirmPasswordImplCopyWithImpl(
      _$ValidateConfirmPasswordImpl _value,
      $Res Function(_$ValidateConfirmPasswordImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateConfirmPasswordImpl implements _ValidateConfirmPassword {
  const _$ValidateConfirmPasswordImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateConfirmPassword()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidateConfirmPasswordImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateConfirmPassword();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateConfirmPassword?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateConfirmPassword != null) {
      return validateConfirmPassword();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateConfirmPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateConfirmPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateConfirmPassword != null) {
      return validateConfirmPassword(this);
    }
    return orElse();
  }
}

abstract class _ValidateConfirmPassword implements RegistrationEvent {
  const factory _ValidateConfirmPassword() = _$ValidateConfirmPasswordImpl;
}

/// @nodoc
abstract class _$$ValidateFirstNameImplCopyWith<$Res> {
  factory _$$ValidateFirstNameImplCopyWith(_$ValidateFirstNameImpl value,
          $Res Function(_$ValidateFirstNameImpl) then) =
      __$$ValidateFirstNameImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateFirstNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateFirstNameImpl>
    implements _$$ValidateFirstNameImplCopyWith<$Res> {
  __$$ValidateFirstNameImplCopyWithImpl(_$ValidateFirstNameImpl _value,
      $Res Function(_$ValidateFirstNameImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateFirstNameImpl implements _ValidateFirstName {
  const _$ValidateFirstNameImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateFirstName()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateFirstNameImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateFirstName();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateFirstName?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateFirstName != null) {
      return validateFirstName();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateFirstName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateFirstName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateFirstName != null) {
      return validateFirstName(this);
    }
    return orElse();
  }
}

abstract class _ValidateFirstName implements RegistrationEvent {
  const factory _ValidateFirstName() = _$ValidateFirstNameImpl;
}

/// @nodoc
abstract class _$$ValidateLastNameImplCopyWith<$Res> {
  factory _$$ValidateLastNameImplCopyWith(_$ValidateLastNameImpl value,
          $Res Function(_$ValidateLastNameImpl) then) =
      __$$ValidateLastNameImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateLastNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateLastNameImpl>
    implements _$$ValidateLastNameImplCopyWith<$Res> {
  __$$ValidateLastNameImplCopyWithImpl(_$ValidateLastNameImpl _value,
      $Res Function(_$ValidateLastNameImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateLastNameImpl implements _ValidateLastName {
  const _$ValidateLastNameImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateLastName()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateLastNameImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateLastName();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateLastName?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateLastName != null) {
      return validateLastName();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateLastName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateLastName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateLastName != null) {
      return validateLastName(this);
    }
    return orElse();
  }
}

abstract class _ValidateLastName implements RegistrationEvent {
  const factory _ValidateLastName() = _$ValidateLastNameImpl;
}

/// @nodoc
abstract class _$$ValidateUserNameImplCopyWith<$Res> {
  factory _$$ValidateUserNameImplCopyWith(_$ValidateUserNameImpl value,
          $Res Function(_$ValidateUserNameImpl) then) =
      __$$ValidateUserNameImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateUserNameImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateUserNameImpl>
    implements _$$ValidateUserNameImplCopyWith<$Res> {
  __$$ValidateUserNameImplCopyWithImpl(_$ValidateUserNameImpl _value,
      $Res Function(_$ValidateUserNameImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateUserNameImpl implements _ValidateUserName {
  const _$ValidateUserNameImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateUserName()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateUserNameImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateUserName();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateUserName?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateUserName != null) {
      return validateUserName();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateUserName(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateUserName?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateUserName != null) {
      return validateUserName(this);
    }
    return orElse();
  }
}

abstract class _ValidateUserName implements RegistrationEvent {
  const factory _ValidateUserName() = _$ValidateUserNameImpl;
}

/// @nodoc
abstract class _$$ValidateBirthDateImplCopyWith<$Res> {
  factory _$$ValidateBirthDateImplCopyWith(_$ValidateBirthDateImpl value,
          $Res Function(_$ValidateBirthDateImpl) then) =
      __$$ValidateBirthDateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidateBirthDateImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidateBirthDateImpl>
    implements _$$ValidateBirthDateImplCopyWith<$Res> {
  __$$ValidateBirthDateImplCopyWithImpl(_$ValidateBirthDateImpl _value,
      $Res Function(_$ValidateBirthDateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidateBirthDateImpl implements _ValidateBirthDate {
  const _$ValidateBirthDateImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validateBirthDate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ValidateBirthDateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validateBirthDate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validateBirthDate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateBirthDate != null) {
      return validateBirthDate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validateBirthDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validateBirthDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validateBirthDate != null) {
      return validateBirthDate(this);
    }
    return orElse();
  }
}

abstract class _ValidateBirthDate implements RegistrationEvent {
  const factory _ValidateBirthDate() = _$ValidateBirthDateImpl;
}

/// @nodoc
abstract class _$$ValidatePhoneNumberImplCopyWith<$Res> {
  factory _$$ValidatePhoneNumberImplCopyWith(_$ValidatePhoneNumberImpl value,
          $Res Function(_$ValidatePhoneNumberImpl) then) =
      __$$ValidatePhoneNumberImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ValidatePhoneNumberImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ValidatePhoneNumberImpl>
    implements _$$ValidatePhoneNumberImplCopyWith<$Res> {
  __$$ValidatePhoneNumberImplCopyWithImpl(_$ValidatePhoneNumberImpl _value,
      $Res Function(_$ValidatePhoneNumberImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ValidatePhoneNumberImpl implements _ValidatePhoneNumber {
  const _$ValidatePhoneNumberImpl();

  @override
  String toString() {
    return 'RegistrationEvent.validatePhoneNumber()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidatePhoneNumberImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return validatePhoneNumber();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return validatePhoneNumber?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validatePhoneNumber != null) {
      return validatePhoneNumber();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return validatePhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return validatePhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (validatePhoneNumber != null) {
      return validatePhoneNumber(this);
    }
    return orElse();
  }
}

abstract class _ValidatePhoneNumber implements RegistrationEvent {
  const factory _ValidatePhoneNumber() = _$ValidatePhoneNumberImpl;
}

/// @nodoc
abstract class _$$AcceptPrivacyPolicyImplCopyWith<$Res> {
  factory _$$AcceptPrivacyPolicyImplCopyWith(_$AcceptPrivacyPolicyImpl value,
          $Res Function(_$AcceptPrivacyPolicyImpl) then) =
      __$$AcceptPrivacyPolicyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$AcceptPrivacyPolicyImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$AcceptPrivacyPolicyImpl>
    implements _$$AcceptPrivacyPolicyImplCopyWith<$Res> {
  __$$AcceptPrivacyPolicyImplCopyWithImpl(_$AcceptPrivacyPolicyImpl _value,
      $Res Function(_$AcceptPrivacyPolicyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$AcceptPrivacyPolicyImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AcceptPrivacyPolicyImpl implements _AcceptPrivacyPolicy {
  const _$AcceptPrivacyPolicyImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'RegistrationEvent.acceptPrivacyPolicy(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AcceptPrivacyPolicyImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AcceptPrivacyPolicyImplCopyWith<_$AcceptPrivacyPolicyImpl> get copyWith =>
      __$$AcceptPrivacyPolicyImplCopyWithImpl<_$AcceptPrivacyPolicyImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return acceptPrivacyPolicy(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return acceptPrivacyPolicy?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (acceptPrivacyPolicy != null) {
      return acceptPrivacyPolicy(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return acceptPrivacyPolicy(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return acceptPrivacyPolicy?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (acceptPrivacyPolicy != null) {
      return acceptPrivacyPolicy(this);
    }
    return orElse();
  }
}

abstract class _AcceptPrivacyPolicy implements RegistrationEvent {
  const factory _AcceptPrivacyPolicy(final bool value) =
      _$AcceptPrivacyPolicyImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$AcceptPrivacyPolicyImplCopyWith<_$AcceptPrivacyPolicyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmitImplCopyWith<$Res> {
  factory _$$SubmitImplCopyWith(
          _$SubmitImpl value, $Res Function(_$SubmitImpl) then) =
      __$$SubmitImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmitImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$SubmitImpl>
    implements _$$SubmitImplCopyWith<$Res> {
  __$$SubmitImplCopyWithImpl(
      _$SubmitImpl _value, $Res Function(_$SubmitImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SubmitImpl implements _Submit {
  const _$SubmitImpl();

  @override
  String toString() {
    return 'RegistrationEvent.submit()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmitImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return submit();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return submit?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (submit != null) {
      return submit();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return submit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return submit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (submit != null) {
      return submit(this);
    }
    return orElse();
  }
}

abstract class _Submit implements RegistrationEvent {
  const factory _Submit() = _$SubmitImpl;
}

/// @nodoc
abstract class _$$RegisterWithSocialImplCopyWith<$Res> {
  factory _$$RegisterWithSocialImplCopyWith(_$RegisterWithSocialImpl value,
          $Res Function(_$RegisterWithSocialImpl) then) =
      __$$RegisterWithSocialImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SocialAuthType type});
}

/// @nodoc
class __$$RegisterWithSocialImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$RegisterWithSocialImpl>
    implements _$$RegisterWithSocialImplCopyWith<$Res> {
  __$$RegisterWithSocialImplCopyWithImpl(_$RegisterWithSocialImpl _value,
      $Res Function(_$RegisterWithSocialImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$RegisterWithSocialImpl(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as SocialAuthType,
    ));
  }
}

/// @nodoc

class _$RegisterWithSocialImpl implements _RegisterWithSocial {
  const _$RegisterWithSocialImpl(this.type);

  @override
  final SocialAuthType type;

  @override
  String toString() {
    return 'RegistrationEvent.registerWithSocial(type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterWithSocialImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterWithSocialImplCopyWith<_$RegisterWithSocialImpl> get copyWith =>
      __$$RegisterWithSocialImplCopyWithImpl<_$RegisterWithSocialImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Gender gender) changeGender,
    required TResult Function(String firstName) changeFirstName,
    required TResult Function(String lastName) changeLastName,
    required TResult Function(String userName) changeUserName,
    required TResult Function(String date) changeDateOfBirth,
    required TResult Function(String email) changeEmail,
    required TResult Function(({String code, String number}) phoneNumber)
        changePhoneNumber,
    required TResult Function(String password) changePassword,
    required TResult Function(String confirmPassword) changeConfirmPassword,
    required TResult Function() changeObscureText,
    required TResult Function() changeConfirmObscureText,
    required TResult Function() validateEmail,
    required TResult Function() validatePassword,
    required TResult Function() validateConfirmPassword,
    required TResult Function() validateFirstName,
    required TResult Function() validateLastName,
    required TResult Function() validateUserName,
    required TResult Function() validateBirthDate,
    required TResult Function() validatePhoneNumber,
    required TResult Function(bool value) acceptPrivacyPolicy,
    required TResult Function() submit,
    required TResult Function(SocialAuthType type) registerWithSocial,
  }) {
    return registerWithSocial(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Gender gender)? changeGender,
    TResult? Function(String firstName)? changeFirstName,
    TResult? Function(String lastName)? changeLastName,
    TResult? Function(String userName)? changeUserName,
    TResult? Function(String date)? changeDateOfBirth,
    TResult? Function(String email)? changeEmail,
    TResult? Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult? Function(String password)? changePassword,
    TResult? Function(String confirmPassword)? changeConfirmPassword,
    TResult? Function()? changeObscureText,
    TResult? Function()? changeConfirmObscureText,
    TResult? Function()? validateEmail,
    TResult? Function()? validatePassword,
    TResult? Function()? validateConfirmPassword,
    TResult? Function()? validateFirstName,
    TResult? Function()? validateLastName,
    TResult? Function()? validateUserName,
    TResult? Function()? validateBirthDate,
    TResult? Function()? validatePhoneNumber,
    TResult? Function(bool value)? acceptPrivacyPolicy,
    TResult? Function()? submit,
    TResult? Function(SocialAuthType type)? registerWithSocial,
  }) {
    return registerWithSocial?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Gender gender)? changeGender,
    TResult Function(String firstName)? changeFirstName,
    TResult Function(String lastName)? changeLastName,
    TResult Function(String userName)? changeUserName,
    TResult Function(String date)? changeDateOfBirth,
    TResult Function(String email)? changeEmail,
    TResult Function(({String code, String number}) phoneNumber)?
        changePhoneNumber,
    TResult Function(String password)? changePassword,
    TResult Function(String confirmPassword)? changeConfirmPassword,
    TResult Function()? changeObscureText,
    TResult Function()? changeConfirmObscureText,
    TResult Function()? validateEmail,
    TResult Function()? validatePassword,
    TResult Function()? validateConfirmPassword,
    TResult Function()? validateFirstName,
    TResult Function()? validateLastName,
    TResult Function()? validateUserName,
    TResult Function()? validateBirthDate,
    TResult Function()? validatePhoneNumber,
    TResult Function(bool value)? acceptPrivacyPolicy,
    TResult Function()? submit,
    TResult Function(SocialAuthType type)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (registerWithSocial != null) {
      return registerWithSocial(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangeGender value) changeGender,
    required TResult Function(_ChangeFirstName value) changeFirstName,
    required TResult Function(_ChangeLastName value) changeLastName,
    required TResult Function(_ChangeUserName value) changeUserName,
    required TResult Function(_ChangeDateOfBirth value) changeDateOfBirth,
    required TResult Function(_ChangeEmail value) changeEmail,
    required TResult Function(_ChangePhoneNumber value) changePhoneNumber,
    required TResult Function(_ChangePassword value) changePassword,
    required TResult Function(_ChangeConfirmPassword value)
        changeConfirmPassword,
    required TResult Function(_ChangeObscureText value) changeObscureText,
    required TResult Function(_ChangeConfirmObscureText value)
        changeConfirmObscureText,
    required TResult Function(_ValidateEmail value) validateEmail,
    required TResult Function(_ValidatePassword value) validatePassword,
    required TResult Function(_ValidateConfirmPassword value)
        validateConfirmPassword,
    required TResult Function(_ValidateFirstName value) validateFirstName,
    required TResult Function(_ValidateLastName value) validateLastName,
    required TResult Function(_ValidateUserName value) validateUserName,
    required TResult Function(_ValidateBirthDate value) validateBirthDate,
    required TResult Function(_ValidatePhoneNumber value) validatePhoneNumber,
    required TResult Function(_AcceptPrivacyPolicy value) acceptPrivacyPolicy,
    required TResult Function(_Submit value) submit,
    required TResult Function(_RegisterWithSocial value) registerWithSocial,
  }) {
    return registerWithSocial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangeGender value)? changeGender,
    TResult? Function(_ChangeFirstName value)? changeFirstName,
    TResult? Function(_ChangeLastName value)? changeLastName,
    TResult? Function(_ChangeUserName value)? changeUserName,
    TResult? Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult? Function(_ChangeEmail value)? changeEmail,
    TResult? Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult? Function(_ChangePassword value)? changePassword,
    TResult? Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult? Function(_ChangeObscureText value)? changeObscureText,
    TResult? Function(_ChangeConfirmObscureText value)?
        changeConfirmObscureText,
    TResult? Function(_ValidateEmail value)? validateEmail,
    TResult? Function(_ValidatePassword value)? validatePassword,
    TResult? Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult? Function(_ValidateFirstName value)? validateFirstName,
    TResult? Function(_ValidateLastName value)? validateLastName,
    TResult? Function(_ValidateUserName value)? validateUserName,
    TResult? Function(_ValidateBirthDate value)? validateBirthDate,
    TResult? Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult? Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult? Function(_Submit value)? submit,
    TResult? Function(_RegisterWithSocial value)? registerWithSocial,
  }) {
    return registerWithSocial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangeGender value)? changeGender,
    TResult Function(_ChangeFirstName value)? changeFirstName,
    TResult Function(_ChangeLastName value)? changeLastName,
    TResult Function(_ChangeUserName value)? changeUserName,
    TResult Function(_ChangeDateOfBirth value)? changeDateOfBirth,
    TResult Function(_ChangeEmail value)? changeEmail,
    TResult Function(_ChangePhoneNumber value)? changePhoneNumber,
    TResult Function(_ChangePassword value)? changePassword,
    TResult Function(_ChangeConfirmPassword value)? changeConfirmPassword,
    TResult Function(_ChangeObscureText value)? changeObscureText,
    TResult Function(_ChangeConfirmObscureText value)? changeConfirmObscureText,
    TResult Function(_ValidateEmail value)? validateEmail,
    TResult Function(_ValidatePassword value)? validatePassword,
    TResult Function(_ValidateConfirmPassword value)? validateConfirmPassword,
    TResult Function(_ValidateFirstName value)? validateFirstName,
    TResult Function(_ValidateLastName value)? validateLastName,
    TResult Function(_ValidateUserName value)? validateUserName,
    TResult Function(_ValidateBirthDate value)? validateBirthDate,
    TResult Function(_ValidatePhoneNumber value)? validatePhoneNumber,
    TResult Function(_AcceptPrivacyPolicy value)? acceptPrivacyPolicy,
    TResult Function(_Submit value)? submit,
    TResult Function(_RegisterWithSocial value)? registerWithSocial,
    required TResult orElse(),
  }) {
    if (registerWithSocial != null) {
      return registerWithSocial(this);
    }
    return orElse();
  }
}

abstract class _RegisterWithSocial implements RegistrationEvent {
  const factory _RegisterWithSocial(final SocialAuthType type) =
      _$RegisterWithSocialImpl;

  SocialAuthType get type;
  @JsonKey(ignore: true)
  _$$RegisterWithSocialImplCopyWith<_$RegisterWithSocialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RegistrationState {
  RegistrationStatus get status => throw _privateConstructorUsedError;
  RegistrationWithSocialStatus get socialStatus =>
      throw _privateConstructorUsedError;
  Gender? get gender => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  ValidationStatus get firstNameValidationStatus =>
      throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  ValidationStatus get lastNameValidationStatus =>
      throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  ValidationStatus get userNameValidationStatus =>
      throw _privateConstructorUsedError;
  String get birthDate => throw _privateConstructorUsedError;
  ({String code, String number}) get phoneNumber =>
      throw _privateConstructorUsedError;
  ValidationStatus get phoneNumberValidationStatus =>
      throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  ValidationStatus get emailValidationStatus =>
      throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  ValidationStatus get passwordValidationStatus =>
      throw _privateConstructorUsedError;
  String get confirmPassword => throw _privateConstructorUsedError;
  ValidationStatus get confirmPasswordValidationStatus =>
      throw _privateConstructorUsedError;
  bool get isObscured => throw _privateConstructorUsedError;
  bool get isConfirmObscured => throw _privateConstructorUsedError;
  bool get isPrivacyAccepted => throw _privateConstructorUsedError;
  SocialAuthOnBoardingRequest? get socialAuthOnBoardingRequest =>
      throw _privateConstructorUsedError;
  bool get isRegistered => throw _privateConstructorUsedError;
  String? get firstNameError => throw _privateConstructorUsedError;
  String? get lastNameError => throw _privateConstructorUsedError;
  String? get userNameError => throw _privateConstructorUsedError;
  String? get birthDateError => throw _privateConstructorUsedError;
  String? get phoneNumberError => throw _privateConstructorUsedError;
  String? get emailError => throw _privateConstructorUsedError;
  String? get passwordError => throw _privateConstructorUsedError;
  String? get confirmPasswordError => throw _privateConstructorUsedError;
  String? get privacyError => throw _privateConstructorUsedError;
  String? get registrationError => throw _privateConstructorUsedError;
  String? get socialRegistrationError => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RegistrationStateCopyWith<RegistrationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegistrationStateCopyWith<$Res> {
  factory $RegistrationStateCopyWith(
          RegistrationState value, $Res Function(RegistrationState) then) =
      _$RegistrationStateCopyWithImpl<$Res, RegistrationState>;
  @useResult
  $Res call(
      {RegistrationStatus status,
      RegistrationWithSocialStatus socialStatus,
      Gender? gender,
      String firstName,
      ValidationStatus firstNameValidationStatus,
      String lastName,
      ValidationStatus lastNameValidationStatus,
      String userName,
      ValidationStatus userNameValidationStatus,
      String birthDate,
      ({String code, String number}) phoneNumber,
      ValidationStatus phoneNumberValidationStatus,
      String email,
      ValidationStatus emailValidationStatus,
      String password,
      ValidationStatus passwordValidationStatus,
      String confirmPassword,
      ValidationStatus confirmPasswordValidationStatus,
      bool isObscured,
      bool isConfirmObscured,
      bool isPrivacyAccepted,
      SocialAuthOnBoardingRequest? socialAuthOnBoardingRequest,
      bool isRegistered,
      String? firstNameError,
      String? lastNameError,
      String? userNameError,
      String? birthDateError,
      String? phoneNumberError,
      String? emailError,
      String? passwordError,
      String? confirmPasswordError,
      String? privacyError,
      String? registrationError,
      String? socialRegistrationError});
}

/// @nodoc
class _$RegistrationStateCopyWithImpl<$Res, $Val extends RegistrationState>
    implements $RegistrationStateCopyWith<$Res> {
  _$RegistrationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? socialStatus = null,
    Object? gender = freezed,
    Object? firstName = null,
    Object? firstNameValidationStatus = null,
    Object? lastName = null,
    Object? lastNameValidationStatus = null,
    Object? userName = null,
    Object? userNameValidationStatus = null,
    Object? birthDate = null,
    Object? phoneNumber = null,
    Object? phoneNumberValidationStatus = null,
    Object? email = null,
    Object? emailValidationStatus = null,
    Object? password = null,
    Object? passwordValidationStatus = null,
    Object? confirmPassword = null,
    Object? confirmPasswordValidationStatus = null,
    Object? isObscured = null,
    Object? isConfirmObscured = null,
    Object? isPrivacyAccepted = null,
    Object? socialAuthOnBoardingRequest = freezed,
    Object? isRegistered = null,
    Object? firstNameError = freezed,
    Object? lastNameError = freezed,
    Object? userNameError = freezed,
    Object? birthDateError = freezed,
    Object? phoneNumberError = freezed,
    Object? emailError = freezed,
    Object? passwordError = freezed,
    Object? confirmPasswordError = freezed,
    Object? privacyError = freezed,
    Object? registrationError = freezed,
    Object? socialRegistrationError = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RegistrationStatus,
      socialStatus: null == socialStatus
          ? _value.socialStatus
          : socialStatus // ignore: cast_nullable_to_non_nullable
              as RegistrationWithSocialStatus,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      firstNameValidationStatus: null == firstNameValidationStatus
          ? _value.firstNameValidationStatus
          : firstNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      lastNameValidationStatus: null == lastNameValidationStatus
          ? _value.lastNameValidationStatus
          : lastNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userNameValidationStatus: null == userNameValidationStatus
          ? _value.userNameValidationStatus
          : userNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      birthDate: null == birthDate
          ? _value.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as ({String code, String number}),
      phoneNumberValidationStatus: null == phoneNumberValidationStatus
          ? _value.phoneNumberValidationStatus
          : phoneNumberValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      emailValidationStatus: null == emailValidationStatus
          ? _value.emailValidationStatus
          : emailValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      passwordValidationStatus: null == passwordValidationStatus
          ? _value.passwordValidationStatus
          : passwordValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPasswordValidationStatus: null == confirmPasswordValidationStatus
          ? _value.confirmPasswordValidationStatus
          : confirmPasswordValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      isObscured: null == isObscured
          ? _value.isObscured
          : isObscured // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmObscured: null == isConfirmObscured
          ? _value.isConfirmObscured
          : isConfirmObscured // ignore: cast_nullable_to_non_nullable
              as bool,
      isPrivacyAccepted: null == isPrivacyAccepted
          ? _value.isPrivacyAccepted
          : isPrivacyAccepted // ignore: cast_nullable_to_non_nullable
              as bool,
      socialAuthOnBoardingRequest: freezed == socialAuthOnBoardingRequest
          ? _value.socialAuthOnBoardingRequest
          : socialAuthOnBoardingRequest // ignore: cast_nullable_to_non_nullable
              as SocialAuthOnBoardingRequest?,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      firstNameError: freezed == firstNameError
          ? _value.firstNameError
          : firstNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      lastNameError: freezed == lastNameError
          ? _value.lastNameError
          : lastNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      userNameError: freezed == userNameError
          ? _value.userNameError
          : userNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateError: freezed == birthDateError
          ? _value.birthDateError
          : birthDateError // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumberError: freezed == phoneNumberError
          ? _value.phoneNumberError
          : phoneNumberError // ignore: cast_nullable_to_non_nullable
              as String?,
      emailError: freezed == emailError
          ? _value.emailError
          : emailError // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordError: freezed == passwordError
          ? _value.passwordError
          : passwordError // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmPasswordError: freezed == confirmPasswordError
          ? _value.confirmPasswordError
          : confirmPasswordError // ignore: cast_nullable_to_non_nullable
              as String?,
      privacyError: freezed == privacyError
          ? _value.privacyError
          : privacyError // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationError: freezed == registrationError
          ? _value.registrationError
          : registrationError // ignore: cast_nullable_to_non_nullable
              as String?,
      socialRegistrationError: freezed == socialRegistrationError
          ? _value.socialRegistrationError
          : socialRegistrationError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegistrationStateImplCopyWith<$Res>
    implements $RegistrationStateCopyWith<$Res> {
  factory _$$RegistrationStateImplCopyWith(_$RegistrationStateImpl value,
          $Res Function(_$RegistrationStateImpl) then) =
      __$$RegistrationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RegistrationStatus status,
      RegistrationWithSocialStatus socialStatus,
      Gender? gender,
      String firstName,
      ValidationStatus firstNameValidationStatus,
      String lastName,
      ValidationStatus lastNameValidationStatus,
      String userName,
      ValidationStatus userNameValidationStatus,
      String birthDate,
      ({String code, String number}) phoneNumber,
      ValidationStatus phoneNumberValidationStatus,
      String email,
      ValidationStatus emailValidationStatus,
      String password,
      ValidationStatus passwordValidationStatus,
      String confirmPassword,
      ValidationStatus confirmPasswordValidationStatus,
      bool isObscured,
      bool isConfirmObscured,
      bool isPrivacyAccepted,
      SocialAuthOnBoardingRequest? socialAuthOnBoardingRequest,
      bool isRegistered,
      String? firstNameError,
      String? lastNameError,
      String? userNameError,
      String? birthDateError,
      String? phoneNumberError,
      String? emailError,
      String? passwordError,
      String? confirmPasswordError,
      String? privacyError,
      String? registrationError,
      String? socialRegistrationError});
}

/// @nodoc
class __$$RegistrationStateImplCopyWithImpl<$Res>
    extends _$RegistrationStateCopyWithImpl<$Res, _$RegistrationStateImpl>
    implements _$$RegistrationStateImplCopyWith<$Res> {
  __$$RegistrationStateImplCopyWithImpl(_$RegistrationStateImpl _value,
      $Res Function(_$RegistrationStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? socialStatus = null,
    Object? gender = freezed,
    Object? firstName = null,
    Object? firstNameValidationStatus = null,
    Object? lastName = null,
    Object? lastNameValidationStatus = null,
    Object? userName = null,
    Object? userNameValidationStatus = null,
    Object? birthDate = null,
    Object? phoneNumber = null,
    Object? phoneNumberValidationStatus = null,
    Object? email = null,
    Object? emailValidationStatus = null,
    Object? password = null,
    Object? passwordValidationStatus = null,
    Object? confirmPassword = null,
    Object? confirmPasswordValidationStatus = null,
    Object? isObscured = null,
    Object? isConfirmObscured = null,
    Object? isPrivacyAccepted = null,
    Object? socialAuthOnBoardingRequest = freezed,
    Object? isRegistered = null,
    Object? firstNameError = freezed,
    Object? lastNameError = freezed,
    Object? userNameError = freezed,
    Object? birthDateError = freezed,
    Object? phoneNumberError = freezed,
    Object? emailError = freezed,
    Object? passwordError = freezed,
    Object? confirmPasswordError = freezed,
    Object? privacyError = freezed,
    Object? registrationError = freezed,
    Object? socialRegistrationError = freezed,
  }) {
    return _then(_$RegistrationStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RegistrationStatus,
      socialStatus: null == socialStatus
          ? _value.socialStatus
          : socialStatus // ignore: cast_nullable_to_non_nullable
              as RegistrationWithSocialStatus,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as Gender?,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      firstNameValidationStatus: null == firstNameValidationStatus
          ? _value.firstNameValidationStatus
          : firstNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      lastNameValidationStatus: null == lastNameValidationStatus
          ? _value.lastNameValidationStatus
          : lastNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userNameValidationStatus: null == userNameValidationStatus
          ? _value.userNameValidationStatus
          : userNameValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      birthDate: null == birthDate
          ? _value.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as ({String code, String number}),
      phoneNumberValidationStatus: null == phoneNumberValidationStatus
          ? _value.phoneNumberValidationStatus
          : phoneNumberValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      emailValidationStatus: null == emailValidationStatus
          ? _value.emailValidationStatus
          : emailValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      passwordValidationStatus: null == passwordValidationStatus
          ? _value.passwordValidationStatus
          : passwordValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPasswordValidationStatus: null == confirmPasswordValidationStatus
          ? _value.confirmPasswordValidationStatus
          : confirmPasswordValidationStatus // ignore: cast_nullable_to_non_nullable
              as ValidationStatus,
      isObscured: null == isObscured
          ? _value.isObscured
          : isObscured // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmObscured: null == isConfirmObscured
          ? _value.isConfirmObscured
          : isConfirmObscured // ignore: cast_nullable_to_non_nullable
              as bool,
      isPrivacyAccepted: null == isPrivacyAccepted
          ? _value.isPrivacyAccepted
          : isPrivacyAccepted // ignore: cast_nullable_to_non_nullable
              as bool,
      socialAuthOnBoardingRequest: freezed == socialAuthOnBoardingRequest
          ? _value.socialAuthOnBoardingRequest
          : socialAuthOnBoardingRequest // ignore: cast_nullable_to_non_nullable
              as SocialAuthOnBoardingRequest?,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      firstNameError: freezed == firstNameError
          ? _value.firstNameError
          : firstNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      lastNameError: freezed == lastNameError
          ? _value.lastNameError
          : lastNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      userNameError: freezed == userNameError
          ? _value.userNameError
          : userNameError // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateError: freezed == birthDateError
          ? _value.birthDateError
          : birthDateError // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumberError: freezed == phoneNumberError
          ? _value.phoneNumberError
          : phoneNumberError // ignore: cast_nullable_to_non_nullable
              as String?,
      emailError: freezed == emailError
          ? _value.emailError
          : emailError // ignore: cast_nullable_to_non_nullable
              as String?,
      passwordError: freezed == passwordError
          ? _value.passwordError
          : passwordError // ignore: cast_nullable_to_non_nullable
              as String?,
      confirmPasswordError: freezed == confirmPasswordError
          ? _value.confirmPasswordError
          : confirmPasswordError // ignore: cast_nullable_to_non_nullable
              as String?,
      privacyError: freezed == privacyError
          ? _value.privacyError
          : privacyError // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationError: freezed == registrationError
          ? _value.registrationError
          : registrationError // ignore: cast_nullable_to_non_nullable
              as String?,
      socialRegistrationError: freezed == socialRegistrationError
          ? _value.socialRegistrationError
          : socialRegistrationError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RegistrationStateImpl implements _RegistrationState {
  const _$RegistrationStateImpl(
      {this.status = RegistrationStatus.idle,
      this.socialStatus = RegistrationWithSocialStatus.idle,
      this.gender,
      this.firstName = '',
      this.firstNameValidationStatus = ValidationStatus.idleOrInvalid,
      this.lastName = '',
      this.lastNameValidationStatus = ValidationStatus.idleOrInvalid,
      this.userName = '',
      this.userNameValidationStatus = ValidationStatus.idleOrInvalid,
      this.birthDate = '',
      this.phoneNumber = const (code: '', number: ''),
      this.phoneNumberValidationStatus = ValidationStatus.idleOrInvalid,
      this.email = '',
      this.emailValidationStatus = ValidationStatus.idleOrInvalid,
      this.password = '',
      this.passwordValidationStatus = ValidationStatus.idleOrInvalid,
      this.confirmPassword = '',
      this.confirmPasswordValidationStatus = ValidationStatus.idleOrInvalid,
      this.isObscured = true,
      this.isConfirmObscured = true,
      this.isPrivacyAccepted = false,
      this.socialAuthOnBoardingRequest,
      this.isRegistered = false,
      this.firstNameError,
      this.lastNameError,
      this.userNameError,
      this.birthDateError,
      this.phoneNumberError,
      this.emailError,
      this.passwordError,
      this.confirmPasswordError,
      this.privacyError,
      this.registrationError,
      this.socialRegistrationError});

  @override
  @JsonKey()
  final RegistrationStatus status;
  @override
  @JsonKey()
  final RegistrationWithSocialStatus socialStatus;
  @override
  final Gender? gender;
  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final ValidationStatus firstNameValidationStatus;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final ValidationStatus lastNameValidationStatus;
  @override
  @JsonKey()
  final String userName;
  @override
  @JsonKey()
  final ValidationStatus userNameValidationStatus;
  @override
  @JsonKey()
  final String birthDate;
  @override
  @JsonKey()
  final ({String code, String number}) phoneNumber;
  @override
  @JsonKey()
  final ValidationStatus phoneNumberValidationStatus;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final ValidationStatus emailValidationStatus;
  @override
  @JsonKey()
  final String password;
  @override
  @JsonKey()
  final ValidationStatus passwordValidationStatus;
  @override
  @JsonKey()
  final String confirmPassword;
  @override
  @JsonKey()
  final ValidationStatus confirmPasswordValidationStatus;
  @override
  @JsonKey()
  final bool isObscured;
  @override
  @JsonKey()
  final bool isConfirmObscured;
  @override
  @JsonKey()
  final bool isPrivacyAccepted;
  @override
  final SocialAuthOnBoardingRequest? socialAuthOnBoardingRequest;
  @override
  @JsonKey()
  final bool isRegistered;
  @override
  final String? firstNameError;
  @override
  final String? lastNameError;
  @override
  final String? userNameError;
  @override
  final String? birthDateError;
  @override
  final String? phoneNumberError;
  @override
  final String? emailError;
  @override
  final String? passwordError;
  @override
  final String? confirmPasswordError;
  @override
  final String? privacyError;
  @override
  final String? registrationError;
  @override
  final String? socialRegistrationError;

  @override
  String toString() {
    return 'RegistrationState(status: $status, socialStatus: $socialStatus, gender: $gender, firstName: $firstName, firstNameValidationStatus: $firstNameValidationStatus, lastName: $lastName, lastNameValidationStatus: $lastNameValidationStatus, userName: $userName, userNameValidationStatus: $userNameValidationStatus, birthDate: $birthDate, phoneNumber: $phoneNumber, phoneNumberValidationStatus: $phoneNumberValidationStatus, email: $email, emailValidationStatus: $emailValidationStatus, password: $password, passwordValidationStatus: $passwordValidationStatus, confirmPassword: $confirmPassword, confirmPasswordValidationStatus: $confirmPasswordValidationStatus, isObscured: $isObscured, isConfirmObscured: $isConfirmObscured, isPrivacyAccepted: $isPrivacyAccepted, socialAuthOnBoardingRequest: $socialAuthOnBoardingRequest, isRegistered: $isRegistered, firstNameError: $firstNameError, lastNameError: $lastNameError, userNameError: $userNameError, birthDateError: $birthDateError, phoneNumberError: $phoneNumberError, emailError: $emailError, passwordError: $passwordError, confirmPasswordError: $confirmPasswordError, privacyError: $privacyError, registrationError: $registrationError, socialRegistrationError: $socialRegistrationError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegistrationStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.socialStatus, socialStatus) ||
                other.socialStatus == socialStatus) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.firstNameValidationStatus, firstNameValidationStatus) ||
                other.firstNameValidationStatus == firstNameValidationStatus) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.lastNameValidationStatus, lastNameValidationStatus) ||
                other.lastNameValidationStatus == lastNameValidationStatus) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userNameValidationStatus, userNameValidationStatus) ||
                other.userNameValidationStatus == userNameValidationStatus) &&
            (identical(other.birthDate, birthDate) ||
                other.birthDate == birthDate) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.phoneNumberValidationStatus, phoneNumberValidationStatus) ||
                other.phoneNumberValidationStatus ==
                    phoneNumberValidationStatus) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailValidationStatus, emailValidationStatus) ||
                other.emailValidationStatus == emailValidationStatus) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.passwordValidationStatus, passwordValidationStatus) ||
                other.passwordValidationStatus == passwordValidationStatus) &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword) &&
            (identical(other.confirmPasswordValidationStatus, confirmPasswordValidationStatus) ||
                other.confirmPasswordValidationStatus ==
                    confirmPasswordValidationStatus) &&
            (identical(other.isObscured, isObscured) ||
                other.isObscured == isObscured) &&
            (identical(other.isConfirmObscured, isConfirmObscured) ||
                other.isConfirmObscured == isConfirmObscured) &&
            (identical(other.isPrivacyAccepted, isPrivacyAccepted) ||
                other.isPrivacyAccepted == isPrivacyAccepted) &&
            (identical(other.socialAuthOnBoardingRequest, socialAuthOnBoardingRequest) ||
                other.socialAuthOnBoardingRequest ==
                    socialAuthOnBoardingRequest) &&
            (identical(other.isRegistered, isRegistered) ||
                other.isRegistered == isRegistered) &&
            (identical(other.firstNameError, firstNameError) || other.firstNameError == firstNameError) &&
            (identical(other.lastNameError, lastNameError) || other.lastNameError == lastNameError) &&
            (identical(other.userNameError, userNameError) || other.userNameError == userNameError) &&
            (identical(other.birthDateError, birthDateError) || other.birthDateError == birthDateError) &&
            (identical(other.phoneNumberError, phoneNumberError) || other.phoneNumberError == phoneNumberError) &&
            (identical(other.emailError, emailError) || other.emailError == emailError) &&
            (identical(other.passwordError, passwordError) || other.passwordError == passwordError) &&
            (identical(other.confirmPasswordError, confirmPasswordError) || other.confirmPasswordError == confirmPasswordError) &&
            (identical(other.privacyError, privacyError) || other.privacyError == privacyError) &&
            (identical(other.registrationError, registrationError) || other.registrationError == registrationError) &&
            (identical(other.socialRegistrationError, socialRegistrationError) || other.socialRegistrationError == socialRegistrationError));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        status,
        socialStatus,
        gender,
        firstName,
        firstNameValidationStatus,
        lastName,
        lastNameValidationStatus,
        userName,
        userNameValidationStatus,
        birthDate,
        phoneNumber,
        phoneNumberValidationStatus,
        email,
        emailValidationStatus,
        password,
        passwordValidationStatus,
        confirmPassword,
        confirmPasswordValidationStatus,
        isObscured,
        isConfirmObscured,
        isPrivacyAccepted,
        socialAuthOnBoardingRequest,
        isRegistered,
        firstNameError,
        lastNameError,
        userNameError,
        birthDateError,
        phoneNumberError,
        emailError,
        passwordError,
        confirmPasswordError,
        privacyError,
        registrationError,
        socialRegistrationError
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegistrationStateImplCopyWith<_$RegistrationStateImpl> get copyWith =>
      __$$RegistrationStateImplCopyWithImpl<_$RegistrationStateImpl>(
          this, _$identity);
}

abstract class _RegistrationState implements RegistrationState {
  const factory _RegistrationState(
      {final RegistrationStatus status,
      final RegistrationWithSocialStatus socialStatus,
      final Gender? gender,
      final String firstName,
      final ValidationStatus firstNameValidationStatus,
      final String lastName,
      final ValidationStatus lastNameValidationStatus,
      final String userName,
      final ValidationStatus userNameValidationStatus,
      final String birthDate,
      final ({String code, String number}) phoneNumber,
      final ValidationStatus phoneNumberValidationStatus,
      final String email,
      final ValidationStatus emailValidationStatus,
      final String password,
      final ValidationStatus passwordValidationStatus,
      final String confirmPassword,
      final ValidationStatus confirmPasswordValidationStatus,
      final bool isObscured,
      final bool isConfirmObscured,
      final bool isPrivacyAccepted,
      final SocialAuthOnBoardingRequest? socialAuthOnBoardingRequest,
      final bool isRegistered,
      final String? firstNameError,
      final String? lastNameError,
      final String? userNameError,
      final String? birthDateError,
      final String? phoneNumberError,
      final String? emailError,
      final String? passwordError,
      final String? confirmPasswordError,
      final String? privacyError,
      final String? registrationError,
      final String? socialRegistrationError}) = _$RegistrationStateImpl;

  @override
  RegistrationStatus get status;
  @override
  RegistrationWithSocialStatus get socialStatus;
  @override
  Gender? get gender;
  @override
  String get firstName;
  @override
  ValidationStatus get firstNameValidationStatus;
  @override
  String get lastName;
  @override
  ValidationStatus get lastNameValidationStatus;
  @override
  String get userName;
  @override
  ValidationStatus get userNameValidationStatus;
  @override
  String get birthDate;
  @override
  ({String code, String number}) get phoneNumber;
  @override
  ValidationStatus get phoneNumberValidationStatus;
  @override
  String get email;
  @override
  ValidationStatus get emailValidationStatus;
  @override
  String get password;
  @override
  ValidationStatus get passwordValidationStatus;
  @override
  String get confirmPassword;
  @override
  ValidationStatus get confirmPasswordValidationStatus;
  @override
  bool get isObscured;
  @override
  bool get isConfirmObscured;
  @override
  bool get isPrivacyAccepted;
  @override
  SocialAuthOnBoardingRequest? get socialAuthOnBoardingRequest;
  @override
  bool get isRegistered;
  @override
  String? get firstNameError;
  @override
  String? get lastNameError;
  @override
  String? get userNameError;
  @override
  String? get birthDateError;
  @override
  String? get phoneNumberError;
  @override
  String? get emailError;
  @override
  String? get passwordError;
  @override
  String? get confirmPasswordError;
  @override
  String? get privacyError;
  @override
  String? get registrationError;
  @override
  String? get socialRegistrationError;
  @override
  @JsonKey(ignore: true)
  _$$RegistrationStateImplCopyWith<_$RegistrationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
