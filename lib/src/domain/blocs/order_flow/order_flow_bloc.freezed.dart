// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_flow_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderFlowEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderFlowEventCopyWith<$Res> {
  factory $OrderFlowEventCopyWith(
          OrderFlowEvent value, $Res Function(OrderFlowEvent) then) =
      _$OrderFlowEventCopyWithImpl<$Res, OrderFlowEvent>;
}

/// @nodoc
class _$OrderFlowEventCopyWithImpl<$Res, $Val extends OrderFlowEvent>
    implements $OrderFlowEventCopyWith<$Res> {
  _$OrderFlowEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$FetchMenusImplCopyWith<$Res> {
  factory _$$FetchMenusImplCopyWith(
          _$FetchMenusImpl value, $Res Function(_$FetchMenusImpl) then) =
      __$$FetchMenusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId});
}

/// @nodoc
class __$$FetchMenusImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$FetchMenusImpl>
    implements _$$FetchMenusImplCopyWith<$Res> {
  __$$FetchMenusImplCopyWithImpl(
      _$FetchMenusImpl _value, $Res Function(_$FetchMenusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
  }) {
    return _then(_$FetchMenusImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FetchMenusImpl implements _FetchMenus {
  const _$FetchMenusImpl(this.restaurantId);

  @override
  final String restaurantId;

  @override
  String toString() {
    return 'OrderFlowEvent.fetchMenus(restaurantId: $restaurantId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchMenusImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FetchMenusImplCopyWith<_$FetchMenusImpl> get copyWith =>
      __$$FetchMenusImplCopyWithImpl<_$FetchMenusImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return fetchMenus(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return fetchMenus?.call(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (fetchMenus != null) {
      return fetchMenus(restaurantId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return fetchMenus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return fetchMenus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (fetchMenus != null) {
      return fetchMenus(this);
    }
    return orElse();
  }
}

abstract class _FetchMenus implements OrderFlowEvent {
  const factory _FetchMenus(final String restaurantId) = _$FetchMenusImpl;

  String get restaurantId;
  @JsonKey(ignore: true)
  _$$FetchMenusImplCopyWith<_$FetchMenusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FetchSpecialsImplCopyWith<$Res> {
  factory _$$FetchSpecialsImplCopyWith(
          _$FetchSpecialsImpl value, $Res Function(_$FetchSpecialsImpl) then) =
      __$$FetchSpecialsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId});
}

/// @nodoc
class __$$FetchSpecialsImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$FetchSpecialsImpl>
    implements _$$FetchSpecialsImplCopyWith<$Res> {
  __$$FetchSpecialsImplCopyWithImpl(
      _$FetchSpecialsImpl _value, $Res Function(_$FetchSpecialsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
  }) {
    return _then(_$FetchSpecialsImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FetchSpecialsImpl implements _FetchSpecials {
  const _$FetchSpecialsImpl(this.restaurantId);

  @override
  final String restaurantId;

  @override
  String toString() {
    return 'OrderFlowEvent.fetchSpecials(restaurantId: $restaurantId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchSpecialsImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FetchSpecialsImplCopyWith<_$FetchSpecialsImpl> get copyWith =>
      __$$FetchSpecialsImplCopyWithImpl<_$FetchSpecialsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return fetchSpecials(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return fetchSpecials?.call(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (fetchSpecials != null) {
      return fetchSpecials(restaurantId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return fetchSpecials(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return fetchSpecials?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (fetchSpecials != null) {
      return fetchSpecials(this);
    }
    return orElse();
  }
}

abstract class _FetchSpecials implements OrderFlowEvent {
  const factory _FetchSpecials(final String restaurantId) = _$FetchSpecialsImpl;

  String get restaurantId;
  @JsonKey(ignore: true)
  _$$FetchSpecialsImplCopyWith<_$FetchSpecialsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddDishToCartImplCopyWith<$Res> {
  factory _$$AddDishToCartImplCopyWith(
          _$AddDishToCartImpl value, $Res Function(_$AddDishToCartImpl) then) =
      __$$AddDishToCartImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String restaurantId,
      DishModel dish,
      int quantity,
      Map<String, List<ExtraItemModel>>? groupedExtras,
      String? notes,
      dynamic withUnique,
      String? uuid});

  $DishModelCopyWith<$Res> get dish;
}

/// @nodoc
class __$$AddDishToCartImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$AddDishToCartImpl>
    implements _$$AddDishToCartImplCopyWith<$Res> {
  __$$AddDishToCartImplCopyWithImpl(
      _$AddDishToCartImpl _value, $Res Function(_$AddDishToCartImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? dish = null,
    Object? quantity = null,
    Object? groupedExtras = freezed,
    Object? notes = freezed,
    Object? withUnique = freezed,
    Object? uuid = freezed,
  }) {
    return _then(_$AddDishToCartImpl(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      dish: null == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as DishModel,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      groupedExtras: freezed == groupedExtras
          ? _value._groupedExtras
          : groupedExtras // ignore: cast_nullable_to_non_nullable
              as Map<String, List<ExtraItemModel>>?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      withUnique: freezed == withUnique ? _value.withUnique! : withUnique,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $DishModelCopyWith<$Res> get dish {
    return $DishModelCopyWith<$Res>(_value.dish, (value) {
      return _then(_value.copyWith(dish: value));
    });
  }
}

/// @nodoc

class _$AddDishToCartImpl implements _AddDishToCart {
  const _$AddDishToCartImpl(
      {required this.restaurantId,
      required this.dish,
      required this.quantity,
      final Map<String, List<ExtraItemModel>>? groupedExtras,
      this.notes,
      this.withUnique = false,
      this.uuid})
      : _groupedExtras = groupedExtras;

  @override
  final String restaurantId;
  @override
  final DishModel dish;
  @override
  final int quantity;
  final Map<String, List<ExtraItemModel>>? _groupedExtras;
  @override
  Map<String, List<ExtraItemModel>>? get groupedExtras {
    final value = _groupedExtras;
    if (value == null) return null;
    if (_groupedExtras is EqualUnmodifiableMapView) return _groupedExtras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? notes;
  @override
  @JsonKey()
  final dynamic withUnique;
  @override
  final String? uuid;

  @override
  String toString() {
    return 'OrderFlowEvent.addDishToCart(restaurantId: $restaurantId, dish: $dish, quantity: $quantity, groupedExtras: $groupedExtras, notes: $notes, withUnique: $withUnique, uuid: $uuid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddDishToCartImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.dish, dish) || other.dish == dish) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            const DeepCollectionEquality()
                .equals(other._groupedExtras, _groupedExtras) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality()
                .equals(other.withUnique, withUnique) &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      restaurantId,
      dish,
      quantity,
      const DeepCollectionEquality().hash(_groupedExtras),
      notes,
      const DeepCollectionEquality().hash(withUnique),
      uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddDishToCartImplCopyWith<_$AddDishToCartImpl> get copyWith =>
      __$$AddDishToCartImplCopyWithImpl<_$AddDishToCartImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return addDishToCart(
        restaurantId, dish, quantity, groupedExtras, notes, withUnique, uuid);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return addDishToCart?.call(
        restaurantId, dish, quantity, groupedExtras, notes, withUnique, uuid);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addDishToCart != null) {
      return addDishToCart(
          restaurantId, dish, quantity, groupedExtras, notes, withUnique, uuid);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return addDishToCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return addDishToCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addDishToCart != null) {
      return addDishToCart(this);
    }
    return orElse();
  }
}

abstract class _AddDishToCart implements OrderFlowEvent {
  const factory _AddDishToCart(
      {required final String restaurantId,
      required final DishModel dish,
      required final int quantity,
      final Map<String, List<ExtraItemModel>>? groupedExtras,
      final String? notes,
      final dynamic withUnique,
      final String? uuid}) = _$AddDishToCartImpl;

  String get restaurantId;
  DishModel get dish;
  int get quantity;
  Map<String, List<ExtraItemModel>>? get groupedExtras;
  String? get notes;
  dynamic get withUnique;
  String? get uuid;
  @JsonKey(ignore: true)
  _$$AddDishToCartImplCopyWith<_$AddDishToCartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveDishFromCartImplCopyWith<$Res> {
  factory _$$RemoveDishFromCartImplCopyWith(_$RemoveDishFromCartImpl value,
          $Res Function(_$RemoveDishFromCartImpl) then) =
      __$$RemoveDishFromCartImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId, DishModel dish, String? uuid});

  $DishModelCopyWith<$Res> get dish;
}

/// @nodoc
class __$$RemoveDishFromCartImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$RemoveDishFromCartImpl>
    implements _$$RemoveDishFromCartImplCopyWith<$Res> {
  __$$RemoveDishFromCartImplCopyWithImpl(_$RemoveDishFromCartImpl _value,
      $Res Function(_$RemoveDishFromCartImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? dish = null,
    Object? uuid = freezed,
  }) {
    return _then(_$RemoveDishFromCartImpl(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      dish: null == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as DishModel,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $DishModelCopyWith<$Res> get dish {
    return $DishModelCopyWith<$Res>(_value.dish, (value) {
      return _then(_value.copyWith(dish: value));
    });
  }
}

/// @nodoc

class _$RemoveDishFromCartImpl implements _RemoveDishFromCart {
  const _$RemoveDishFromCartImpl(
      {required this.restaurantId, required this.dish, this.uuid});

  @override
  final String restaurantId;
  @override
  final DishModel dish;
  @override
  final String? uuid;

  @override
  String toString() {
    return 'OrderFlowEvent.removeDishFromCart(restaurantId: $restaurantId, dish: $dish, uuid: $uuid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveDishFromCartImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.dish, dish) || other.dish == dish) &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId, dish, uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveDishFromCartImplCopyWith<_$RemoveDishFromCartImpl> get copyWith =>
      __$$RemoveDishFromCartImplCopyWithImpl<_$RemoveDishFromCartImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return removeDishFromCart(restaurantId, dish, uuid);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return removeDishFromCart?.call(restaurantId, dish, uuid);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (removeDishFromCart != null) {
      return removeDishFromCart(restaurantId, dish, uuid);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return removeDishFromCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return removeDishFromCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (removeDishFromCart != null) {
      return removeDishFromCart(this);
    }
    return orElse();
  }
}

abstract class _RemoveDishFromCart implements OrderFlowEvent {
  const factory _RemoveDishFromCart(
      {required final String restaurantId,
      required final DishModel dish,
      final String? uuid}) = _$RemoveDishFromCartImpl;

  String get restaurantId;
  DishModel get dish;
  String? get uuid;
  @JsonKey(ignore: true)
  _$$RemoveDishFromCartImplCopyWith<_$RemoveDishFromCartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddSpecialToCartImplCopyWith<$Res> {
  factory _$$AddSpecialToCartImplCopyWith(_$AddSpecialToCartImpl value,
          $Res Function(_$AddSpecialToCartImpl) then) =
      __$$AddSpecialToCartImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String restaurantId,
      SpecialModel special,
      int quantity,
      Set<SpecialComponent>? selectedComponents,
      String? notes});

  $SpecialModelCopyWith<$Res> get special;
}

/// @nodoc
class __$$AddSpecialToCartImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$AddSpecialToCartImpl>
    implements _$$AddSpecialToCartImplCopyWith<$Res> {
  __$$AddSpecialToCartImplCopyWithImpl(_$AddSpecialToCartImpl _value,
      $Res Function(_$AddSpecialToCartImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? special = null,
    Object? quantity = null,
    Object? selectedComponents = freezed,
    Object? notes = freezed,
  }) {
    return _then(_$AddSpecialToCartImpl(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      special: null == special
          ? _value.special
          : special // ignore: cast_nullable_to_non_nullable
              as SpecialModel,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      selectedComponents: freezed == selectedComponents
          ? _value._selectedComponents
          : selectedComponents // ignore: cast_nullable_to_non_nullable
              as Set<SpecialComponent>?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $SpecialModelCopyWith<$Res> get special {
    return $SpecialModelCopyWith<$Res>(_value.special, (value) {
      return _then(_value.copyWith(special: value));
    });
  }
}

/// @nodoc

class _$AddSpecialToCartImpl implements _AddSpecialToCart {
  const _$AddSpecialToCartImpl(
      {required this.restaurantId,
      required this.special,
      required this.quantity,
      final Set<SpecialComponent>? selectedComponents,
      this.notes})
      : _selectedComponents = selectedComponents;

  @override
  final String restaurantId;
  @override
  final SpecialModel special;
  @override
  final int quantity;
  final Set<SpecialComponent>? _selectedComponents;
  @override
  Set<SpecialComponent>? get selectedComponents {
    final value = _selectedComponents;
    if (value == null) return null;
    if (_selectedComponents is EqualUnmodifiableSetView)
      return _selectedComponents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(value);
  }

  @override
  final String? notes;

  @override
  String toString() {
    return 'OrderFlowEvent.addSpecialToCart(restaurantId: $restaurantId, special: $special, quantity: $quantity, selectedComponents: $selectedComponents, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddSpecialToCartImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.special, special) || other.special == special) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            const DeepCollectionEquality()
                .equals(other._selectedComponents, _selectedComponents) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId, special, quantity,
      const DeepCollectionEquality().hash(_selectedComponents), notes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddSpecialToCartImplCopyWith<_$AddSpecialToCartImpl> get copyWith =>
      __$$AddSpecialToCartImplCopyWithImpl<_$AddSpecialToCartImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return addSpecialToCart(
        restaurantId, special, quantity, selectedComponents, notes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return addSpecialToCart?.call(
        restaurantId, special, quantity, selectedComponents, notes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addSpecialToCart != null) {
      return addSpecialToCart(
          restaurantId, special, quantity, selectedComponents, notes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return addSpecialToCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return addSpecialToCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addSpecialToCart != null) {
      return addSpecialToCart(this);
    }
    return orElse();
  }
}

abstract class _AddSpecialToCart implements OrderFlowEvent {
  const factory _AddSpecialToCart(
      {required final String restaurantId,
      required final SpecialModel special,
      required final int quantity,
      final Set<SpecialComponent>? selectedComponents,
      final String? notes}) = _$AddSpecialToCartImpl;

  String get restaurantId;
  SpecialModel get special;
  int get quantity;
  Set<SpecialComponent>? get selectedComponents;
  String? get notes;
  @JsonKey(ignore: true)
  _$$AddSpecialToCartImplCopyWith<_$AddSpecialToCartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoveSpecialFromCartImplCopyWith<$Res> {
  factory _$$RemoveSpecialFromCartImplCopyWith(
          _$RemoveSpecialFromCartImpl value,
          $Res Function(_$RemoveSpecialFromCartImpl) then) =
      __$$RemoveSpecialFromCartImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId, SpecialModel special});

  $SpecialModelCopyWith<$Res> get special;
}

/// @nodoc
class __$$RemoveSpecialFromCartImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$RemoveSpecialFromCartImpl>
    implements _$$RemoveSpecialFromCartImplCopyWith<$Res> {
  __$$RemoveSpecialFromCartImplCopyWithImpl(_$RemoveSpecialFromCartImpl _value,
      $Res Function(_$RemoveSpecialFromCartImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? special = null,
  }) {
    return _then(_$RemoveSpecialFromCartImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      null == special
          ? _value.special
          : special // ignore: cast_nullable_to_non_nullable
              as SpecialModel,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $SpecialModelCopyWith<$Res> get special {
    return $SpecialModelCopyWith<$Res>(_value.special, (value) {
      return _then(_value.copyWith(special: value));
    });
  }
}

/// @nodoc

class _$RemoveSpecialFromCartImpl implements _RemoveSpecialFromCart {
  const _$RemoveSpecialFromCartImpl(this.restaurantId, this.special);

  @override
  final String restaurantId;
  @override
  final SpecialModel special;

  @override
  String toString() {
    return 'OrderFlowEvent.removeSpecialFromCart(restaurantId: $restaurantId, special: $special)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveSpecialFromCartImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.special, special) || other.special == special));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId, special);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveSpecialFromCartImplCopyWith<_$RemoveSpecialFromCartImpl>
      get copyWith => __$$RemoveSpecialFromCartImplCopyWithImpl<
          _$RemoveSpecialFromCartImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return removeSpecialFromCart(restaurantId, special);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return removeSpecialFromCart?.call(restaurantId, special);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (removeSpecialFromCart != null) {
      return removeSpecialFromCart(restaurantId, special);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return removeSpecialFromCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return removeSpecialFromCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (removeSpecialFromCart != null) {
      return removeSpecialFromCart(this);
    }
    return orElse();
  }
}

abstract class _RemoveSpecialFromCart implements OrderFlowEvent {
  const factory _RemoveSpecialFromCart(
          final String restaurantId, final SpecialModel special) =
      _$RemoveSpecialFromCartImpl;

  String get restaurantId;
  SpecialModel get special;
  @JsonKey(ignore: true)
  _$$RemoveSpecialFromCartImplCopyWith<_$RemoveSpecialFromCartImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeOrderFlowTypeImplCopyWith<$Res> {
  factory _$$ChangeOrderFlowTypeImplCopyWith(_$ChangeOrderFlowTypeImpl value,
          $Res Function(_$ChangeOrderFlowTypeImpl) then) =
      __$$ChangeOrderFlowTypeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OrderFlowType type});
}

/// @nodoc
class __$$ChangeOrderFlowTypeImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ChangeOrderFlowTypeImpl>
    implements _$$ChangeOrderFlowTypeImplCopyWith<$Res> {
  __$$ChangeOrderFlowTypeImplCopyWithImpl(_$ChangeOrderFlowTypeImpl _value,
      $Res Function(_$ChangeOrderFlowTypeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$ChangeOrderFlowTypeImpl(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OrderFlowType,
    ));
  }
}

/// @nodoc

class _$ChangeOrderFlowTypeImpl implements _ChangeOrderFlowType {
  const _$ChangeOrderFlowTypeImpl(this.type);

  @override
  final OrderFlowType type;

  @override
  String toString() {
    return 'OrderFlowEvent.changeOrderFlowType(type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeOrderFlowTypeImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeOrderFlowTypeImplCopyWith<_$ChangeOrderFlowTypeImpl> get copyWith =>
      __$$ChangeOrderFlowTypeImplCopyWithImpl<_$ChangeOrderFlowTypeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return changeOrderFlowType(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return changeOrderFlowType?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeOrderFlowType != null) {
      return changeOrderFlowType(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return changeOrderFlowType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return changeOrderFlowType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeOrderFlowType != null) {
      return changeOrderFlowType(this);
    }
    return orElse();
  }
}

abstract class _ChangeOrderFlowType implements OrderFlowEvent {
  const factory _ChangeOrderFlowType(final OrderFlowType type) =
      _$ChangeOrderFlowTypeImpl;

  OrderFlowType get type;
  @JsonKey(ignore: true)
  _$$ChangeOrderFlowTypeImplCopyWith<_$ChangeOrderFlowTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddSpecialRequirementsToDishImplCopyWith<$Res> {
  factory _$$AddSpecialRequirementsToDishImplCopyWith(
          _$AddSpecialRequirementsToDishImpl value,
          $Res Function(_$AddSpecialRequirementsToDishImpl) then) =
      __$$AddSpecialRequirementsToDishImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId, DishModel dish, String specialRequirements});

  $DishModelCopyWith<$Res> get dish;
}

/// @nodoc
class __$$AddSpecialRequirementsToDishImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res,
        _$AddSpecialRequirementsToDishImpl>
    implements _$$AddSpecialRequirementsToDishImplCopyWith<$Res> {
  __$$AddSpecialRequirementsToDishImplCopyWithImpl(
      _$AddSpecialRequirementsToDishImpl _value,
      $Res Function(_$AddSpecialRequirementsToDishImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? dish = null,
    Object? specialRequirements = null,
  }) {
    return _then(_$AddSpecialRequirementsToDishImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      null == dish
          ? _value.dish
          : dish // ignore: cast_nullable_to_non_nullable
              as DishModel,
      null == specialRequirements
          ? _value.specialRequirements
          : specialRequirements // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $DishModelCopyWith<$Res> get dish {
    return $DishModelCopyWith<$Res>(_value.dish, (value) {
      return _then(_value.copyWith(dish: value));
    });
  }
}

/// @nodoc

class _$AddSpecialRequirementsToDishImpl
    implements _AddSpecialRequirementsToDish {
  const _$AddSpecialRequirementsToDishImpl(
      this.restaurantId, this.dish, this.specialRequirements);

  @override
  final String restaurantId;
  @override
  final DishModel dish;
  @override
  final String specialRequirements;

  @override
  String toString() {
    return 'OrderFlowEvent.addSpecialRequirementsToDish(restaurantId: $restaurantId, dish: $dish, specialRequirements: $specialRequirements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddSpecialRequirementsToDishImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.dish, dish) || other.dish == dish) &&
            (identical(other.specialRequirements, specialRequirements) ||
                other.specialRequirements == specialRequirements));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, restaurantId, dish, specialRequirements);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddSpecialRequirementsToDishImplCopyWith<
          _$AddSpecialRequirementsToDishImpl>
      get copyWith => __$$AddSpecialRequirementsToDishImplCopyWithImpl<
          _$AddSpecialRequirementsToDishImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return addSpecialRequirementsToDish(
        restaurantId, dish, specialRequirements);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return addSpecialRequirementsToDish?.call(
        restaurantId, dish, specialRequirements);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addSpecialRequirementsToDish != null) {
      return addSpecialRequirementsToDish(
          restaurantId, dish, specialRequirements);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return addSpecialRequirementsToDish(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return addSpecialRequirementsToDish?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (addSpecialRequirementsToDish != null) {
      return addSpecialRequirementsToDish(this);
    }
    return orElse();
  }
}

abstract class _AddSpecialRequirementsToDish implements OrderFlowEvent {
  const factory _AddSpecialRequirementsToDish(
      final String restaurantId,
      final DishModel dish,
      final String specialRequirements) = _$AddSpecialRequirementsToDishImpl;

  String get restaurantId;
  DishModel get dish;
  String get specialRequirements;
  @JsonKey(ignore: true)
  _$$AddSpecialRequirementsToDishImplCopyWith<
          _$AddSpecialRequirementsToDishImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeOrderImplCopyWith<$Res> {
  factory _$$ChangeOrderImplCopyWith(
          _$ChangeOrderImpl value, $Res Function(_$ChangeOrderImpl) then) =
      __$$ChangeOrderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId});
}

/// @nodoc
class __$$ChangeOrderImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ChangeOrderImpl>
    implements _$$ChangeOrderImplCopyWith<$Res> {
  __$$ChangeOrderImplCopyWithImpl(
      _$ChangeOrderImpl _value, $Res Function(_$ChangeOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
  }) {
    return _then(_$ChangeOrderImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeOrderImpl implements _ChangeOrder {
  const _$ChangeOrderImpl(this.restaurantId);

  @override
  final String restaurantId;

  @override
  String toString() {
    return 'OrderFlowEvent.changeOrder(restaurantId: $restaurantId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeOrderImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeOrderImplCopyWith<_$ChangeOrderImpl> get copyWith =>
      __$$ChangeOrderImplCopyWithImpl<_$ChangeOrderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return changeOrder(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return changeOrder?.call(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeOrder != null) {
      return changeOrder(restaurantId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return changeOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return changeOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeOrder != null) {
      return changeOrder(this);
    }
    return orElse();
  }
}

abstract class _ChangeOrder implements OrderFlowEvent {
  const factory _ChangeOrder(final String restaurantId) = _$ChangeOrderImpl;

  String get restaurantId;
  @JsonKey(ignore: true)
  _$$ChangeOrderImplCopyWith<_$ChangeOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetPreOrderImplCopyWith<$Res> {
  factory _$$GetPreOrderImplCopyWith(
          _$GetPreOrderImpl value, $Res Function(_$GetPreOrderImpl) then) =
      __$$GetPreOrderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId});
}

/// @nodoc
class __$$GetPreOrderImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$GetPreOrderImpl>
    implements _$$GetPreOrderImplCopyWith<$Res> {
  __$$GetPreOrderImplCopyWithImpl(
      _$GetPreOrderImpl _value, $Res Function(_$GetPreOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
  }) {
    return _then(_$GetPreOrderImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$GetPreOrderImpl implements _GetPreOrder {
  const _$GetPreOrderImpl(this.restaurantId);

  @override
  final String restaurantId;

  @override
  String toString() {
    return 'OrderFlowEvent.getPreOrder(restaurantId: $restaurantId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetPreOrderImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GetPreOrderImplCopyWith<_$GetPreOrderImpl> get copyWith =>
      __$$GetPreOrderImplCopyWithImpl<_$GetPreOrderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return getPreOrder(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return getPreOrder?.call(restaurantId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (getPreOrder != null) {
      return getPreOrder(restaurantId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return getPreOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return getPreOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (getPreOrder != null) {
      return getPreOrder(this);
    }
    return orElse();
  }
}

abstract class _GetPreOrder implements OrderFlowEvent {
  const factory _GetPreOrder(final String restaurantId) = _$GetPreOrderImpl;

  String get restaurantId;
  @JsonKey(ignore: true)
  _$$GetPreOrderImplCopyWith<_$GetPreOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PayImplCopyWith<$Res> {
  factory _$$PayImplCopyWith(_$PayImpl value, $Res Function(_$PayImpl) then) =
      __$$PayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId, String stripeAccountId, String? couponId});
}

/// @nodoc
class __$$PayImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$PayImpl>
    implements _$$PayImplCopyWith<$Res> {
  __$$PayImplCopyWithImpl(_$PayImpl _value, $Res Function(_$PayImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? stripeAccountId = null,
    Object? couponId = freezed,
  }) {
    return _then(_$PayImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      null == stripeAccountId
          ? _value.stripeAccountId
          : stripeAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PayImpl implements _Pay {
  const _$PayImpl(this.restaurantId, this.stripeAccountId, this.couponId);

  @override
  final String restaurantId;
  @override
  final String stripeAccountId;
  @override
  final String? couponId;

  @override
  String toString() {
    return 'OrderFlowEvent.pay(restaurantId: $restaurantId, stripeAccountId: $stripeAccountId, couponId: $couponId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.stripeAccountId, stripeAccountId) ||
                other.stripeAccountId == stripeAccountId) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, restaurantId, stripeAccountId, couponId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PayImplCopyWith<_$PayImpl> get copyWith =>
      __$$PayImplCopyWithImpl<_$PayImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return pay(restaurantId, stripeAccountId, couponId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return pay?.call(restaurantId, stripeAccountId, couponId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (pay != null) {
      return pay(restaurantId, stripeAccountId, couponId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return pay(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return pay?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (pay != null) {
      return pay(this);
    }
    return orElse();
  }
}

abstract class _Pay implements OrderFlowEvent {
  const factory _Pay(final String restaurantId, final String stripeAccountId,
      final String? couponId) = _$PayImpl;

  String get restaurantId;
  String get stripeAccountId;
  String? get couponId;
  @JsonKey(ignore: true)
  _$$PayImplCopyWith<_$PayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeTableNumberImplCopyWith<$Res> {
  factory _$$ChangeTableNumberImplCopyWith(_$ChangeTableNumberImpl value,
          $Res Function(_$ChangeTableNumberImpl) then) =
      __$$ChangeTableNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String tableNumber});
}

/// @nodoc
class __$$ChangeTableNumberImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ChangeTableNumberImpl>
    implements _$$ChangeTableNumberImplCopyWith<$Res> {
  __$$ChangeTableNumberImplCopyWithImpl(_$ChangeTableNumberImpl _value,
      $Res Function(_$ChangeTableNumberImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tableNumber = null,
  }) {
    return _then(_$ChangeTableNumberImpl(
      null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeTableNumberImpl implements _ChangeTableNumber {
  const _$ChangeTableNumberImpl(this.tableNumber);

  @override
  final String tableNumber;

  @override
  String toString() {
    return 'OrderFlowEvent.changeTableNumber(tableNumber: $tableNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeTableNumberImpl &&
            (identical(other.tableNumber, tableNumber) ||
                other.tableNumber == tableNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tableNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeTableNumberImplCopyWith<_$ChangeTableNumberImpl> get copyWith =>
      __$$ChangeTableNumberImplCopyWithImpl<_$ChangeTableNumberImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return changeTableNumber(tableNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return changeTableNumber?.call(tableNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeTableNumber != null) {
      return changeTableNumber(tableNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return changeTableNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return changeTableNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeTableNumber != null) {
      return changeTableNumber(this);
    }
    return orElse();
  }
}

abstract class _ChangeTableNumber implements OrderFlowEvent {
  const factory _ChangeTableNumber(final String tableNumber) =
      _$ChangeTableNumberImpl;

  String get tableNumber;
  @JsonKey(ignore: true)
  _$$ChangeTableNumberImplCopyWith<_$ChangeTableNumberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFavoriteImplCopyWith<$Res> {
  factory _$$ChangeFavoriteImplCopyWith(_$ChangeFavoriteImpl value,
          $Res Function(_$ChangeFavoriteImpl) then) =
      __$$ChangeFavoriteImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String restaurantId, bool isFavorite});
}

/// @nodoc
class __$$ChangeFavoriteImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ChangeFavoriteImpl>
    implements _$$ChangeFavoriteImplCopyWith<$Res> {
  __$$ChangeFavoriteImplCopyWithImpl(
      _$ChangeFavoriteImpl _value, $Res Function(_$ChangeFavoriteImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? isFavorite = null,
  }) {
    return _then(_$ChangeFavoriteImpl(
      null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      null == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ChangeFavoriteImpl implements _ChangeFavorite {
  const _$ChangeFavoriteImpl(this.restaurantId, this.isFavorite);

  @override
  final String restaurantId;
  @override
  final bool isFavorite;

  @override
  String toString() {
    return 'OrderFlowEvent.changeFavorite(restaurantId: $restaurantId, isFavorite: $isFavorite)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFavoriteImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurantId, isFavorite);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFavoriteImplCopyWith<_$ChangeFavoriteImpl> get copyWith =>
      __$$ChangeFavoriteImplCopyWithImpl<_$ChangeFavoriteImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return changeFavorite(restaurantId, isFavorite);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return changeFavorite?.call(restaurantId, isFavorite);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeFavorite != null) {
      return changeFavorite(restaurantId, isFavorite);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return changeFavorite(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return changeFavorite?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeFavorite != null) {
      return changeFavorite(this);
    }
    return orElse();
  }
}

abstract class _ChangeFavorite implements OrderFlowEvent {
  const factory _ChangeFavorite(
      final String restaurantId, final bool isFavorite) = _$ChangeFavoriteImpl;

  String get restaurantId;
  bool get isFavorite;
  @JsonKey(ignore: true)
  _$$ChangeFavoriteImplCopyWith<_$ChangeFavoriteImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeDietPreferencesImplCopyWith<$Res> {
  factory _$$ChangeDietPreferencesImplCopyWith(
          _$ChangeDietPreferencesImpl value,
          $Res Function(_$ChangeDietPreferencesImpl) then) =
      __$$ChangeDietPreferencesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Set<DietPreferencesModel> dietPreferences});
}

/// @nodoc
class __$$ChangeDietPreferencesImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ChangeDietPreferencesImpl>
    implements _$$ChangeDietPreferencesImplCopyWith<$Res> {
  __$$ChangeDietPreferencesImplCopyWithImpl(_$ChangeDietPreferencesImpl _value,
      $Res Function(_$ChangeDietPreferencesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dietPreferences = null,
  }) {
    return _then(_$ChangeDietPreferencesImpl(
      null == dietPreferences
          ? _value._dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as Set<DietPreferencesModel>,
    ));
  }
}

/// @nodoc

class _$ChangeDietPreferencesImpl implements _ChangeDietPreferences {
  const _$ChangeDietPreferencesImpl(
      final Set<DietPreferencesModel> dietPreferences)
      : _dietPreferences = dietPreferences;

  final Set<DietPreferencesModel> _dietPreferences;
  @override
  Set<DietPreferencesModel> get dietPreferences {
    if (_dietPreferences is EqualUnmodifiableSetView) return _dietPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_dietPreferences);
  }

  @override
  String toString() {
    return 'OrderFlowEvent.changeDietPreferences(dietPreferences: $dietPreferences)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeDietPreferencesImpl &&
            const DeepCollectionEquality()
                .equals(other._dietPreferences, _dietPreferences));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_dietPreferences));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeDietPreferencesImplCopyWith<_$ChangeDietPreferencesImpl>
      get copyWith => __$$ChangeDietPreferencesImplCopyWithImpl<
          _$ChangeDietPreferencesImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return changeDietPreferences(dietPreferences);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return changeDietPreferences?.call(dietPreferences);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeDietPreferences != null) {
      return changeDietPreferences(dietPreferences);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return changeDietPreferences(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return changeDietPreferences?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (changeDietPreferences != null) {
      return changeDietPreferences(this);
    }
    return orElse();
  }
}

abstract class _ChangeDietPreferences implements OrderFlowEvent {
  const factory _ChangeDietPreferences(
          final Set<DietPreferencesModel> dietPreferences) =
      _$ChangeDietPreferencesImpl;

  Set<DietPreferencesModel> get dietPreferences;
  @JsonKey(ignore: true)
  _$$ChangeDietPreferencesImplCopyWith<_$ChangeDietPreferencesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EarnPointsImplCopyWith<$Res> {
  factory _$$EarnPointsImplCopyWith(
          _$EarnPointsImpl value, $Res Function(_$EarnPointsImpl) then) =
      __$$EarnPointsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String orderId});
}

/// @nodoc
class __$$EarnPointsImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$EarnPointsImpl>
    implements _$$EarnPointsImplCopyWith<$Res> {
  __$$EarnPointsImplCopyWithImpl(
      _$EarnPointsImpl _value, $Res Function(_$EarnPointsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
  }) {
    return _then(_$EarnPointsImpl(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EarnPointsImpl implements _EarnPoints {
  const _$EarnPointsImpl({required this.orderId});

  @override
  final String orderId;

  @override
  String toString() {
    return 'OrderFlowEvent.earnPoints(orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EarnPointsImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EarnPointsImplCopyWith<_$EarnPointsImpl> get copyWith =>
      __$$EarnPointsImplCopyWithImpl<_$EarnPointsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return earnPoints(orderId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return earnPoints?.call(orderId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (earnPoints != null) {
      return earnPoints(orderId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return earnPoints(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return earnPoints?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (earnPoints != null) {
      return earnPoints(this);
    }
    return orElse();
  }
}

abstract class _EarnPoints implements OrderFlowEvent {
  const factory _EarnPoints({required final String orderId}) = _$EarnPointsImpl;

  String get orderId;
  @JsonKey(ignore: true)
  _$$EarnPointsImplCopyWith<_$EarnPointsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PrecalculateOrderImplCopyWith<$Res> {
  factory _$$PrecalculateOrderImplCopyWith(_$PrecalculateOrderImpl value,
          $Res Function(_$PrecalculateOrderImpl) then) =
      __$$PrecalculateOrderImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String restaurantId,
      String? couponId,
      List<CartItemModel> dishes,
      List<SpecialCartItemModel> specials});
}

/// @nodoc
class __$$PrecalculateOrderImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$PrecalculateOrderImpl>
    implements _$$PrecalculateOrderImplCopyWith<$Res> {
  __$$PrecalculateOrderImplCopyWithImpl(_$PrecalculateOrderImpl _value,
      $Res Function(_$PrecalculateOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurantId = null,
    Object? couponId = freezed,
    Object? dishes = null,
    Object? specials = null,
  }) {
    return _then(_$PrecalculateOrderImpl(
      restaurantId: null == restaurantId
          ? _value.restaurantId
          : restaurantId // ignore: cast_nullable_to_non_nullable
              as String,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      dishes: null == dishes
          ? _value._dishes
          : dishes // ignore: cast_nullable_to_non_nullable
              as List<CartItemModel>,
      specials: null == specials
          ? _value._specials
          : specials // ignore: cast_nullable_to_non_nullable
              as List<SpecialCartItemModel>,
    ));
  }
}

/// @nodoc

class _$PrecalculateOrderImpl implements _PrecalculateOrder {
  const _$PrecalculateOrderImpl(
      {required this.restaurantId,
      required this.couponId,
      required final List<CartItemModel> dishes,
      required final List<SpecialCartItemModel> specials})
      : _dishes = dishes,
        _specials = specials;

  @override
  final String restaurantId;
  @override
  final String? couponId;
  final List<CartItemModel> _dishes;
  @override
  List<CartItemModel> get dishes {
    if (_dishes is EqualUnmodifiableListView) return _dishes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dishes);
  }

  final List<SpecialCartItemModel> _specials;
  @override
  List<SpecialCartItemModel> get specials {
    if (_specials is EqualUnmodifiableListView) return _specials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specials);
  }

  @override
  String toString() {
    return 'OrderFlowEvent.precalculateOrder(restaurantId: $restaurantId, couponId: $couponId, dishes: $dishes, specials: $specials)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PrecalculateOrderImpl &&
            (identical(other.restaurantId, restaurantId) ||
                other.restaurantId == restaurantId) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            const DeepCollectionEquality().equals(other._dishes, _dishes) &&
            const DeepCollectionEquality().equals(other._specials, _specials));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      restaurantId,
      couponId,
      const DeepCollectionEquality().hash(_dishes),
      const DeepCollectionEquality().hash(_specials));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PrecalculateOrderImplCopyWith<_$PrecalculateOrderImpl> get copyWith =>
      __$$PrecalculateOrderImplCopyWithImpl<_$PrecalculateOrderImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return precalculateOrder(restaurantId, couponId, dishes, specials);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return precalculateOrder?.call(restaurantId, couponId, dishes, specials);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (precalculateOrder != null) {
      return precalculateOrder(restaurantId, couponId, dishes, specials);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return precalculateOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return precalculateOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (precalculateOrder != null) {
      return precalculateOrder(this);
    }
    return orElse();
  }
}

abstract class _PrecalculateOrder implements OrderFlowEvent {
  const factory _PrecalculateOrder(
          {required final String restaurantId,
          required final String? couponId,
          required final List<CartItemModel> dishes,
          required final List<SpecialCartItemModel> specials}) =
      _$PrecalculateOrderImpl;

  String get restaurantId;
  String? get couponId;
  List<CartItemModel> get dishes;
  List<SpecialCartItemModel> get specials;
  @JsonKey(ignore: true)
  _$$PrecalculateOrderImplCopyWith<_$PrecalculateOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DetachCouponImplCopyWith<$Res> {
  factory _$$DetachCouponImplCopyWith(
          _$DetachCouponImpl value, $Res Function(_$DetachCouponImpl) then) =
      __$$DetachCouponImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String orderId});
}

/// @nodoc
class __$$DetachCouponImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$DetachCouponImpl>
    implements _$$DetachCouponImplCopyWith<$Res> {
  __$$DetachCouponImplCopyWithImpl(
      _$DetachCouponImpl _value, $Res Function(_$DetachCouponImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
  }) {
    return _then(_$DetachCouponImpl(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DetachCouponImpl implements _DetachCoupon {
  const _$DetachCouponImpl({required this.orderId});

  @override
  final String orderId;

  @override
  String toString() {
    return 'OrderFlowEvent.detachCoupon(orderId: $orderId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DetachCouponImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, orderId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DetachCouponImplCopyWith<_$DetachCouponImpl> get copyWith =>
      __$$DetachCouponImplCopyWithImpl<_$DetachCouponImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return detachCoupon(orderId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return detachCoupon?.call(orderId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (detachCoupon != null) {
      return detachCoupon(orderId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return detachCoupon(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return detachCoupon?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (detachCoupon != null) {
      return detachCoupon(this);
    }
    return orElse();
  }
}

abstract class _DetachCoupon implements OrderFlowEvent {
  const factory _DetachCoupon({required final String orderId}) =
      _$DetachCouponImpl;

  String get orderId;
  @JsonKey(ignore: true)
  _$$DetachCouponImplCopyWith<_$DetachCouponImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetOrderFlowImplCopyWith<$Res> {
  factory _$$ResetOrderFlowImplCopyWith(_$ResetOrderFlowImpl value,
          $Res Function(_$ResetOrderFlowImpl) then) =
      __$$ResetOrderFlowImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetOrderFlowImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ResetOrderFlowImpl>
    implements _$$ResetOrderFlowImplCopyWith<$Res> {
  __$$ResetOrderFlowImplCopyWithImpl(
      _$ResetOrderFlowImpl _value, $Res Function(_$ResetOrderFlowImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ResetOrderFlowImpl implements _ResetOrderFlow {
  const _$ResetOrderFlowImpl();

  @override
  String toString() {
    return 'OrderFlowEvent.resetOrderFlow()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetOrderFlowImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return resetOrderFlow();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return resetOrderFlow?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (resetOrderFlow != null) {
      return resetOrderFlow();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return resetOrderFlow(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return resetOrderFlow?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (resetOrderFlow != null) {
      return resetOrderFlow(this);
    }
    return orElse();
  }
}

abstract class _ResetOrderFlow implements OrderFlowEvent {
  const factory _ResetOrderFlow() = _$ResetOrderFlowImpl;
}

/// @nodoc
abstract class _$$StoreOrderImplCopyWith<$Res> {
  factory _$$StoreOrderImplCopyWith(
          _$StoreOrderImpl value, $Res Function(_$StoreOrderImpl) then) =
      __$$StoreOrderImplCopyWithImpl<$Res>;
  @useResult
  $Res call({StoredOrderModel model});

  $StoredOrderModelCopyWith<$Res> get model;
}

/// @nodoc
class __$$StoreOrderImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$StoreOrderImpl>
    implements _$$StoreOrderImplCopyWith<$Res> {
  __$$StoreOrderImplCopyWithImpl(
      _$StoreOrderImpl _value, $Res Function(_$StoreOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
  }) {
    return _then(_$StoreOrderImpl(
      null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as StoredOrderModel,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $StoredOrderModelCopyWith<$Res> get model {
    return $StoredOrderModelCopyWith<$Res>(_value.model, (value) {
      return _then(_value.copyWith(model: value));
    });
  }
}

/// @nodoc

class _$StoreOrderImpl implements _StoreOrder {
  const _$StoreOrderImpl(this.model);

  @override
  final StoredOrderModel model;

  @override
  String toString() {
    return 'OrderFlowEvent.storeOrder(model: $model)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StoreOrderImpl &&
            (identical(other.model, model) || other.model == model));
  }

  @override
  int get hashCode => Object.hash(runtimeType, model);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StoreOrderImplCopyWith<_$StoreOrderImpl> get copyWith =>
      __$$StoreOrderImplCopyWithImpl<_$StoreOrderImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return storeOrder(model);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return storeOrder?.call(model);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (storeOrder != null) {
      return storeOrder(model);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return storeOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return storeOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (storeOrder != null) {
      return storeOrder(this);
    }
    return orElse();
  }
}

abstract class _StoreOrder implements OrderFlowEvent {
  const factory _StoreOrder(final StoredOrderModel model) = _$StoreOrderImpl;

  StoredOrderModel get model;
  @JsonKey(ignore: true)
  _$$StoreOrderImplCopyWith<_$StoreOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearStoredOrderImplCopyWith<$Res> {
  factory _$$ClearStoredOrderImplCopyWith(_$ClearStoredOrderImpl value,
          $Res Function(_$ClearStoredOrderImpl) then) =
      __$$ClearStoredOrderImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearStoredOrderImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ClearStoredOrderImpl>
    implements _$$ClearStoredOrderImplCopyWith<$Res> {
  __$$ClearStoredOrderImplCopyWithImpl(_$ClearStoredOrderImpl _value,
      $Res Function(_$ClearStoredOrderImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearStoredOrderImpl implements _ClearStoredOrder {
  const _$ClearStoredOrderImpl();

  @override
  String toString() {
    return 'OrderFlowEvent.clearStoredOrder()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearStoredOrderImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return clearStoredOrder();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return clearStoredOrder?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (clearStoredOrder != null) {
      return clearStoredOrder();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return clearStoredOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return clearStoredOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (clearStoredOrder != null) {
      return clearStoredOrder(this);
    }
    return orElse();
  }
}

abstract class _ClearStoredOrder implements OrderFlowEvent {
  const factory _ClearStoredOrder() = _$ClearStoredOrderImpl;
}

/// @nodoc
abstract class _$$ClearStoredCartImplCopyWith<$Res> {
  factory _$$ClearStoredCartImplCopyWith(_$ClearStoredCartImpl value,
          $Res Function(_$ClearStoredCartImpl) then) =
      __$$ClearStoredCartImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearStoredCartImplCopyWithImpl<$Res>
    extends _$OrderFlowEventCopyWithImpl<$Res, _$ClearStoredCartImpl>
    implements _$$ClearStoredCartImplCopyWith<$Res> {
  __$$ClearStoredCartImplCopyWithImpl(
      _$ClearStoredCartImpl _value, $Res Function(_$ClearStoredCartImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearStoredCartImpl implements _ClearStoredCart {
  const _$ClearStoredCartImpl();

  @override
  String toString() {
    return 'OrderFlowEvent.clearStoredCart()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearStoredCartImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String restaurantId) fetchMenus,
    required TResult Function(String restaurantId) fetchSpecials,
    required TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)
        addDishToCart,
    required TResult Function(String restaurantId, DishModel dish, String? uuid)
        removeDishFromCart,
    required TResult Function(
            String restaurantId,
            SpecialModel special,
            int quantity,
            Set<SpecialComponent>? selectedComponents,
            String? notes)
        addSpecialToCart,
    required TResult Function(String restaurantId, SpecialModel special)
        removeSpecialFromCart,
    required TResult Function(OrderFlowType type) changeOrderFlowType,
    required TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)
        addSpecialRequirementsToDish,
    required TResult Function(String restaurantId) changeOrder,
    required TResult Function(String restaurantId) getPreOrder,
    required TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)
        pay,
    required TResult Function(String tableNumber) changeTableNumber,
    required TResult Function(String restaurantId, bool isFavorite)
        changeFavorite,
    required TResult Function(Set<DietPreferencesModel> dietPreferences)
        changeDietPreferences,
    required TResult Function(String orderId) earnPoints,
    required TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)
        precalculateOrder,
    required TResult Function(String orderId) detachCoupon,
    required TResult Function() resetOrderFlow,
    required TResult Function(StoredOrderModel model) storeOrder,
    required TResult Function() clearStoredOrder,
    required TResult Function() clearStoredCart,
  }) {
    return clearStoredCart();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String restaurantId)? fetchMenus,
    TResult? Function(String restaurantId)? fetchSpecials,
    TResult? Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult? Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult? Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult? Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult? Function(OrderFlowType type)? changeOrderFlowType,
    TResult? Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult? Function(String restaurantId)? changeOrder,
    TResult? Function(String restaurantId)? getPreOrder,
    TResult? Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult? Function(String tableNumber)? changeTableNumber,
    TResult? Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult? Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult? Function(String orderId)? earnPoints,
    TResult? Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult? Function(String orderId)? detachCoupon,
    TResult? Function()? resetOrderFlow,
    TResult? Function(StoredOrderModel model)? storeOrder,
    TResult? Function()? clearStoredOrder,
    TResult? Function()? clearStoredCart,
  }) {
    return clearStoredCart?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String restaurantId)? fetchMenus,
    TResult Function(String restaurantId)? fetchSpecials,
    TResult Function(
            String restaurantId,
            DishModel dish,
            int quantity,
            Map<String, List<ExtraItemModel>>? groupedExtras,
            String? notes,
            dynamic withUnique,
            String? uuid)?
        addDishToCart,
    TResult Function(String restaurantId, DishModel dish, String? uuid)?
        removeDishFromCart,
    TResult Function(String restaurantId, SpecialModel special, int quantity,
            Set<SpecialComponent>? selectedComponents, String? notes)?
        addSpecialToCart,
    TResult Function(String restaurantId, SpecialModel special)?
        removeSpecialFromCart,
    TResult Function(OrderFlowType type)? changeOrderFlowType,
    TResult Function(
            String restaurantId, DishModel dish, String specialRequirements)?
        addSpecialRequirementsToDish,
    TResult Function(String restaurantId)? changeOrder,
    TResult Function(String restaurantId)? getPreOrder,
    TResult Function(
            String restaurantId, String stripeAccountId, String? couponId)?
        pay,
    TResult Function(String tableNumber)? changeTableNumber,
    TResult Function(String restaurantId, bool isFavorite)? changeFavorite,
    TResult Function(Set<DietPreferencesModel> dietPreferences)?
        changeDietPreferences,
    TResult Function(String orderId)? earnPoints,
    TResult Function(String restaurantId, String? couponId,
            List<CartItemModel> dishes, List<SpecialCartItemModel> specials)?
        precalculateOrder,
    TResult Function(String orderId)? detachCoupon,
    TResult Function()? resetOrderFlow,
    TResult Function(StoredOrderModel model)? storeOrder,
    TResult Function()? clearStoredOrder,
    TResult Function()? clearStoredCart,
    required TResult orElse(),
  }) {
    if (clearStoredCart != null) {
      return clearStoredCart();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchMenus value) fetchMenus,
    required TResult Function(_FetchSpecials value) fetchSpecials,
    required TResult Function(_AddDishToCart value) addDishToCart,
    required TResult Function(_RemoveDishFromCart value) removeDishFromCart,
    required TResult Function(_AddSpecialToCart value) addSpecialToCart,
    required TResult Function(_RemoveSpecialFromCart value)
        removeSpecialFromCart,
    required TResult Function(_ChangeOrderFlowType value) changeOrderFlowType,
    required TResult Function(_AddSpecialRequirementsToDish value)
        addSpecialRequirementsToDish,
    required TResult Function(_ChangeOrder value) changeOrder,
    required TResult Function(_GetPreOrder value) getPreOrder,
    required TResult Function(_Pay value) pay,
    required TResult Function(_ChangeTableNumber value) changeTableNumber,
    required TResult Function(_ChangeFavorite value) changeFavorite,
    required TResult Function(_ChangeDietPreferences value)
        changeDietPreferences,
    required TResult Function(_EarnPoints value) earnPoints,
    required TResult Function(_PrecalculateOrder value) precalculateOrder,
    required TResult Function(_DetachCoupon value) detachCoupon,
    required TResult Function(_ResetOrderFlow value) resetOrderFlow,
    required TResult Function(_StoreOrder value) storeOrder,
    required TResult Function(_ClearStoredOrder value) clearStoredOrder,
    required TResult Function(_ClearStoredCart value) clearStoredCart,
  }) {
    return clearStoredCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchMenus value)? fetchMenus,
    TResult? Function(_FetchSpecials value)? fetchSpecials,
    TResult? Function(_AddDishToCart value)? addDishToCart,
    TResult? Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult? Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult? Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult? Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult? Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult? Function(_ChangeOrder value)? changeOrder,
    TResult? Function(_GetPreOrder value)? getPreOrder,
    TResult? Function(_Pay value)? pay,
    TResult? Function(_ChangeTableNumber value)? changeTableNumber,
    TResult? Function(_ChangeFavorite value)? changeFavorite,
    TResult? Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult? Function(_EarnPoints value)? earnPoints,
    TResult? Function(_PrecalculateOrder value)? precalculateOrder,
    TResult? Function(_DetachCoupon value)? detachCoupon,
    TResult? Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult? Function(_StoreOrder value)? storeOrder,
    TResult? Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult? Function(_ClearStoredCart value)? clearStoredCart,
  }) {
    return clearStoredCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchMenus value)? fetchMenus,
    TResult Function(_FetchSpecials value)? fetchSpecials,
    TResult Function(_AddDishToCart value)? addDishToCart,
    TResult Function(_RemoveDishFromCart value)? removeDishFromCart,
    TResult Function(_AddSpecialToCart value)? addSpecialToCart,
    TResult Function(_RemoveSpecialFromCart value)? removeSpecialFromCart,
    TResult Function(_ChangeOrderFlowType value)? changeOrderFlowType,
    TResult Function(_AddSpecialRequirementsToDish value)?
        addSpecialRequirementsToDish,
    TResult Function(_ChangeOrder value)? changeOrder,
    TResult Function(_GetPreOrder value)? getPreOrder,
    TResult Function(_Pay value)? pay,
    TResult Function(_ChangeTableNumber value)? changeTableNumber,
    TResult Function(_ChangeFavorite value)? changeFavorite,
    TResult Function(_ChangeDietPreferences value)? changeDietPreferences,
    TResult Function(_EarnPoints value)? earnPoints,
    TResult Function(_PrecalculateOrder value)? precalculateOrder,
    TResult Function(_DetachCoupon value)? detachCoupon,
    TResult Function(_ResetOrderFlow value)? resetOrderFlow,
    TResult Function(_StoreOrder value)? storeOrder,
    TResult Function(_ClearStoredOrder value)? clearStoredOrder,
    TResult Function(_ClearStoredCart value)? clearStoredCart,
    required TResult orElse(),
  }) {
    if (clearStoredCart != null) {
      return clearStoredCart(this);
    }
    return orElse();
  }
}

abstract class _ClearStoredCart implements OrderFlowEvent {
  const factory _ClearStoredCart() = _$ClearStoredCartImpl;
}

/// @nodoc
mixin _$OrderFlowState {
  String get tableNumber => throw _privateConstructorUsedError;
  OrderFlowType get type => throw _privateConstructorUsedError;
  List<CartItemModel> get order => throw _privateConstructorUsedError;
  List<CartItemModel> get cart => throw _privateConstructorUsedError;
  List<SpecialCartItemModel> get specialCart =>
      throw _privateConstructorUsedError;
  List<SpecialCartItemModel> get orderSpecialCart =>
      throw _privateConstructorUsedError;
  List<MainGroupDishesModel> get menus => throw _privateConstructorUsedError;
  OrderMenuStatus get menuStatus => throw _privateConstructorUsedError;
  OrderProceedStatus get orderStatus => throw _privateConstructorUsedError;
  PreOrderStatus get preOrderStatus => throw _privateConstructorUsedError;
  PaymentProceedStatus get paymentStatus => throw _privateConstructorUsedError;
  PrecalculatedOrderStatus get precalculatedOrderStatus =>
      throw _privateConstructorUsedError;
  EarnPointsStatus get earnPointsStatus => throw _privateConstructorUsedError;
  DetachCouponStatus get detachCouponStatus =>
      throw _privateConstructorUsedError;
  OrderSpecialsStatus get specialsStatus => throw _privateConstructorUsedError;
  OrderModel? get preOrder => throw _privateConstructorUsedError;
  (String, bool)? get favorite => throw _privateConstructorUsedError;
  PrecalculatedOrderModel? get precalculatedOrder =>
      throw _privateConstructorUsedError;
  EarnPointsModel? get earnPoints => throw _privateConstructorUsedError;
  Set<DietPreferencesModel> get dietPreferences =>
      throw _privateConstructorUsedError;
  List<SpecialModel> get specials => throw _privateConstructorUsedError;
  PrecalculatedOrderRequest? get precalculatedOrderRequest =>
      throw _privateConstructorUsedError;
  String? get successfulOrderId => throw _privateConstructorUsedError;
  String? get menuError => throw _privateConstructorUsedError;
  String? get orderError => throw _privateConstructorUsedError;
  String? get paymentError => throw _privateConstructorUsedError;
  String? get couponsError => throw _privateConstructorUsedError;
  String? get precalculatedOrderError => throw _privateConstructorUsedError;
  String? get earnPointsError => throw _privateConstructorUsedError;
  String? get detachCouponError => throw _privateConstructorUsedError;
  String? get specialsError => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OrderFlowStateCopyWith<OrderFlowState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderFlowStateCopyWith<$Res> {
  factory $OrderFlowStateCopyWith(
          OrderFlowState value, $Res Function(OrderFlowState) then) =
      _$OrderFlowStateCopyWithImpl<$Res, OrderFlowState>;
  @useResult
  $Res call(
      {String tableNumber,
      OrderFlowType type,
      List<CartItemModel> order,
      List<CartItemModel> cart,
      List<SpecialCartItemModel> specialCart,
      List<SpecialCartItemModel> orderSpecialCart,
      List<MainGroupDishesModel> menus,
      OrderMenuStatus menuStatus,
      OrderProceedStatus orderStatus,
      PreOrderStatus preOrderStatus,
      PaymentProceedStatus paymentStatus,
      PrecalculatedOrderStatus precalculatedOrderStatus,
      EarnPointsStatus earnPointsStatus,
      DetachCouponStatus detachCouponStatus,
      OrderSpecialsStatus specialsStatus,
      OrderModel? preOrder,
      (String, bool)? favorite,
      PrecalculatedOrderModel? precalculatedOrder,
      EarnPointsModel? earnPoints,
      Set<DietPreferencesModel> dietPreferences,
      List<SpecialModel> specials,
      PrecalculatedOrderRequest? precalculatedOrderRequest,
      String? successfulOrderId,
      String? menuError,
      String? orderError,
      String? paymentError,
      String? couponsError,
      String? precalculatedOrderError,
      String? earnPointsError,
      String? detachCouponError,
      String? specialsError});

  $OrderModelCopyWith<$Res>? get preOrder;
  $PrecalculatedOrderModelCopyWith<$Res>? get precalculatedOrder;
  $EarnPointsModelCopyWith<$Res>? get earnPoints;
  $PrecalculatedOrderRequestCopyWith<$Res>? get precalculatedOrderRequest;
}

/// @nodoc
class _$OrderFlowStateCopyWithImpl<$Res, $Val extends OrderFlowState>
    implements $OrderFlowStateCopyWith<$Res> {
  _$OrderFlowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tableNumber = null,
    Object? type = null,
    Object? order = null,
    Object? cart = null,
    Object? specialCart = null,
    Object? orderSpecialCart = null,
    Object? menus = null,
    Object? menuStatus = null,
    Object? orderStatus = null,
    Object? preOrderStatus = null,
    Object? paymentStatus = null,
    Object? precalculatedOrderStatus = null,
    Object? earnPointsStatus = null,
    Object? detachCouponStatus = null,
    Object? specialsStatus = null,
    Object? preOrder = freezed,
    Object? favorite = freezed,
    Object? precalculatedOrder = freezed,
    Object? earnPoints = freezed,
    Object? dietPreferences = null,
    Object? specials = null,
    Object? precalculatedOrderRequest = freezed,
    Object? successfulOrderId = freezed,
    Object? menuError = freezed,
    Object? orderError = freezed,
    Object? paymentError = freezed,
    Object? couponsError = freezed,
    Object? precalculatedOrderError = freezed,
    Object? earnPointsError = freezed,
    Object? detachCouponError = freezed,
    Object? specialsError = freezed,
  }) {
    return _then(_value.copyWith(
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OrderFlowType,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as List<CartItemModel>,
      cart: null == cart
          ? _value.cart
          : cart // ignore: cast_nullable_to_non_nullable
              as List<CartItemModel>,
      specialCart: null == specialCart
          ? _value.specialCart
          : specialCart // ignore: cast_nullable_to_non_nullable
              as List<SpecialCartItemModel>,
      orderSpecialCart: null == orderSpecialCart
          ? _value.orderSpecialCart
          : orderSpecialCart // ignore: cast_nullable_to_non_nullable
              as List<SpecialCartItemModel>,
      menus: null == menus
          ? _value.menus
          : menus // ignore: cast_nullable_to_non_nullable
              as List<MainGroupDishesModel>,
      menuStatus: null == menuStatus
          ? _value.menuStatus
          : menuStatus // ignore: cast_nullable_to_non_nullable
              as OrderMenuStatus,
      orderStatus: null == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderProceedStatus,
      preOrderStatus: null == preOrderStatus
          ? _value.preOrderStatus
          : preOrderStatus // ignore: cast_nullable_to_non_nullable
              as PreOrderStatus,
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentProceedStatus,
      precalculatedOrderStatus: null == precalculatedOrderStatus
          ? _value.precalculatedOrderStatus
          : precalculatedOrderStatus // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderStatus,
      earnPointsStatus: null == earnPointsStatus
          ? _value.earnPointsStatus
          : earnPointsStatus // ignore: cast_nullable_to_non_nullable
              as EarnPointsStatus,
      detachCouponStatus: null == detachCouponStatus
          ? _value.detachCouponStatus
          : detachCouponStatus // ignore: cast_nullable_to_non_nullable
              as DetachCouponStatus,
      specialsStatus: null == specialsStatus
          ? _value.specialsStatus
          : specialsStatus // ignore: cast_nullable_to_non_nullable
              as OrderSpecialsStatus,
      preOrder: freezed == preOrder
          ? _value.preOrder
          : preOrder // ignore: cast_nullable_to_non_nullable
              as OrderModel?,
      favorite: freezed == favorite
          ? _value.favorite
          : favorite // ignore: cast_nullable_to_non_nullable
              as (String, bool)?,
      precalculatedOrder: freezed == precalculatedOrder
          ? _value.precalculatedOrder
          : precalculatedOrder // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderModel?,
      earnPoints: freezed == earnPoints
          ? _value.earnPoints
          : earnPoints // ignore: cast_nullable_to_non_nullable
              as EarnPointsModel?,
      dietPreferences: null == dietPreferences
          ? _value.dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as Set<DietPreferencesModel>,
      specials: null == specials
          ? _value.specials
          : specials // ignore: cast_nullable_to_non_nullable
              as List<SpecialModel>,
      precalculatedOrderRequest: freezed == precalculatedOrderRequest
          ? _value.precalculatedOrderRequest
          : precalculatedOrderRequest // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderRequest?,
      successfulOrderId: freezed == successfulOrderId
          ? _value.successfulOrderId
          : successfulOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      menuError: freezed == menuError
          ? _value.menuError
          : menuError // ignore: cast_nullable_to_non_nullable
              as String?,
      orderError: freezed == orderError
          ? _value.orderError
          : orderError // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentError: freezed == paymentError
          ? _value.paymentError
          : paymentError // ignore: cast_nullable_to_non_nullable
              as String?,
      couponsError: freezed == couponsError
          ? _value.couponsError
          : couponsError // ignore: cast_nullable_to_non_nullable
              as String?,
      precalculatedOrderError: freezed == precalculatedOrderError
          ? _value.precalculatedOrderError
          : precalculatedOrderError // ignore: cast_nullable_to_non_nullable
              as String?,
      earnPointsError: freezed == earnPointsError
          ? _value.earnPointsError
          : earnPointsError // ignore: cast_nullable_to_non_nullable
              as String?,
      detachCouponError: freezed == detachCouponError
          ? _value.detachCouponError
          : detachCouponError // ignore: cast_nullable_to_non_nullable
              as String?,
      specialsError: freezed == specialsError
          ? _value.specialsError
          : specialsError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OrderModelCopyWith<$Res>? get preOrder {
    if (_value.preOrder == null) {
      return null;
    }

    return $OrderModelCopyWith<$Res>(_value.preOrder!, (value) {
      return _then(_value.copyWith(preOrder: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PrecalculatedOrderModelCopyWith<$Res>? get precalculatedOrder {
    if (_value.precalculatedOrder == null) {
      return null;
    }

    return $PrecalculatedOrderModelCopyWith<$Res>(_value.precalculatedOrder!,
        (value) {
      return _then(_value.copyWith(precalculatedOrder: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $EarnPointsModelCopyWith<$Res>? get earnPoints {
    if (_value.earnPoints == null) {
      return null;
    }

    return $EarnPointsModelCopyWith<$Res>(_value.earnPoints!, (value) {
      return _then(_value.copyWith(earnPoints: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PrecalculatedOrderRequestCopyWith<$Res>? get precalculatedOrderRequest {
    if (_value.precalculatedOrderRequest == null) {
      return null;
    }

    return $PrecalculatedOrderRequestCopyWith<$Res>(
        _value.precalculatedOrderRequest!, (value) {
      return _then(_value.copyWith(precalculatedOrderRequest: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderFlowStateImplCopyWith<$Res>
    implements $OrderFlowStateCopyWith<$Res> {
  factory _$$OrderFlowStateImplCopyWith(_$OrderFlowStateImpl value,
          $Res Function(_$OrderFlowStateImpl) then) =
      __$$OrderFlowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String tableNumber,
      OrderFlowType type,
      List<CartItemModel> order,
      List<CartItemModel> cart,
      List<SpecialCartItemModel> specialCart,
      List<SpecialCartItemModel> orderSpecialCart,
      List<MainGroupDishesModel> menus,
      OrderMenuStatus menuStatus,
      OrderProceedStatus orderStatus,
      PreOrderStatus preOrderStatus,
      PaymentProceedStatus paymentStatus,
      PrecalculatedOrderStatus precalculatedOrderStatus,
      EarnPointsStatus earnPointsStatus,
      DetachCouponStatus detachCouponStatus,
      OrderSpecialsStatus specialsStatus,
      OrderModel? preOrder,
      (String, bool)? favorite,
      PrecalculatedOrderModel? precalculatedOrder,
      EarnPointsModel? earnPoints,
      Set<DietPreferencesModel> dietPreferences,
      List<SpecialModel> specials,
      PrecalculatedOrderRequest? precalculatedOrderRequest,
      String? successfulOrderId,
      String? menuError,
      String? orderError,
      String? paymentError,
      String? couponsError,
      String? precalculatedOrderError,
      String? earnPointsError,
      String? detachCouponError,
      String? specialsError});

  @override
  $OrderModelCopyWith<$Res>? get preOrder;
  @override
  $PrecalculatedOrderModelCopyWith<$Res>? get precalculatedOrder;
  @override
  $EarnPointsModelCopyWith<$Res>? get earnPoints;
  @override
  $PrecalculatedOrderRequestCopyWith<$Res>? get precalculatedOrderRequest;
}

/// @nodoc
class __$$OrderFlowStateImplCopyWithImpl<$Res>
    extends _$OrderFlowStateCopyWithImpl<$Res, _$OrderFlowStateImpl>
    implements _$$OrderFlowStateImplCopyWith<$Res> {
  __$$OrderFlowStateImplCopyWithImpl(
      _$OrderFlowStateImpl _value, $Res Function(_$OrderFlowStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tableNumber = null,
    Object? type = null,
    Object? order = null,
    Object? cart = null,
    Object? specialCart = null,
    Object? orderSpecialCart = null,
    Object? menus = null,
    Object? menuStatus = null,
    Object? orderStatus = null,
    Object? preOrderStatus = null,
    Object? paymentStatus = null,
    Object? precalculatedOrderStatus = null,
    Object? earnPointsStatus = null,
    Object? detachCouponStatus = null,
    Object? specialsStatus = null,
    Object? preOrder = freezed,
    Object? favorite = freezed,
    Object? precalculatedOrder = freezed,
    Object? earnPoints = freezed,
    Object? dietPreferences = null,
    Object? specials = null,
    Object? precalculatedOrderRequest = freezed,
    Object? successfulOrderId = freezed,
    Object? menuError = freezed,
    Object? orderError = freezed,
    Object? paymentError = freezed,
    Object? couponsError = freezed,
    Object? precalculatedOrderError = freezed,
    Object? earnPointsError = freezed,
    Object? detachCouponError = freezed,
    Object? specialsError = freezed,
  }) {
    return _then(_$OrderFlowStateImpl(
      tableNumber: null == tableNumber
          ? _value.tableNumber
          : tableNumber // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OrderFlowType,
      order: null == order
          ? _value._order
          : order // ignore: cast_nullable_to_non_nullable
              as List<CartItemModel>,
      cart: null == cart
          ? _value._cart
          : cart // ignore: cast_nullable_to_non_nullable
              as List<CartItemModel>,
      specialCart: null == specialCart
          ? _value._specialCart
          : specialCart // ignore: cast_nullable_to_non_nullable
              as List<SpecialCartItemModel>,
      orderSpecialCart: null == orderSpecialCart
          ? _value._orderSpecialCart
          : orderSpecialCart // ignore: cast_nullable_to_non_nullable
              as List<SpecialCartItemModel>,
      menus: null == menus
          ? _value._menus
          : menus // ignore: cast_nullable_to_non_nullable
              as List<MainGroupDishesModel>,
      menuStatus: null == menuStatus
          ? _value.menuStatus
          : menuStatus // ignore: cast_nullable_to_non_nullable
              as OrderMenuStatus,
      orderStatus: null == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrderProceedStatus,
      preOrderStatus: null == preOrderStatus
          ? _value.preOrderStatus
          : preOrderStatus // ignore: cast_nullable_to_non_nullable
              as PreOrderStatus,
      paymentStatus: null == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentProceedStatus,
      precalculatedOrderStatus: null == precalculatedOrderStatus
          ? _value.precalculatedOrderStatus
          : precalculatedOrderStatus // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderStatus,
      earnPointsStatus: null == earnPointsStatus
          ? _value.earnPointsStatus
          : earnPointsStatus // ignore: cast_nullable_to_non_nullable
              as EarnPointsStatus,
      detachCouponStatus: null == detachCouponStatus
          ? _value.detachCouponStatus
          : detachCouponStatus // ignore: cast_nullable_to_non_nullable
              as DetachCouponStatus,
      specialsStatus: null == specialsStatus
          ? _value.specialsStatus
          : specialsStatus // ignore: cast_nullable_to_non_nullable
              as OrderSpecialsStatus,
      preOrder: freezed == preOrder
          ? _value.preOrder
          : preOrder // ignore: cast_nullable_to_non_nullable
              as OrderModel?,
      favorite: freezed == favorite
          ? _value.favorite
          : favorite // ignore: cast_nullable_to_non_nullable
              as (String, bool)?,
      precalculatedOrder: freezed == precalculatedOrder
          ? _value.precalculatedOrder
          : precalculatedOrder // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderModel?,
      earnPoints: freezed == earnPoints
          ? _value.earnPoints
          : earnPoints // ignore: cast_nullable_to_non_nullable
              as EarnPointsModel?,
      dietPreferences: null == dietPreferences
          ? _value._dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as Set<DietPreferencesModel>,
      specials: null == specials
          ? _value._specials
          : specials // ignore: cast_nullable_to_non_nullable
              as List<SpecialModel>,
      precalculatedOrderRequest: freezed == precalculatedOrderRequest
          ? _value.precalculatedOrderRequest
          : precalculatedOrderRequest // ignore: cast_nullable_to_non_nullable
              as PrecalculatedOrderRequest?,
      successfulOrderId: freezed == successfulOrderId
          ? _value.successfulOrderId
          : successfulOrderId // ignore: cast_nullable_to_non_nullable
              as String?,
      menuError: freezed == menuError
          ? _value.menuError
          : menuError // ignore: cast_nullable_to_non_nullable
              as String?,
      orderError: freezed == orderError
          ? _value.orderError
          : orderError // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentError: freezed == paymentError
          ? _value.paymentError
          : paymentError // ignore: cast_nullable_to_non_nullable
              as String?,
      couponsError: freezed == couponsError
          ? _value.couponsError
          : couponsError // ignore: cast_nullable_to_non_nullable
              as String?,
      precalculatedOrderError: freezed == precalculatedOrderError
          ? _value.precalculatedOrderError
          : precalculatedOrderError // ignore: cast_nullable_to_non_nullable
              as String?,
      earnPointsError: freezed == earnPointsError
          ? _value.earnPointsError
          : earnPointsError // ignore: cast_nullable_to_non_nullable
              as String?,
      detachCouponError: freezed == detachCouponError
          ? _value.detachCouponError
          : detachCouponError // ignore: cast_nullable_to_non_nullable
              as String?,
      specialsError: freezed == specialsError
          ? _value.specialsError
          : specialsError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$OrderFlowStateImpl implements _OrderFlowState {
  const _$OrderFlowStateImpl(
      {this.tableNumber = '',
      this.type = OrderFlowType.order,
      final List<CartItemModel> order = const [],
      final List<CartItemModel> cart = const [],
      final List<SpecialCartItemModel> specialCart = const [],
      final List<SpecialCartItemModel> orderSpecialCart = const [],
      final List<MainGroupDishesModel> menus = const [],
      this.menuStatus = OrderMenuStatus.idle,
      this.orderStatus = OrderProceedStatus.idle,
      this.preOrderStatus = PreOrderStatus.idle,
      this.paymentStatus = PaymentProceedStatus.idle,
      this.precalculatedOrderStatus = PrecalculatedOrderStatus.idle,
      this.earnPointsStatus = EarnPointsStatus.idle,
      this.detachCouponStatus = DetachCouponStatus.idle,
      this.specialsStatus = OrderSpecialsStatus.idle,
      required this.preOrder,
      required this.favorite,
      this.precalculatedOrder,
      this.earnPoints,
      final Set<DietPreferencesModel> dietPreferences = const {},
      final List<SpecialModel> specials = const [],
      this.precalculatedOrderRequest,
      this.successfulOrderId,
      this.menuError,
      this.orderError,
      this.paymentError,
      this.couponsError,
      this.precalculatedOrderError,
      this.earnPointsError,
      this.detachCouponError,
      this.specialsError})
      : _order = order,
        _cart = cart,
        _specialCart = specialCart,
        _orderSpecialCart = orderSpecialCart,
        _menus = menus,
        _dietPreferences = dietPreferences,
        _specials = specials;

  @override
  @JsonKey()
  final String tableNumber;
  @override
  @JsonKey()
  final OrderFlowType type;
  final List<CartItemModel> _order;
  @override
  @JsonKey()
  List<CartItemModel> get order {
    if (_order is EqualUnmodifiableListView) return _order;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_order);
  }

  final List<CartItemModel> _cart;
  @override
  @JsonKey()
  List<CartItemModel> get cart {
    if (_cart is EqualUnmodifiableListView) return _cart;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cart);
  }

  final List<SpecialCartItemModel> _specialCart;
  @override
  @JsonKey()
  List<SpecialCartItemModel> get specialCart {
    if (_specialCart is EqualUnmodifiableListView) return _specialCart;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specialCart);
  }

  final List<SpecialCartItemModel> _orderSpecialCart;
  @override
  @JsonKey()
  List<SpecialCartItemModel> get orderSpecialCart {
    if (_orderSpecialCart is EqualUnmodifiableListView)
      return _orderSpecialCart;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orderSpecialCart);
  }

  final List<MainGroupDishesModel> _menus;
  @override
  @JsonKey()
  List<MainGroupDishesModel> get menus {
    if (_menus is EqualUnmodifiableListView) return _menus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_menus);
  }

  @override
  @JsonKey()
  final OrderMenuStatus menuStatus;
  @override
  @JsonKey()
  final OrderProceedStatus orderStatus;
  @override
  @JsonKey()
  final PreOrderStatus preOrderStatus;
  @override
  @JsonKey()
  final PaymentProceedStatus paymentStatus;
  @override
  @JsonKey()
  final PrecalculatedOrderStatus precalculatedOrderStatus;
  @override
  @JsonKey()
  final EarnPointsStatus earnPointsStatus;
  @override
  @JsonKey()
  final DetachCouponStatus detachCouponStatus;
  @override
  @JsonKey()
  final OrderSpecialsStatus specialsStatus;
  @override
  final OrderModel? preOrder;
  @override
  final (String, bool)? favorite;
  @override
  final PrecalculatedOrderModel? precalculatedOrder;
  @override
  final EarnPointsModel? earnPoints;
  final Set<DietPreferencesModel> _dietPreferences;
  @override
  @JsonKey()
  Set<DietPreferencesModel> get dietPreferences {
    if (_dietPreferences is EqualUnmodifiableSetView) return _dietPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_dietPreferences);
  }

  final List<SpecialModel> _specials;
  @override
  @JsonKey()
  List<SpecialModel> get specials {
    if (_specials is EqualUnmodifiableListView) return _specials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_specials);
  }

  @override
  final PrecalculatedOrderRequest? precalculatedOrderRequest;
  @override
  final String? successfulOrderId;
  @override
  final String? menuError;
  @override
  final String? orderError;
  @override
  final String? paymentError;
  @override
  final String? couponsError;
  @override
  final String? precalculatedOrderError;
  @override
  final String? earnPointsError;
  @override
  final String? detachCouponError;
  @override
  final String? specialsError;

  @override
  String toString() {
    return 'OrderFlowState(tableNumber: $tableNumber, type: $type, order: $order, cart: $cart, specialCart: $specialCart, orderSpecialCart: $orderSpecialCart, menus: $menus, menuStatus: $menuStatus, orderStatus: $orderStatus, preOrderStatus: $preOrderStatus, paymentStatus: $paymentStatus, precalculatedOrderStatus: $precalculatedOrderStatus, earnPointsStatus: $earnPointsStatus, detachCouponStatus: $detachCouponStatus, specialsStatus: $specialsStatus, preOrder: $preOrder, favorite: $favorite, precalculatedOrder: $precalculatedOrder, earnPoints: $earnPoints, dietPreferences: $dietPreferences, specials: $specials, precalculatedOrderRequest: $precalculatedOrderRequest, successfulOrderId: $successfulOrderId, menuError: $menuError, orderError: $orderError, paymentError: $paymentError, couponsError: $couponsError, precalculatedOrderError: $precalculatedOrderError, earnPointsError: $earnPointsError, detachCouponError: $detachCouponError, specialsError: $specialsError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderFlowStateImpl &&
            (identical(other.tableNumber, tableNumber) ||
                other.tableNumber == tableNumber) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._order, _order) &&
            const DeepCollectionEquality().equals(other._cart, _cart) &&
            const DeepCollectionEquality()
                .equals(other._specialCart, _specialCart) &&
            const DeepCollectionEquality()
                .equals(other._orderSpecialCart, _orderSpecialCart) &&
            const DeepCollectionEquality().equals(other._menus, _menus) &&
            (identical(other.menuStatus, menuStatus) ||
                other.menuStatus == menuStatus) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.preOrderStatus, preOrderStatus) ||
                other.preOrderStatus == preOrderStatus) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(
                    other.precalculatedOrderStatus, precalculatedOrderStatus) ||
                other.precalculatedOrderStatus == precalculatedOrderStatus) &&
            (identical(other.earnPointsStatus, earnPointsStatus) ||
                other.earnPointsStatus == earnPointsStatus) &&
            (identical(other.detachCouponStatus, detachCouponStatus) ||
                other.detachCouponStatus == detachCouponStatus) &&
            (identical(other.specialsStatus, specialsStatus) ||
                other.specialsStatus == specialsStatus) &&
            (identical(other.preOrder, preOrder) ||
                other.preOrder == preOrder) &&
            (identical(other.favorite, favorite) ||
                other.favorite == favorite) &&
            (identical(other.precalculatedOrder, precalculatedOrder) ||
                other.precalculatedOrder == precalculatedOrder) &&
            (identical(other.earnPoints, earnPoints) ||
                other.earnPoints == earnPoints) &&
            const DeepCollectionEquality()
                .equals(other._dietPreferences, _dietPreferences) &&
            const DeepCollectionEquality().equals(other._specials, _specials) &&
            (identical(other.precalculatedOrderRequest,
                    precalculatedOrderRequest) ||
                other.precalculatedOrderRequest == precalculatedOrderRequest) &&
            (identical(other.successfulOrderId, successfulOrderId) ||
                other.successfulOrderId == successfulOrderId) &&
            (identical(other.menuError, menuError) ||
                other.menuError == menuError) &&
            (identical(other.orderError, orderError) ||
                other.orderError == orderError) &&
            (identical(other.paymentError, paymentError) ||
                other.paymentError == paymentError) &&
            (identical(other.couponsError, couponsError) ||
                other.couponsError == couponsError) &&
            (identical(
                    other.precalculatedOrderError, precalculatedOrderError) ||
                other.precalculatedOrderError == precalculatedOrderError) &&
            (identical(other.earnPointsError, earnPointsError) ||
                other.earnPointsError == earnPointsError) &&
            (identical(other.detachCouponError, detachCouponError) ||
                other.detachCouponError == detachCouponError) &&
            (identical(other.specialsError, specialsError) ||
                other.specialsError == specialsError));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        tableNumber,
        type,
        const DeepCollectionEquality().hash(_order),
        const DeepCollectionEquality().hash(_cart),
        const DeepCollectionEquality().hash(_specialCart),
        const DeepCollectionEquality().hash(_orderSpecialCart),
        const DeepCollectionEquality().hash(_menus),
        menuStatus,
        orderStatus,
        preOrderStatus,
        paymentStatus,
        precalculatedOrderStatus,
        earnPointsStatus,
        detachCouponStatus,
        specialsStatus,
        preOrder,
        favorite,
        precalculatedOrder,
        earnPoints,
        const DeepCollectionEquality().hash(_dietPreferences),
        const DeepCollectionEquality().hash(_specials),
        precalculatedOrderRequest,
        successfulOrderId,
        menuError,
        orderError,
        paymentError,
        couponsError,
        precalculatedOrderError,
        earnPointsError,
        detachCouponError,
        specialsError
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderFlowStateImplCopyWith<_$OrderFlowStateImpl> get copyWith =>
      __$$OrderFlowStateImplCopyWithImpl<_$OrderFlowStateImpl>(
          this, _$identity);
}

abstract class _OrderFlowState implements OrderFlowState {
  const factory _OrderFlowState(
      {final String tableNumber,
      final OrderFlowType type,
      final List<CartItemModel> order,
      final List<CartItemModel> cart,
      final List<SpecialCartItemModel> specialCart,
      final List<SpecialCartItemModel> orderSpecialCart,
      final List<MainGroupDishesModel> menus,
      final OrderMenuStatus menuStatus,
      final OrderProceedStatus orderStatus,
      final PreOrderStatus preOrderStatus,
      final PaymentProceedStatus paymentStatus,
      final PrecalculatedOrderStatus precalculatedOrderStatus,
      final EarnPointsStatus earnPointsStatus,
      final DetachCouponStatus detachCouponStatus,
      final OrderSpecialsStatus specialsStatus,
      required final OrderModel? preOrder,
      required final (String, bool)? favorite,
      final PrecalculatedOrderModel? precalculatedOrder,
      final EarnPointsModel? earnPoints,
      final Set<DietPreferencesModel> dietPreferences,
      final List<SpecialModel> specials,
      final PrecalculatedOrderRequest? precalculatedOrderRequest,
      final String? successfulOrderId,
      final String? menuError,
      final String? orderError,
      final String? paymentError,
      final String? couponsError,
      final String? precalculatedOrderError,
      final String? earnPointsError,
      final String? detachCouponError,
      final String? specialsError}) = _$OrderFlowStateImpl;

  @override
  String get tableNumber;
  @override
  OrderFlowType get type;
  @override
  List<CartItemModel> get order;
  @override
  List<CartItemModel> get cart;
  @override
  List<SpecialCartItemModel> get specialCart;
  @override
  List<SpecialCartItemModel> get orderSpecialCart;
  @override
  List<MainGroupDishesModel> get menus;
  @override
  OrderMenuStatus get menuStatus;
  @override
  OrderProceedStatus get orderStatus;
  @override
  PreOrderStatus get preOrderStatus;
  @override
  PaymentProceedStatus get paymentStatus;
  @override
  PrecalculatedOrderStatus get precalculatedOrderStatus;
  @override
  EarnPointsStatus get earnPointsStatus;
  @override
  DetachCouponStatus get detachCouponStatus;
  @override
  OrderSpecialsStatus get specialsStatus;
  @override
  OrderModel? get preOrder;
  @override
  (String, bool)? get favorite;
  @override
  PrecalculatedOrderModel? get precalculatedOrder;
  @override
  EarnPointsModel? get earnPoints;
  @override
  Set<DietPreferencesModel> get dietPreferences;
  @override
  List<SpecialModel> get specials;
  @override
  PrecalculatedOrderRequest? get precalculatedOrderRequest;
  @override
  String? get successfulOrderId;
  @override
  String? get menuError;
  @override
  String? get orderError;
  @override
  String? get paymentError;
  @override
  String? get couponsError;
  @override
  String? get precalculatedOrderError;
  @override
  String? get earnPointsError;
  @override
  String? get detachCouponError;
  @override
  String? get specialsError;
  @override
  @JsonKey(ignore: true)
  _$$OrderFlowStateImplCopyWith<_$OrderFlowStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
