import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/constants/order_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_selection_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/orders/order_detail_model.dart';
import 'package:mutualz/src/domain/models/orders/order_status.dart';
import 'package:mutualz/src/domain/models/orders/precalculated_order_model.dart';
import 'package:mutualz/src/domain/usecases/orders/create_order_usecase.dart';
import 'package:uuid/uuid.dart';

import '../../models/requests/order/precalculated_item_special.dart';

part 'order_flow_bloc.freezed.dart';
part 'order_flow_event.dart';
part 'order_flow_state.dart';

class OrderFlowBloc extends Bloc<OrderFlowEvent, OrderFlowState> {
  final FetchMenuUseCase _fetchMenuUseCase;
  final ChangeOrderUseCase _changeOrderUseCase;
  final FetchOrderUseCase _getOrderUseCase;
  final PaymentService _paymentService;
  final AddToFavoriteUseCase _addToFavoriteUseCase;
  final RemoveFromFavoriteUseCase _removeFromFavoriteUseCase;
  final FavoriteCheckerService _favoriteCheckerService;
  final EarnPointsUseCase _earnPointsUseCase;
  final PrecalculateOrderUseCase _precalculateOrderUseCase;
  final AttachCouponUseCase _attachCouponUseCase;
  final DetachCouponUseCase _detachCouponUseCase;
  final FetchSpecialsUseCase _fetchSpecialsUseCase;
  final CreateStoredOrderUseCase _createStoredOrderUseCase;
  final ClearStoredOrderUseCase _clearStoredOrderUseCase;
  final CreateStoredCartUseCase _createStoredCartUseCase;
  final ClearStoredCartUseCase _clearStoredCartUseCase;
  final ReadStoredCartUseCase _readStoredCartUseCase;
  final MLogger _logger;

  OrderFlowBloc(
    this._fetchMenuUseCase,
    this._changeOrderUseCase,
    this._getOrderUseCase,
    this._paymentService,
    this._addToFavoriteUseCase,
    this._removeFromFavoriteUseCase,
    this._favoriteCheckerService,
    this._earnPointsUseCase,
    this._precalculateOrderUseCase,
    this._attachCouponUseCase,
    this._detachCouponUseCase,
    this._fetchSpecialsUseCase,
    this._createStoredOrderUseCase,
    this._clearStoredOrderUseCase,
    this._createStoredCartUseCase,
    this._clearStoredCartUseCase,
    this._readStoredCartUseCase,
    this._logger,
  ) : super(const OrderFlowState(preOrder: null, favorite: null)) {
    on<_FetchMenus>(_fetchMenus);
    on<_AddDishToCart>(_addDishToCart);
    on<_RemoveDishFromCart>(_removeDishFromCart);
    on<_AddSpecialToCart>(_addSpecialToCart);
    on<_RemoveSpecialFromCart>(_removeSpecialFromCart);
    on<_ChangeOrderFlowType>(_changeOrderFlowType);
    on<_AddSpecialRequirementsToDish>(_addSpecialRequirementsToDish);
    on<_ChangeOrder>(_changeOrder);
    on<_GetPreOrder>(_getPreOrder);
    on<_Pay>(_pay);
    on<_ChangeTableNumber>(_changeTableNumber);
    on<_ChangeFavorite>(_changeFavorite);
    on<_ChangeDietPreferences>(_changeDietPreferences);
    on<_EarnPoints>(_earnPoints);
    on<_PrecalculateOrder>(_precalculateOrder);
    on<_DetachCoupon>(_detachCoupon);
    on<_FetchSpecials>(_fetchSpecials);
    on<_ResetOrderFlow>(_resetOrderFlow);
    on<_StoreOrder>(_storeOrder);
    on<_ClearStoredOrder>(_clearStoredOrder);
    on<_ClearStoredCart>(_clearStoredCart);
  }

  Future<void> _fetchSpecials(_FetchSpecials event, Emitter<OrderFlowState> emit) async {
    try {
      emit(state.copyWith(specialsStatus: OrderSpecialsStatus.loading));

      final specials = await _fetchSpecialsUseCase(event.restaurantId);

      emit(state.copyWith(specialsStatus: OrderSpecialsStatus.success, specials: specials));
    } on DioException catch (e, s) {
      _logger.error('Problem with fetching specials ${e.toString()} $s');
      emit(state.copyWith(specialsStatus: OrderSpecialsStatus.error, specialsError: e.message));
    } catch (e, s) {
      _logger.error('Problem with fetching specials ${e.toString()} $s');
      emit(state.copyWith(specialsStatus: OrderSpecialsStatus.error, specialsError: e.toString()));
    }
  }

  Future<void> _detachCoupon(_DetachCoupon event, Emitter<OrderFlowState> emit) async {
    try {
      emit(state.copyWith(detachCouponStatus: DetachCouponStatus.loading));
      await _detachCouponUseCase(orderId: event.orderId);
      emit(state.copyWith(
        detachCouponStatus: DetachCouponStatus.success,
        preOrder: state.preOrder?.copyWith(couponId: null),
      ));
    } on DioException catch (e, s) {
      _logger.error('Problem with detaching coupon ${e.toString()} $s');
      emit(state.copyWith(detachCouponStatus: DetachCouponStatus.error, detachCouponError: e.message));
    } catch (e, s) {
      _logger.error('Problem with detaching coupon ${e.toString()} $s');
      emit(state.copyWith(detachCouponStatus: DetachCouponStatus.error, detachCouponError: e.toString()));
    }
    emit(state.copyWith(detachCouponStatus: DetachCouponStatus.idle));
  }

  /// Using for converting cart to order details
  PrecalculatedOrderDetails convertCartToOrderDetails({
    required List<CartItemModel>? cartDishes,
    required List<SpecialCartItemModel>? cartSpecials,
  }) {
    return PrecalculatedOrderDetails(
      items: [
        if (cartDishes != null)
          ...cartDishes.map((e) => PrecalculatedItemDish(
                type: OrderType.dish,
                dish: PrecalculatedDish(
                  id: e.dish.id,
                  extras: e.selectedExtras.entries
                      .where((entry) => entry.value.isNotEmpty)
                      .map((entry) => ExtraSelectionModel(
                            id: entry.key,
                            items: entry.value.map((item) => item.id).toList(),
                          ))
                      .toList(),
                ),
                quantity: e.quantity,
              )),
        if (cartSpecials != null)
          ...cartSpecials.map((e) => PrecalculatedItemSpecial(
                type: OrderType.special,
                special: PrecalculatedSpecial(
                  id: e.special.id,
                  components: e.components
                      .map((e) => PrecalculatedSpecialComponent(
                            id: e.id,
                            dishes: e.dishes
                                .map((dish) => PrecalculatedDish(
                                      id: dish.id,
                                      extras: dish.extras
                                          .where((group) => group.items.isNotEmpty)
                                          .map((group) => ExtraSelectionModel(
                                                id: group.id,
                                                items: group.items.map((item) => item.id).toList(),
                                              ))
                                          .toList(),
                                    ))
                                .toList(),
                          ))
                      .toList(),
                ),
                quantity: e.quantity,
              )),
      ],
    );
  }

  Future<void> _precalculateOrder(_PrecalculateOrder event, Emitter<OrderFlowState> emit) async {
    try {
      emit(state.copyWith(precalculatedOrderStatus: PrecalculatedOrderStatus.loading));
      final request = PrecalculatedOrderRequest(restaurantId: event.restaurantId, couponId: event.couponId, details: [
        convertCartToOrderDetails(
          cartDishes: event.dishes,
          cartSpecials: event.specials,
        )

        /// Todo: Remove deprecated code if everything works
        // PrecalculatedOrderDetails(
        //   items: [
        //     ...event.dishes.map((e) => PrecalculatedItemDish(
        //           type: OrderType.dish,
        //           dish: PrecalculatedDish(
        //             id: e.dish.id,
        //             extras: e.extras.map((e) => e.id).toList(),
        //           ),
        //           quantity: e.quantity,
        //         )),
        //     ...event.specials.map((e) => PrecalculatedItemSpecial(
        //           type: OrderType.special,
        //           special: PrecalculatedSpecial(
        //             id: e.special.id,
        //             components: e.components
        //                 .map((e) => PrecalculatedSpecialComponent(
        //                       id: e.id,
        //                       dishes: e.dishes
        //                           .map((e) => PrecalculatedDish(
        //                                 id: e.id,
        //                                 extras: e.extras
        //                                     .map((e) => e.id)
        //                                     .toList(),
        //                               ))
        //                           .toList(),
        //                     ))
        //                 .toList(),
        //           ),
        //           quantity: e.quantity,
        //         )),
        //   ],
        // ),
      ]);

      final response = await _precalculateOrderUseCase(
        restaurantId: event.restaurantId,
        couponId: event.couponId,
        dishes: event.dishes,
        specials: event.specials,
      );
      emit(state.copyWith(
          precalculatedOrder: response.data,
          precalculatedOrderStatus: PrecalculatedOrderStatus.success,
          precalculatedOrderRequest: request));
    } on DioException catch (e, s) {
      _logger.error('Problem with precalculating order ${e.toString()} $s');
      emit(state.copyWith(precalculatedOrderStatus: PrecalculatedOrderStatus.error, precalculatedOrderError: e.message));
    } catch (e, s) {
      _logger.error('Problem with precalculating order ${e.toString()} $s');
      emit(state.copyWith(precalculatedOrderStatus: PrecalculatedOrderStatus.error, precalculatedOrderError: e.toString()));
    }
  }

  Future<void> _earnPoints(_EarnPoints event, Emitter<OrderFlowState> emit) async {
    try {
      emit(state.copyWith(earnPointsStatus: EarnPointsStatus.loading));
      final earnPointsModel = await _earnPointsUseCase(orderId: event.orderId);

      emit(state.copyWith(
        successfulOrderId: null,
        earnPointsStatus: EarnPointsStatus.success,
        earnPoints: earnPointsModel,
      ));
    } on DioException catch (e, s) {
      _logger.error('Problem with earning points ${e.toString()} $s');
      emit(state.copyWith(earnPointsStatus: EarnPointsStatus.error, earnPointsError: e.message));
    } catch (e, s) {
      _logger.error('Problem with earning points ${e.toString()} $s');
      emit(state.copyWith(earnPointsStatus: EarnPointsStatus.error, earnPointsError: e.toString()));
    }
  }

  Future<void> _changeDietPreferences(_ChangeDietPreferences event, Emitter<OrderFlowState> emit) async {
    emit(state.copyWith(dietPreferences: event.dietPreferences.toSet()));
  }

  Future<void> _changeTableNumber(_ChangeTableNumber event, Emitter<OrderFlowState> emit) async {
    emit(state.copyWith(tableNumber: event.tableNumber));
  }

  Future<void> _addSpecialRequirementsToDish(_AddSpecialRequirementsToDish event, Emitter<OrderFlowState> emit) async {
    final newCart = List<CartItemModel>.from(state.cart)
        .map((e) => e.dish.id == event.dish.id ? e.copyWith(notes: event.specialRequirements) : e)
        .toList();

    emit(state.copyWith(cart: newCart));

    await _updateStoredCart(
      restaurantId: event.restaurantId,
      cart: newCart,
    );
  }

  Future<void> _changeOrderFlowType(_ChangeOrderFlowType event, Emitter<OrderFlowState> emit) async {
    emit(state.copyWith(type: event.type));
  }

  bool checkIsDishInCart(DishModel dish) {
    return state.cart.any((cartItem) => cartItem.dish.id == dish.id);
  }

  bool checkIsSpecialInCart(SpecialModel special) {
    return state.specialCart.any((cartItem) => cartItem.special.id == special.id);
  }

  int getQuantityOfDishInCart(DishModel dish) {
    final cartItem = state.cart.firstWhere((cartItem) => cartItem.dish.id == dish.id, orElse: () => CartItemModel(quantity: 0, dish: dish));

    return cartItem.quantity;
  }

  Future<void> _removeDishFromCart(_RemoveDishFromCart event, Emitter<OrderFlowState> emit) async {
    final newCart = List<CartItemModel>.from(state.cart);

    if (event.uuid != null) {
      newCart.removeWhere((cartItem) => cartItem.uuid == event.uuid);
    } else {
      newCart.removeWhere((cartItem) => cartItem.dish.id == event.dish.id);
    }

    final updatedCart = List<CartItemModel>.from(newCart);

    await _updateStoredCart(
      restaurantId: event.restaurantId,
      cart: updatedCart,
    );

    emit(state.copyWith(cart: newCart));
  }

  Future<void> _addDishToCart(_AddDishToCart event, Emitter<OrderFlowState> emit) async {
    final cartItem = CartItemModel(
      quantity: event.quantity.toInt(),
      dish: event.dish,
      selectedExtras: event.groupedExtras ?? {},
      notes: event.notes,
    );

    final newCart = List<CartItemModel>.from(state.cart);

    if (event.withUnique) {
      final updatedCartItem = cartItem.copyWith(uuid: const Uuid().v4());
      newCart.add(updatedCartItem);
    } else if (!checkIsDishInCart(event.dish) && cartItem.uuid == null) {
      newCart.add(cartItem);
    } else if (checkIsDishInCart(event.dish)) {
      final index = event.uuid == null
          ? newCart.indexWhere((cartItem) => cartItem.dish.id == event.dish.id)
          : newCart.indexWhere((cartItem) => cartItem.dish.id == event.dish.id && cartItem.uuid == event.uuid);

      final updatedCartItem = cartItem.copyWith(
        selectedExtras: event.groupedExtras ?? newCart[index].selectedExtras,
        notes: event.notes ?? newCart[index].notes,
      );
      newCart[index] = updatedCartItem;
    }

    final updatedCart = List<CartItemModel>.from(newCart);

    await _updateStoredCart(
      restaurantId: event.restaurantId,
      cart: updatedCart,
    );

    emit(state.copyWith(cart: List<CartItemModel>.from(newCart)));
  }

  Future<void> _addSpecialToCart(_AddSpecialToCart event, Emitter<OrderFlowState> emit) async {
    final cartItem = SpecialCartItemModel(
      quantity: event.quantity,
      special: event.special,
      components: event.selectedComponents?.toList() ?? [],
      notes: event.notes,
    );

    final newCart = List<SpecialCartItemModel>.from(state.specialCart);

    if (checkIsSpecialInCart(event.special)) {
      final index = newCart.indexWhere((cartItem) => cartItem.special.id == event.special.id);
      final updatedCartItem = cartItem.copyWith(
        components: event.selectedComponents?.toList() ?? cartItem.components,
        notes: event.notes ?? cartItem.notes,
      );
      newCart[index] = updatedCartItem;
    } else {
      newCart.add(cartItem);
    }

    final updatedSpecialCart = List<SpecialCartItemModel>.from(newCart);

    await _updateStoredCart(
      restaurantId: event.restaurantId,
      specialCart: updatedSpecialCart,
    );

    emit(state.copyWith(specialCart: List<SpecialCartItemModel>.from(newCart)));
  }

  Future<void> _removeSpecialFromCart(_RemoveSpecialFromCart event, Emitter<OrderFlowState> emit) async {
    final newCart = List<SpecialCartItemModel>.from(state.specialCart);
    newCart.removeWhere((cartItem) => cartItem.special.id == event.special.id);

    final updatedSpecialCart = List<SpecialCartItemModel>.from(newCart);

    await _updateStoredCart(
      restaurantId: event.restaurantId,
      specialCart: updatedSpecialCart,
    );

    emit(state.copyWith(specialCart: newCart));
  }

  /// This function updates the stored cart in the local storage.
  Future<void> _updateStoredCart({
    required String restaurantId,
    List<SpecialCartItemModel>? specialCart,
    List<CartItemModel>? cart,
  }) async {
    final storedCart = await _readStoredCartUseCase();

    // If the stored cart is null, we can create a new one
    if (storedCart == null) {
      // Create a new StoredMixedCartItemModel for the restaurant
      final newMixedCartItem = StoredMixedCartItemModel(
        restaurantId: restaurantId,
        date: DateTime.now(),
        cart: cart ?? [],
        specialCart: specialCart ?? [],
      );

      // Create a new StoredCartModel with one item
      final newStoredCart = StoredCartModel(items: [newMixedCartItem]);

      // Save the new cart
      await _createStoredCartUseCase(newStoredCart);
    } else {
      final storedMixedCartItemModel = storedCart.getItemById(restaurantId);

      if (storedMixedCartItemModel == null) {
        // If there's no data for this restaurant, add a new item
        final newMixedCartItem = StoredMixedCartItemModel(
          restaurantId: restaurantId,
          date: DateTime.now(),
          cart: cart ?? [],
          specialCart: specialCart ?? [],
        );

        // Add the new item to the list and create an updated model
        final updatedItems = [...storedCart.items, newMixedCartItem];
        final updatedStoredCart = storedCart.copyWith(items: updatedItems);

        // Save the updated cart
        await _createStoredCartUseCase(updatedStoredCart);
      } else {
        // If data for the restaurant already exists, update it
        final updatedStoredMixedCartItemModel = storedMixedCartItemModel.copyWith(
          cart: cart ?? storedMixedCartItemModel.cart,
          specialCart: specialCart ?? storedMixedCartItemModel.specialCart,
        );

        // Create a new list, replacing the old item with the updated one
        final updatedItems = storedCart.items.map((item) {
          return item.restaurantId == restaurantId ? updatedStoredMixedCartItemModel : item;
        }).toList();

        // Create an updated cart model
        final updatedStoredCart = storedCart.copyWith(items: updatedItems);

        // Save the updated cart
        await _createStoredCartUseCase(updatedStoredCart);
      }
    }
  }

  Future<void> _fetchMenus(_FetchMenus event, Emitter<OrderFlowState> emit) async {
    try {
      emit(state.copyWith(menuStatus: OrderMenuStatus.loading));

      List<MainGroupDishesModel> menus = await _fetchMenuUseCase(event.restaurantId);

      emit(state.copyWith(
        menuStatus: OrderMenuStatus.success,
        menus: menus,
      ));
    } on DioException catch (e, s) {
      _logger.error('Problem with fetching menu ${e.toString()} $s');
      emit(state.copyWith(menuStatus: OrderMenuStatus.error, menuError: e.message));
    } catch (e, s) {
      _logger.error('Problem with fetching menu ${e.toString()} $s');
      emit(state.copyWith(menuStatus: OrderMenuStatus.error, menuError: e.toString()));
    }
  }

  Future<void> _changeOrder(_ChangeOrder event, Emitter<OrderFlowState> emit) async {
    try {
      final storedCart = await _readStoredCartUseCase();
      if (storedCart != null) {
        await _clearStoredCartUseCase();
      }
    } catch (e, s) {
      _logger.error('Problem with clearing stored cart ${e.toString()} $s');
    }

    try {
      emit(state.copyWith(orderStatus: OrderProceedStatus.loading));

      final type = state.preOrder != null ? ChangeOrderType.update : ChangeOrderType.add;
      final orderId = state.preOrder?.id;

      final result = await _changeOrderUseCase(
        restaurantId: event.restaurantId,
        tableNumber: state.tableNumber,
        orderId: orderId,
        cart: state.cart,
        specialCart: state.specialCart,
        type: type,
      );

      emit(state.copyWith(
        orderStatus: OrderProceedStatus.success,
        preOrder: result.data,
      ));
    } on DioException catch (e, s) {
      _logger.error('Problem with creating order ${e.toString()} $s');
      emit(state.copyWith(
        orderStatus: OrderProceedStatus.error,
        orderError: e.message,
      ));
    } catch (e, s) {
      _logger.error('Problem with creating order ${e.toString()} $s');
      emit(state.copyWith(
        orderStatus: OrderProceedStatus.error,
        orderError: e.toString(),
      ));
    }
    emit(state.copyWith(orderStatus: OrderProceedStatus.idle));
  }

  Future<void> _getPreOrder(_GetPreOrder event, Emitter<OrderFlowState> emit) async {
    StoredCartModel? storedCart;

    /// Check if the stored cart is empty
    try {
      storedCart = await _readStoredCartUseCase();
    } catch (e, s) {
      _logger.error('Problem with reading stored cart ${e.toString()} $s');
    }

    try {
      final preOrderResponse = await _getOrderUseCase(restaurantId: event.restaurantId, paymentStatus: PaymentStatus.unpaid);

      final OrderModel? currentPreOrder = _getCurrentPreOrder(preOrderResponse);

      if (currentPreOrder != null) {
        emit(state.copyWith(
          order: currentPreOrder.details
              .map((e) => e.items
                  .where((e) => e.type == OrderType.dish && e.dish != null)
                  .map((e) => CartItemModel(
                        id: e.id,
                        dish: e.dish!,
                        quantity: e.quantity,
                        notes: e.notes,
                        selectedExtras: _extractSelectedExtras(e.dish!.extras),
                      ))
                  .toList())
              .expand((element) => element)
              .toList(),
          orderSpecialCart: currentPreOrder.details
              .map((e) => e.items
                  .where((e) => e.type == OrderType.special && e.special != null)
                  .map((e) => SpecialCartItemModel(
                        id: e.id,
                        special: e.special!,
                        quantity: e.quantity,
                        components: e.special?.components != null
                            ? e.special!.components
                                .map((c) => SpecialComponent(
                                      id: c.id,
                                      name: c.name,
                                      dishes: c.dishes,
                                      dish: c.dishes.first,
                                    ))
                                .toList()
                            : [],
                        notes: e.notes,
                      ))
                  .toList())
              .expand((element) => element)
              .toList(),
          preOrderStatus: PreOrderStatus.exists,
          preOrder: currentPreOrder,
          tableNumber: currentPreOrder.tableNumber,
        ));

        /// We need to clear stored cart if we have a pre order
        if (storedCart != null) {
          await _clearStoredCartUseCase();
        }
      } else {
        /// if we don't have a pre order, we need to check if we have a stored cart
        print('***StoredCart***: $storedCart');
        if (storedCart != null) {
          final storedMixedCartItemModel = storedCart.getItemById(event.restaurantId);

          final maxCacheTimeLimit = DateTime.now().subtract(const Duration(minutes: 30));

          print("***Time***");
          print(storedMixedCartItemModel?.date.isAfter(maxCacheTimeLimit));

          if (storedMixedCartItemModel != null && (storedMixedCartItemModel.date.isAfter(maxCacheTimeLimit))) {
            emit(state.copyWith(
              cart: storedMixedCartItemModel.cart,
              specialCart: storedMixedCartItemModel.specialCart,
            ));
          } else {
            await _clearStoredCartUseCase();
          }
        }
        emit(state.copyWith(preOrderStatus: PreOrderStatus.notExists));
      }
    } catch (e, s) {
      _logger.error('Problem with fetching pre order ${e.toString()} $s');
      emit(state.copyWith(preOrderStatus: PreOrderStatus.notExists));
    }
  }

  Map<String, List<ExtraItemModel>> _extractSelectedExtras(List<ExtraGroupModel> extras) {
    Map<String, List<ExtraItemModel>> selectedExtras = {};

    for (final group in extras) {
      if (group.items.isNotEmpty) {
        selectedExtras[group.id] = group.items;
      }
    }

    return selectedExtras;
  }

  OrderModel? _getCurrentPreOrder(OrdersResponse preOrderResponse) {
    final List<OrderModel> orders = preOrderResponse.data;

    final List<OrderModel> ordersFiltered = orders.where((o) => o.orderStatus != OrderStatus.cancelled).toList();
    final List<OrderModel> orderDetailsFiltered = ordersFiltered.map((o) {
      final List<OrderDetailModel> details = o.details.where((d) => d.orderStatus != OrderStatus.cancelled).toList();
      return o.copyWith(details: details);
    }).toList();

    if (orderDetailsFiltered.isNotEmpty &&
        orderDetailsFiltered.first.orderStatus != null &&
        orderDetailsFiltered.first.orderStatus != OrderStatus.cancelled) {
      return orderDetailsFiltered.first;
    }

    return null;
  }

  bool _compareSpecialCarts(List<SpecialCartItemModel> cart1, List<SpecialCartItemModel> cart2) {
    if (cart1.length != cart2.length) return false;

    final sortedCart1 = List<SpecialCartItemModel>.from(cart1)..sort((a, b) => a.special.id.compareTo(b.special.id));
    final sortedCart2 = List<SpecialCartItemModel>.from(cart2)..sort((a, b) => a.special.id.compareTo(b.special.id));

    for (int i = 0; i < sortedCart1.length; i++) {
      if (!_areSpecialItemsEqual(sortedCart1[i], sortedCart2[i])) {
        return false;
      }
    }
    return true;
  }

  bool _compareCarts(List<CartItemModel> cart1, List<CartItemModel> cart2) {
    if (cart1.length != cart2.length) return false;

    final sortedCart1 = List<CartItemModel>.from(cart1)..sort((a, b) => a.dish.id.compareTo(b.dish.id));
    final sortedCart2 = List<CartItemModel>.from(cart2)..sort((a, b) => a.dish.id.compareTo(b.dish.id));

    for (int i = 0; i < sortedCart1.length; i++) {
      if (!_areItemsEqual(sortedCart1[i], sortedCart2[i])) {
        return false;
      }
    }
    return true;
  }

  bool _areItemsEqual(CartItemModel item1, CartItemModel item2) {
    return item1.id == item2.id;
  }

  bool _areSpecialItemsEqual(SpecialCartItemModel item1, SpecialCartItemModel item2) {
    return item1.id == item2.id;
  }

  Future<void> _pay(_Pay event, Emitter<OrderFlowState> emit) async {
    final preOrder = state.preOrder != null && state.preOrderStatus == PreOrderStatus.exists ? state.preOrder : null;

    try {
      emit(state.copyWith(paymentStatus: PaymentProceedStatus.loading));

      final type = preOrder != null ? ChangeOrderType.update : ChangeOrderType.add;

      OrderModel? currentOrder = state.preOrder;

      if ((state.cart.isNotEmpty && !_compareCarts(state.cart, state.order)) ||
          (state.specialCart.isNotEmpty && !_compareSpecialCarts(state.specialCart, state.orderSpecialCart))) {
        final response = await _changeOrderUseCase(
          restaurantId: event.restaurantId,
          tableNumber: state.tableNumber,
          cart: state.cart,
          specialCart: state.specialCart,
          type: type,
          orderId: currentOrder?.id,
          couponId: event.couponId,
        );
        currentOrder = response.data;

        emit(state.copyWith(
          cart: [],
          specialCart: [],
          order: currentOrder.details
              .map((e) => e.items
                  .where((e) => e.type == OrderType.dish && e.dish != null)
                  .map((e) => CartItemModel(
                        id: e.id,
                        dish: e.dish!,
                        quantity: e.quantity,
                        notes: e.notes,
                      ))
                  .toList())
              .expand((element) => element)
              .toList(),
          orderSpecialCart: currentOrder.details
              .map((e) => e.items
                  .where((e) => e.type == OrderType.special && e.special != null)
                  .map((e) => SpecialCartItemModel(
                        id: e.id,
                        special: e.special!,
                        quantity: e.quantity,
                        components: e.special?.components != null
                            ? e.special!.components
                                .map((c) => SpecialComponent(
                                      id: c.id,
                                      name: c.name,
                                      dishes: c.dishes,
                                      dish: c.dishes.first,
                                    ))
                                .toList()
                            : [],
                        notes: e.notes,
                      ))
                  .toList())
              .expand((element) => element)
              .toList(),
        ));
      }

      if ((currentOrder != null && event.couponId != null) && (currentOrder.couponId == null || currentOrder.couponId != event.couponId)) {
        await _attachCouponUseCase(orderId: currentOrder.id, couponId: event.couponId!);
      }

      emit(state.copyWith(
        preOrder: currentOrder,
        preOrderStatus: currentOrder != null ? PreOrderStatus.exists : PreOrderStatus.notExists,
      ));

      await _paymentService.initPaymentSheet(
        currentOrder!.id,
        event.stripeAccountId,
      );

      final computedDietPreferences = currentOrder.dietPreferences.toSet();

      emit(state.copyWith(
        paymentStatus: PaymentProceedStatus.success,
        successfulOrderId: currentOrder.id,
        dietPreferences: computedDietPreferences,
      ));
    } on DioException catch (e, s) {
      _logger.error('Problem with initializing payment sheet on Dio ${e.toString()} $s');
      emit(state.copyWith(
        paymentStatus: PaymentProceedStatus.error,
        paymentError: e.message,
      ));
    } on StripeException catch (e, s) {
      _logger.error('Problem with initializing payment sheet on Stripe ${e.toString()} $s');
      emit(state.copyWith(
        paymentStatus: PaymentProceedStatus.error,
        paymentError: e.error.message,
      ));
    } catch (e, s) {
      _logger.error('Problem with initializing payment sheet ${e.toString()} $s');
      emit(state.copyWith(paymentStatus: PaymentProceedStatus.error, paymentError: e.toString()));
    }

    emit(state.copyWith(
      paymentStatus: PaymentProceedStatus.idle,
    ));
  }

  Future<void> _changeFavorite(_ChangeFavorite event, Emitter<OrderFlowState> emit) async {
    final isFavorite = event.isFavorite;

    if (isFavorite) {
      await _addToFavoriteUseCase(event.restaurantId);

      _favoriteCheckerService.updateFavorite(event.restaurantId, true);
    } else {
      await _removeFromFavoriteUseCase(event.restaurantId);

      _favoriteCheckerService.updateFavorite(event.restaurantId, false);
    }

    emit(state.copyWith(favorite: (event.restaurantId, event.isFavorite)));
  }

  Future<void> _resetOrderFlow(_ResetOrderFlow event, Emitter<OrderFlowState> emit) async {
    emit(const OrderFlowState(preOrder: null, favorite: null));
  }

  Future<void> _storeOrder(_StoreOrder event, Emitter<OrderFlowState> emit) async {
    try {
      await _createStoredOrderUseCase(event.model);
    } catch (e, s) {
      _logger.error('Problem with storing order ${e.toString()} $s');
    }
  }

  Future<void> _clearStoredCart(_ClearStoredCart event, Emitter<OrderFlowState> emit) async {
    try {
      await _clearStoredCartUseCase();
    } catch (e, s) {
      _logger.error('Problem with clearing stored cart ${e.toString()} $s');
    }
  }

  Future<void> _clearStoredOrder(_ClearStoredOrder event, Emitter<OrderFlowState> emit) async {
    try {
      await _clearStoredOrderUseCase();
    } catch (e, s) {
      _logger.error('Problem with clearing stored order ${e.toString()} $s');
    }
  }
}
