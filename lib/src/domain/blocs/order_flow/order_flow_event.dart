part of 'order_flow_bloc.dart';

@freezed
class OrderFlowEvent with _$OrderFlowEvent {
  const factory OrderFlowEvent.fetchMenus(String restaurantId) = _FetchMenus;
  const factory OrderFlowEvent.fetchSpecials(String restaurantId) = _FetchSpecials;
  const factory OrderFlowEvent.addDishToCart({
    required String restaurantId,
    required DishModel dish,
    required  int quantity,
    Map<String, List<ExtraItemModel>>? groupedExtras,
    String? notes,
    @Default(false) withUnique,
    String? uuid,
  }) = _AddDishToCart;
  const factory OrderFlowEvent.removeDishFromCart({
    required String restaurantId,
    required DishModel dish,
    String? uuid
  }) = _RemoveDishFromCart;
  const factory OrderFlowEvent.addSpecialToCart({
    required String restaurantId,
    required SpecialModel special,
    required int quantity,
    Set<SpecialComponent>? selectedComponents,
    String? notes
  }) = _AddSpecialToCart;
  const factory OrderFlowEvent.removeSpecialFromCart(
    String restaurantId,
    SpecialModel special
  ) = _RemoveSpecialFromCart;
  const factory OrderFlowEvent.changeOrderFlowType(OrderFlowType type) = _ChangeOrderFlowType;
  const factory OrderFlowEvent.addSpecialRequirementsToDish(
    String restaurantId,
    DishModel dish,
    String specialRequirements
  ) = _AddSpecialRequirementsToDish;
  const factory OrderFlowEvent.changeOrder(String restaurantId) = _ChangeOrder;
  const factory OrderFlowEvent.getPreOrder(String restaurantId) = _GetPreOrder;
  const factory OrderFlowEvent.pay(String restaurantId, String stripeAccountId, String? couponId) = _Pay;
  const factory OrderFlowEvent.changeTableNumber(String tableNumber) = _ChangeTableNumber;
  const factory OrderFlowEvent.changeFavorite(String restaurantId, bool isFavorite) = _ChangeFavorite;
  const factory OrderFlowEvent.changeDietPreferences(Set<DietPreferencesModel> dietPreferences) = _ChangeDietPreferences;
  const factory OrderFlowEvent.earnPoints({required String orderId}) = _EarnPoints;
  const factory OrderFlowEvent.precalculateOrder({
    required String restaurantId,
    required String? couponId,
    required List<CartItemModel> dishes,
    required List<SpecialCartItemModel> specials,
  }) = _PrecalculateOrder;
  const factory OrderFlowEvent.detachCoupon({required String orderId}) = _DetachCoupon;
  const factory OrderFlowEvent.resetOrderFlow() = _ResetOrderFlow;
  const factory OrderFlowEvent.storeOrder(StoredOrderModel model) = _StoreOrder;
  const factory OrderFlowEvent.clearStoredOrder() = _ClearStoredOrder;
  const factory OrderFlowEvent.clearStoredCart() = _ClearStoredCart;
}
