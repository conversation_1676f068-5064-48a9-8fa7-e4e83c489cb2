import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/utils/platform_utils.dart';

import '../../models/review/late_review_notification.dart';
import '../../services/navigation/navigation_event_service.dart';

part 'app_event.dart';

part 'app_state.dart';

part 'app_bloc.freezed.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  final GetLocaleUseCase _getLocaleUseCase;
  final SetLocaleUseCase _setLocaleUseCase;
  final NotificationService _notificationService;
  final DeviceInfoPlugin _deviceInfoPlugin;
  final UpdateFcmTokenUseCase _updateFcmTokenUseCase;
  final DeleteFcmTokenUseCase _deleteFcmTokenUseCase;
  final DeepLinksService _deepLinksService;
  final GetFirstLaunchUseCase _getFirstLaunchUseCase;
  final SetFirstLaunchUseCase _setFirstLaunchUseCase;
  final ClearSettingsUseCase _clearSettingsUseCase;
  final DeactivateService _deactivateService;
  final ReadStoredOrderUseCase _readStoredOrderUseCase;
  final NavigationEventService _navigationEventService;
  final MLogger _logger;

  late final StreamSubscription<RemoteMessage> _onBackgroundMessageSubscription;
  late final StreamSubscription<RemoteMessage> _onMessageSubscription;
  late final StreamSubscription<RemoteMessage> _onMessageOpenedAppSubscription;
  late final StreamSubscription<String> _onTokenRefreshSubscription;
  late final StreamSubscription<Uri?> _deepLinkSubscription;
  late final StreamSubscription<bool> _onDeactivateSubscription;

  AppBloc(
      this._getLocaleUseCase,
      this._setLocaleUseCase,
      this._notificationService,
      this._deviceInfoPlugin,
      this._updateFcmTokenUseCase,
      this._deleteFcmTokenUseCase,
      this._deepLinksService,
      this._getFirstLaunchUseCase,
      this._setFirstLaunchUseCase,
      this._clearSettingsUseCase,
      this._deactivateService,
      this._readStoredOrderUseCase,
      this._navigationEventService,
      this._logger)
      : super(const AppState(deepLinkQRCodeToken: null)) {
    on<_SetBNBVisible>(_setBNBVisible);
    on<_ChangeLocale>(_changeLocale);
    on<_InitLocale>(_initLocale);
    on<_SetDeepLinkQRCodeToken>(_setDeepLinkQRCodeToken);
    on<_SetUserLoaded>(_setUserLoaded);
    on<_SetRestaurantsLoaded>(_setRestaurantsLoaded);
    on<_SetUserDeactivated>(_setUserDeactivated);
    on<_ReadStoredOrder>(_readStoredOrder);
    on<_LateReviewNotification>(_lateReviewNotification);
    on<_ClearShowReview>(_clearShowReview);

    _initNotifications();
    _initUpdatingFcmToken();
    _initDeepLinks();
    _initFirstLaunchCheck();
    _initCheckDeactivate();

    add(const AppEvent.initLocale());
  }
  
  /// Handles initial message on app cold start
  /// Called when app is launched by clicking a notification
  void handleInitialMessage(RemoteMessage message) {
    if (message.data['notificationType'] == 'late_review') {
      add(AppEvent.showLateReview(LateReviewNotification(
        restaurantId: message.data['restaurantId'],
        restaurantName: message.data['restaurantName'] ?? 'Default',
      )));
    } else if (message.data['notificationType'] == 'couponRefreshmentAvailable') {
      _navigationEventService.navigateToRestaurant(message.data['restaurantId']);
    }
  }

  Future<void> _lateReviewNotification(
    _LateReviewNotification event, Emitter<AppState> emit) async {
    emit(state.copyWith(lateReviewNotification: null));

    emit(state.copyWith(lateReviewNotification: event.lateReviewNotification));
  }

  Future<void> _clearShowReview(
    _ClearShowReview event, Emitter<AppState> emit) async {
    emit(state.copyWith(lateReviewNotification: null));
  }

  Future<void> _setUserDeactivated(
      _SetUserDeactivated event, Emitter<AppState> emit) async {
    print('Deactivated: ${event.value}');
    emit(state.copyWith(isUserDeactivated: event.value));
  }

  void _initCheckDeactivate() {
    _onDeactivateSubscription =
        _deactivateService.deactivateStream.listen((value) {
      _logger.info('Deactivate: $value');
      add(AppEvent.setUserDeactivated(value));
    });
  }

  Future<void> _initFirstLaunchCheck() async {
    final isFirstLaunch = await _getFirstLaunchUseCase();
    _logger.info('First launch: $isFirstLaunch');
    if (isFirstLaunch) {
      await _clearSettingsUseCase();
      await _setFirstLaunchUseCase(false);
    }
  }

  Future<void> _initUpdatingFcmToken() async {
    _onTokenRefreshSubscription =
        _notificationService.onTokenRefresh.listen((token) async {
      _logger.info('Firebase token: $token');
      if (state.isUserLoaded) {
        final device = await getDevice();
        await _updateFcmTokenUseCase(token, device.deviceId);
      }
    });
  }

  Future<void> _initDeepLinks() async {
    final initialLink = await _deepLinksService.getInitialDeepLink();
    if (initialLink != null) {
      _handleQRCodeDeepLinks(initialLink);
    }

    _deepLinkSubscription = _deepLinksService.stream.listen((appLink) {
      _logger.info('Deep link in AppBloc: $appLink');
      if (appLink != null) {
        _handleQRCodeDeepLinks(appLink);
      }
    });
  }

  void _initNotifications() {
    final token = _notificationService.getToken();
    token.then((value) {
      _logger.info('Firebase token: $value');
    });
    
    // NOTIFICATION BEHAVIOR GUIDE:
    // - late_review: Auto-opens modal when notification arrives (foreground) or clicked
    // - couponRefreshmentAvailable: Opens restaurant ONLY when user clicks notification
    // To add new types: Add handling in all 3 listeners below + handleInitialMessage()
    
    // 1. LOCAL NOTIFICATION CLICKS (foreground only)
    // This stream receives events when user clicks on local notifications
    _onBackgroundMessageSubscription =
        _notificationService.onBackgroundMessage.listen((message) {
      if (message.data['notificationType'] == 'late_review') {
        add(AppEvent.showLateReview(LateReviewNotification(
          restaurantId: message.data['restaurantId'],
          restaurantName: message.data['restaurantName'] ?? 'Default',
        )));
      } else if (message.data['notificationType'] == 'couponRefreshmentAvailable') {
        _navigationEventService.navigateToRestaurant(message.data['restaurantId']);
      }
    });

    // 2. FOREGROUND FCM MESSAGES
    // When app is open and receives FCM
    _onMessageSubscription = _notificationService.onMessage.listen((message) {
      _logger.info('onMessage: ${message.toMap()}');
      
      // Always show local notification for all types
      _notificationService.showLocalNotification(
          message.notification?.title ?? '',
          message.notification?.body ?? '',
          message.data);

      // Auto-action ONLY for specific types (late_review)
      if (message.data['notificationType'] == 'late_review') {
        add(AppEvent.showLateReview(LateReviewNotification(
          restaurantId: message.data['restaurantId'],
          restaurantName: message.data['restaurantName'] ?? 'Default',
        )));
      }
      // couponRefreshmentAvailable: Wait for click (handled by onBackgroundMessage)
    });

    // 3. BACKGROUND FCM CLICKS
    // When user clicks FCM notification while app is in background
    _onMessageOpenedAppSubscription =
        _notificationService.onMessageOpenedApp.listen((message) {
      _logger.info('onMessageOpenedApp: ${message.toMap()}');
      if (message.data['notificationType'] == 'late_review') {
        add(AppEvent.showLateReview(LateReviewNotification(
          restaurantId: message.data['restaurantId'],
          restaurantName: message.data['restaurantName'] ?? 'Default',
        )));
      } else if (message.data['notificationType'] == 'couponRefreshmentAvailable') {
        _navigationEventService.navigateToRestaurant(message.data['restaurantId']);
      }
    });
  }

  void _handleQRCodeDeepLinks(Uri deepLink) {
    final token = deepLink.queryParameters['token'];
    if (token != null && deepLink.pathSegments.last == 'qrcode') {
      _logger.info('Deep link token: $token');
      add(AppEvent.setDeepLinkQRCodeToken(token));
    }
  }

  Future<void> _setUserLoaded(
      _SetUserLoaded event, Emitter<AppState> emit) async {
    try {
      final device = await getDevice();
      if (event.value) {
        final fcmToken = await _notificationService.getToken();
        if (fcmToken != null) {
          await _updateFcmTokenUseCase(fcmToken, device.deviceId);
        }
      } else {
        await _deleteFcmTokenUseCase(device.deviceId);
      }
    } on DioException catch (e, s) {
      _logger.error('Error fcm token operations $e $s');
    } catch (e, s) {
      _logger.error('Error fcm token operations $e $s');
    }
    emit(state.copyWith(isUserLoaded: event.value));
  }

  void _setRestaurantsLoaded(
      _SetRestaurantsLoaded event, Emitter<AppState> emit) {
    emit(state.copyWith(isRestaurantsLoaded: event.value));
  }

  void _setDeepLinkQRCodeToken(
      _SetDeepLinkQRCodeToken event, Emitter<AppState> emit) {
    _logger.info('Deep link in AppBloc: ${event.value}');
    emit(state.copyWith(deepLinkQRCodeToken: event.value));
  }

  Future<void> _initLocale(_InitLocale event, Emitter<AppState> emit) async {
    final Object getStoredRawLocale =
        await _getLocaleUseCase() ?? LocaleSettings.useDeviceLocale();
    final locale = AppLocaleUtils.parseLocaleParts(
        languageCode: getStoredRawLocale is AppLocale
            ? getStoredRawLocale.languageCode
            : getStoredRawLocale.toString());

    emit(state.copyWith(locale: locale));
    LocaleSettings.setLocale(locale);
  }

  Future<void> _setBNBVisible(
      _SetBNBVisible event, Emitter<AppState> emit) async {
    emit(state.copyWith(isBNBVisible: event.value));
  }

  Future<void> _changeLocale(
      _ChangeLocale event, Emitter<AppState> emit) async {
    try {
      await _setLocaleUseCase(event.value.languageCode);
      emit(state.copyWith(locale: event.value));
      LocaleSettings.setLocale(event.value);
    } catch (e) {
      _logger.error('Error changing locale $e');
    }
  }

  Future<Device> getDevice() async {
    final deviceInfo = Platform.isAndroid
        ? readAndroidBuildData(await _deviceInfoPlugin.androidInfo)
        : readIosDeviceInfo(await _deviceInfoPlugin.iosInfo);
    return deviceInfo;
  }

  Future<void> _readStoredOrder(
      _ReadStoredOrder event, Emitter<AppState> emit) async {
    try {
      emit(state.copyWith(storedOrderStatus: StoredOrderStatus.loading));
      final storedOrder = await _readStoredOrderUseCase();

      emit(state.copyWith(
          storedOrder: storedOrder,
          storedOrderStatus: storedOrder == null
              ? StoredOrderStatus.idle
              : StoredOrderStatus.success));
    } catch (e) {
      emit(state.copyWith(storedOrderStatus: StoredOrderStatus.error));
    }
  }

  @override
  Future<void> close() {
    _onDeactivateSubscription.cancel();
    _onTokenRefreshSubscription.cancel();
    _onBackgroundMessageSubscription.cancel();
    _onMessageSubscription.cancel();
    _onMessageOpenedAppSubscription.cancel();
    _deepLinkSubscription.cancel();
    return super.close();
  }
}
