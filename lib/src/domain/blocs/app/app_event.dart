part of 'app_bloc.dart';

@freezed
class AppEvent with _$AppEvent {
  const factory AppEvent.setBNBVisible(bool value) = _SetBNBVisible;

  const factory AppEvent.changeLocale(AppLocale value) = _ChangeLocale;

  const factory AppEvent.initLocale() = _InitLocale;

  const factory AppEvent.setDeepLinkQRCodeToken(String? value) =
      _SetDeepLinkQRCodeToken;

  const factory AppEvent.setUserLoaded({@Default(true) bool value}) =
      _SetUserLoaded;

  const factory AppEvent.setRestaurantsLoaded(bool value) =
      _SetRestaurantsLoaded;

  const factory AppEvent.setUserDeactivated(bool value) = _SetUserDeactivated;

  const factory AppEvent.readStoredOrder() = _ReadStoredOrder;

  const factory AppEvent.showLateReview(LateReviewNotification lateReviewNotification) = _LateReviewNotification;

  const factory AppEvent.clearShowReview() = _ClearShowReview;
}
