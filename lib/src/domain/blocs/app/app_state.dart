part of 'app_bloc.dart';

@freezed
class AppState with _$AppState {
  const factory AppState({
    @Default(true) bool isBNBVisible,
    @Default(AppLocale.de) AppLocale locale,
    required String? deepLinkQRCodeToken,
    @Default(false) isUserLoaded,
    @Default(false) isRestaurantsLoaded,
    @Default(false) isUserDeactivated,
    StoredOrderModel? storedOrder,
    @Default(StoredOrderStatus.idle) StoredOrderStatus storedOrderStatus,
    LateReviewNotification? lateReviewNotification,
  }) = _AppState;
}

enum StoredOrderStatus { idle, loading, success, error }
