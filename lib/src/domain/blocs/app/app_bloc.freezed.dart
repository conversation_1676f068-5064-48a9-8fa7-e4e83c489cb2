// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppEventCopyWith<$Res> {
  factory $AppEventCopyWith(AppEvent value, $Res Function(AppEvent) then) =
      _$AppEventCopyWithImpl<$Res, AppEvent>;
}

/// @nodoc
class _$AppEventCopyWithImpl<$Res, $Val extends AppEvent>
    implements $AppEventCopyWith<$Res> {
  _$AppEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$SetBNBVisibleImplCopyWith<$Res> {
  factory _$$SetBNBVisibleImplCopyWith(
          _$SetBNBVisibleImpl value, $Res Function(_$SetBNBVisibleImpl) then) =
      __$$SetBNBVisibleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetBNBVisibleImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$SetBNBVisibleImpl>
    implements _$$SetBNBVisibleImplCopyWith<$Res> {
  __$$SetBNBVisibleImplCopyWithImpl(
      _$SetBNBVisibleImpl _value, $Res Function(_$SetBNBVisibleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetBNBVisibleImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetBNBVisibleImpl implements _SetBNBVisible {
  const _$SetBNBVisibleImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'AppEvent.setBNBVisible(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetBNBVisibleImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetBNBVisibleImplCopyWith<_$SetBNBVisibleImpl> get copyWith =>
      __$$SetBNBVisibleImplCopyWithImpl<_$SetBNBVisibleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return setBNBVisible(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return setBNBVisible?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (setBNBVisible != null) {
      return setBNBVisible(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return setBNBVisible(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return setBNBVisible?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (setBNBVisible != null) {
      return setBNBVisible(this);
    }
    return orElse();
  }
}

abstract class _SetBNBVisible implements AppEvent {
  const factory _SetBNBVisible(final bool value) = _$SetBNBVisibleImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetBNBVisibleImplCopyWith<_$SetBNBVisibleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeLocaleImplCopyWith<$Res> {
  factory _$$ChangeLocaleImplCopyWith(
          _$ChangeLocaleImpl value, $Res Function(_$ChangeLocaleImpl) then) =
      __$$ChangeLocaleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppLocale value});
}

/// @nodoc
class __$$ChangeLocaleImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$ChangeLocaleImpl>
    implements _$$ChangeLocaleImplCopyWith<$Res> {
  __$$ChangeLocaleImplCopyWithImpl(
      _$ChangeLocaleImpl _value, $Res Function(_$ChangeLocaleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$ChangeLocaleImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as AppLocale,
    ));
  }
}

/// @nodoc

class _$ChangeLocaleImpl implements _ChangeLocale {
  const _$ChangeLocaleImpl(this.value);

  @override
  final AppLocale value;

  @override
  String toString() {
    return 'AppEvent.changeLocale(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeLocaleImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeLocaleImplCopyWith<_$ChangeLocaleImpl> get copyWith =>
      __$$ChangeLocaleImplCopyWithImpl<_$ChangeLocaleImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return changeLocale(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return changeLocale?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (changeLocale != null) {
      return changeLocale(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return changeLocale(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return changeLocale?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (changeLocale != null) {
      return changeLocale(this);
    }
    return orElse();
  }
}

abstract class _ChangeLocale implements AppEvent {
  const factory _ChangeLocale(final AppLocale value) = _$ChangeLocaleImpl;

  AppLocale get value;
  @JsonKey(ignore: true)
  _$$ChangeLocaleImplCopyWith<_$ChangeLocaleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitLocaleImplCopyWith<$Res> {
  factory _$$InitLocaleImplCopyWith(
          _$InitLocaleImpl value, $Res Function(_$InitLocaleImpl) then) =
      __$$InitLocaleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitLocaleImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$InitLocaleImpl>
    implements _$$InitLocaleImplCopyWith<$Res> {
  __$$InitLocaleImplCopyWithImpl(
      _$InitLocaleImpl _value, $Res Function(_$InitLocaleImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitLocaleImpl implements _InitLocale {
  const _$InitLocaleImpl();

  @override
  String toString() {
    return 'AppEvent.initLocale()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitLocaleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return initLocale();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return initLocale?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (initLocale != null) {
      return initLocale();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return initLocale(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return initLocale?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (initLocale != null) {
      return initLocale(this);
    }
    return orElse();
  }
}

abstract class _InitLocale implements AppEvent {
  const factory _InitLocale() = _$InitLocaleImpl;
}

/// @nodoc
abstract class _$$SetDeepLinkQRCodeTokenImplCopyWith<$Res> {
  factory _$$SetDeepLinkQRCodeTokenImplCopyWith(
          _$SetDeepLinkQRCodeTokenImpl value,
          $Res Function(_$SetDeepLinkQRCodeTokenImpl) then) =
      __$$SetDeepLinkQRCodeTokenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$SetDeepLinkQRCodeTokenImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$SetDeepLinkQRCodeTokenImpl>
    implements _$$SetDeepLinkQRCodeTokenImplCopyWith<$Res> {
  __$$SetDeepLinkQRCodeTokenImplCopyWithImpl(
      _$SetDeepLinkQRCodeTokenImpl _value,
      $Res Function(_$SetDeepLinkQRCodeTokenImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$SetDeepLinkQRCodeTokenImpl(
      freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SetDeepLinkQRCodeTokenImpl implements _SetDeepLinkQRCodeToken {
  const _$SetDeepLinkQRCodeTokenImpl(this.value);

  @override
  final String? value;

  @override
  String toString() {
    return 'AppEvent.setDeepLinkQRCodeToken(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetDeepLinkQRCodeTokenImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetDeepLinkQRCodeTokenImplCopyWith<_$SetDeepLinkQRCodeTokenImpl>
      get copyWith => __$$SetDeepLinkQRCodeTokenImplCopyWithImpl<
          _$SetDeepLinkQRCodeTokenImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return setDeepLinkQRCodeToken(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return setDeepLinkQRCodeToken?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (setDeepLinkQRCodeToken != null) {
      return setDeepLinkQRCodeToken(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return setDeepLinkQRCodeToken(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return setDeepLinkQRCodeToken?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (setDeepLinkQRCodeToken != null) {
      return setDeepLinkQRCodeToken(this);
    }
    return orElse();
  }
}

abstract class _SetDeepLinkQRCodeToken implements AppEvent {
  const factory _SetDeepLinkQRCodeToken(final String? value) =
      _$SetDeepLinkQRCodeTokenImpl;

  String? get value;
  @JsonKey(ignore: true)
  _$$SetDeepLinkQRCodeTokenImplCopyWith<_$SetDeepLinkQRCodeTokenImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetUserLoadedImplCopyWith<$Res> {
  factory _$$SetUserLoadedImplCopyWith(
          _$SetUserLoadedImpl value, $Res Function(_$SetUserLoadedImpl) then) =
      __$$SetUserLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetUserLoadedImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$SetUserLoadedImpl>
    implements _$$SetUserLoadedImplCopyWith<$Res> {
  __$$SetUserLoadedImplCopyWithImpl(
      _$SetUserLoadedImpl _value, $Res Function(_$SetUserLoadedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetUserLoadedImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetUserLoadedImpl implements _SetUserLoaded {
  const _$SetUserLoadedImpl({this.value = true});

  @override
  @JsonKey()
  final bool value;

  @override
  String toString() {
    return 'AppEvent.setUserLoaded(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUserLoadedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUserLoadedImplCopyWith<_$SetUserLoadedImpl> get copyWith =>
      __$$SetUserLoadedImplCopyWithImpl<_$SetUserLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return setUserLoaded(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return setUserLoaded?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (setUserLoaded != null) {
      return setUserLoaded(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return setUserLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return setUserLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (setUserLoaded != null) {
      return setUserLoaded(this);
    }
    return orElse();
  }
}

abstract class _SetUserLoaded implements AppEvent {
  const factory _SetUserLoaded({final bool value}) = _$SetUserLoadedImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetUserLoadedImplCopyWith<_$SetUserLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetRestaurantsLoadedImplCopyWith<$Res> {
  factory _$$SetRestaurantsLoadedImplCopyWith(_$SetRestaurantsLoadedImpl value,
          $Res Function(_$SetRestaurantsLoadedImpl) then) =
      __$$SetRestaurantsLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetRestaurantsLoadedImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$SetRestaurantsLoadedImpl>
    implements _$$SetRestaurantsLoadedImplCopyWith<$Res> {
  __$$SetRestaurantsLoadedImplCopyWithImpl(_$SetRestaurantsLoadedImpl _value,
      $Res Function(_$SetRestaurantsLoadedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetRestaurantsLoadedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetRestaurantsLoadedImpl implements _SetRestaurantsLoaded {
  const _$SetRestaurantsLoadedImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'AppEvent.setRestaurantsLoaded(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetRestaurantsLoadedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetRestaurantsLoadedImplCopyWith<_$SetRestaurantsLoadedImpl>
      get copyWith =>
          __$$SetRestaurantsLoadedImplCopyWithImpl<_$SetRestaurantsLoadedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return setRestaurantsLoaded(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return setRestaurantsLoaded?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (setRestaurantsLoaded != null) {
      return setRestaurantsLoaded(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return setRestaurantsLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return setRestaurantsLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (setRestaurantsLoaded != null) {
      return setRestaurantsLoaded(this);
    }
    return orElse();
  }
}

abstract class _SetRestaurantsLoaded implements AppEvent {
  const factory _SetRestaurantsLoaded(final bool value) =
      _$SetRestaurantsLoadedImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetRestaurantsLoadedImplCopyWith<_$SetRestaurantsLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetUserDeactivatedImplCopyWith<$Res> {
  factory _$$SetUserDeactivatedImplCopyWith(_$SetUserDeactivatedImpl value,
          $Res Function(_$SetUserDeactivatedImpl) then) =
      __$$SetUserDeactivatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$SetUserDeactivatedImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$SetUserDeactivatedImpl>
    implements _$$SetUserDeactivatedImplCopyWith<$Res> {
  __$$SetUserDeactivatedImplCopyWithImpl(_$SetUserDeactivatedImpl _value,
      $Res Function(_$SetUserDeactivatedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SetUserDeactivatedImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SetUserDeactivatedImpl implements _SetUserDeactivated {
  const _$SetUserDeactivatedImpl(this.value);

  @override
  final bool value;

  @override
  String toString() {
    return 'AppEvent.setUserDeactivated(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUserDeactivatedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUserDeactivatedImplCopyWith<_$SetUserDeactivatedImpl> get copyWith =>
      __$$SetUserDeactivatedImplCopyWithImpl<_$SetUserDeactivatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return setUserDeactivated(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return setUserDeactivated?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (setUserDeactivated != null) {
      return setUserDeactivated(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return setUserDeactivated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return setUserDeactivated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (setUserDeactivated != null) {
      return setUserDeactivated(this);
    }
    return orElse();
  }
}

abstract class _SetUserDeactivated implements AppEvent {
  const factory _SetUserDeactivated(final bool value) =
      _$SetUserDeactivatedImpl;

  bool get value;
  @JsonKey(ignore: true)
  _$$SetUserDeactivatedImplCopyWith<_$SetUserDeactivatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReadStoredOrderImplCopyWith<$Res> {
  factory _$$ReadStoredOrderImplCopyWith(_$ReadStoredOrderImpl value,
          $Res Function(_$ReadStoredOrderImpl) then) =
      __$$ReadStoredOrderImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReadStoredOrderImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$ReadStoredOrderImpl>
    implements _$$ReadStoredOrderImplCopyWith<$Res> {
  __$$ReadStoredOrderImplCopyWithImpl(
      _$ReadStoredOrderImpl _value, $Res Function(_$ReadStoredOrderImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ReadStoredOrderImpl implements _ReadStoredOrder {
  const _$ReadStoredOrderImpl();

  @override
  String toString() {
    return 'AppEvent.readStoredOrder()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReadStoredOrderImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return readStoredOrder();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return readStoredOrder?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (readStoredOrder != null) {
      return readStoredOrder();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return readStoredOrder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return readStoredOrder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (readStoredOrder != null) {
      return readStoredOrder(this);
    }
    return orElse();
  }
}

abstract class _ReadStoredOrder implements AppEvent {
  const factory _ReadStoredOrder() = _$ReadStoredOrderImpl;
}

/// @nodoc
abstract class _$$LateReviewNotificationImplCopyWith<$Res> {
  factory _$$LateReviewNotificationImplCopyWith(
          _$LateReviewNotificationImpl value,
          $Res Function(_$LateReviewNotificationImpl) then) =
      __$$LateReviewNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({LateReviewNotification lateReviewNotification});

  $LateReviewNotificationCopyWith<$Res> get lateReviewNotification;
}

/// @nodoc
class __$$LateReviewNotificationImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$LateReviewNotificationImpl>
    implements _$$LateReviewNotificationImplCopyWith<$Res> {
  __$$LateReviewNotificationImplCopyWithImpl(
      _$LateReviewNotificationImpl _value,
      $Res Function(_$LateReviewNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lateReviewNotification = null,
  }) {
    return _then(_$LateReviewNotificationImpl(
      null == lateReviewNotification
          ? _value.lateReviewNotification
          : lateReviewNotification // ignore: cast_nullable_to_non_nullable
              as LateReviewNotification,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LateReviewNotificationCopyWith<$Res> get lateReviewNotification {
    return $LateReviewNotificationCopyWith<$Res>(_value.lateReviewNotification,
        (value) {
      return _then(_value.copyWith(lateReviewNotification: value));
    });
  }
}

/// @nodoc

class _$LateReviewNotificationImpl implements _LateReviewNotification {
  const _$LateReviewNotificationImpl(this.lateReviewNotification);

  @override
  final LateReviewNotification lateReviewNotification;

  @override
  String toString() {
    return 'AppEvent.showLateReview(lateReviewNotification: $lateReviewNotification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LateReviewNotificationImpl &&
            (identical(other.lateReviewNotification, lateReviewNotification) ||
                other.lateReviewNotification == lateReviewNotification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, lateReviewNotification);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LateReviewNotificationImplCopyWith<_$LateReviewNotificationImpl>
      get copyWith => __$$LateReviewNotificationImplCopyWithImpl<
          _$LateReviewNotificationImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return showLateReview(lateReviewNotification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return showLateReview?.call(lateReviewNotification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (showLateReview != null) {
      return showLateReview(lateReviewNotification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return showLateReview(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return showLateReview?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (showLateReview != null) {
      return showLateReview(this);
    }
    return orElse();
  }
}

abstract class _LateReviewNotification implements AppEvent {
  const factory _LateReviewNotification(
          final LateReviewNotification lateReviewNotification) =
      _$LateReviewNotificationImpl;

  LateReviewNotification get lateReviewNotification;
  @JsonKey(ignore: true)
  _$$LateReviewNotificationImplCopyWith<_$LateReviewNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearShowReviewImplCopyWith<$Res> {
  factory _$$ClearShowReviewImplCopyWith(_$ClearShowReviewImpl value,
          $Res Function(_$ClearShowReviewImpl) then) =
      __$$ClearShowReviewImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearShowReviewImplCopyWithImpl<$Res>
    extends _$AppEventCopyWithImpl<$Res, _$ClearShowReviewImpl>
    implements _$$ClearShowReviewImplCopyWith<$Res> {
  __$$ClearShowReviewImplCopyWithImpl(
      _$ClearShowReviewImpl _value, $Res Function(_$ClearShowReviewImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$ClearShowReviewImpl implements _ClearShowReview {
  const _$ClearShowReviewImpl();

  @override
  String toString() {
    return 'AppEvent.clearShowReview()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearShowReviewImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool value) setBNBVisible,
    required TResult Function(AppLocale value) changeLocale,
    required TResult Function() initLocale,
    required TResult Function(String? value) setDeepLinkQRCodeToken,
    required TResult Function(bool value) setUserLoaded,
    required TResult Function(bool value) setRestaurantsLoaded,
    required TResult Function(bool value) setUserDeactivated,
    required TResult Function() readStoredOrder,
    required TResult Function(LateReviewNotification lateReviewNotification)
        showLateReview,
    required TResult Function() clearShowReview,
  }) {
    return clearShowReview();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool value)? setBNBVisible,
    TResult? Function(AppLocale value)? changeLocale,
    TResult? Function()? initLocale,
    TResult? Function(String? value)? setDeepLinkQRCodeToken,
    TResult? Function(bool value)? setUserLoaded,
    TResult? Function(bool value)? setRestaurantsLoaded,
    TResult? Function(bool value)? setUserDeactivated,
    TResult? Function()? readStoredOrder,
    TResult? Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult? Function()? clearShowReview,
  }) {
    return clearShowReview?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool value)? setBNBVisible,
    TResult Function(AppLocale value)? changeLocale,
    TResult Function()? initLocale,
    TResult Function(String? value)? setDeepLinkQRCodeToken,
    TResult Function(bool value)? setUserLoaded,
    TResult Function(bool value)? setRestaurantsLoaded,
    TResult Function(bool value)? setUserDeactivated,
    TResult Function()? readStoredOrder,
    TResult Function(LateReviewNotification lateReviewNotification)?
        showLateReview,
    TResult Function()? clearShowReview,
    required TResult orElse(),
  }) {
    if (clearShowReview != null) {
      return clearShowReview();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetBNBVisible value) setBNBVisible,
    required TResult Function(_ChangeLocale value) changeLocale,
    required TResult Function(_InitLocale value) initLocale,
    required TResult Function(_SetDeepLinkQRCodeToken value)
        setDeepLinkQRCodeToken,
    required TResult Function(_SetUserLoaded value) setUserLoaded,
    required TResult Function(_SetRestaurantsLoaded value) setRestaurantsLoaded,
    required TResult Function(_SetUserDeactivated value) setUserDeactivated,
    required TResult Function(_ReadStoredOrder value) readStoredOrder,
    required TResult Function(_LateReviewNotification value) showLateReview,
    required TResult Function(_ClearShowReview value) clearShowReview,
  }) {
    return clearShowReview(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetBNBVisible value)? setBNBVisible,
    TResult? Function(_ChangeLocale value)? changeLocale,
    TResult? Function(_InitLocale value)? initLocale,
    TResult? Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult? Function(_SetUserLoaded value)? setUserLoaded,
    TResult? Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult? Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult? Function(_ReadStoredOrder value)? readStoredOrder,
    TResult? Function(_LateReviewNotification value)? showLateReview,
    TResult? Function(_ClearShowReview value)? clearShowReview,
  }) {
    return clearShowReview?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetBNBVisible value)? setBNBVisible,
    TResult Function(_ChangeLocale value)? changeLocale,
    TResult Function(_InitLocale value)? initLocale,
    TResult Function(_SetDeepLinkQRCodeToken value)? setDeepLinkQRCodeToken,
    TResult Function(_SetUserLoaded value)? setUserLoaded,
    TResult Function(_SetRestaurantsLoaded value)? setRestaurantsLoaded,
    TResult Function(_SetUserDeactivated value)? setUserDeactivated,
    TResult Function(_ReadStoredOrder value)? readStoredOrder,
    TResult Function(_LateReviewNotification value)? showLateReview,
    TResult Function(_ClearShowReview value)? clearShowReview,
    required TResult orElse(),
  }) {
    if (clearShowReview != null) {
      return clearShowReview(this);
    }
    return orElse();
  }
}

abstract class _ClearShowReview implements AppEvent {
  const factory _ClearShowReview() = _$ClearShowReviewImpl;
}

/// @nodoc
mixin _$AppState {
  bool get isBNBVisible => throw _privateConstructorUsedError;
  AppLocale get locale => throw _privateConstructorUsedError;
  String? get deepLinkQRCodeToken => throw _privateConstructorUsedError;
  dynamic get isUserLoaded => throw _privateConstructorUsedError;
  dynamic get isRestaurantsLoaded => throw _privateConstructorUsedError;
  dynamic get isUserDeactivated => throw _privateConstructorUsedError;
  StoredOrderModel? get storedOrder => throw _privateConstructorUsedError;
  StoredOrderStatus get storedOrderStatus => throw _privateConstructorUsedError;
  LateReviewNotification? get lateReviewNotification =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AppStateCopyWith<AppState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppStateCopyWith<$Res> {
  factory $AppStateCopyWith(AppState value, $Res Function(AppState) then) =
      _$AppStateCopyWithImpl<$Res, AppState>;
  @useResult
  $Res call(
      {bool isBNBVisible,
      AppLocale locale,
      String? deepLinkQRCodeToken,
      dynamic isUserLoaded,
      dynamic isRestaurantsLoaded,
      dynamic isUserDeactivated,
      StoredOrderModel? storedOrder,
      StoredOrderStatus storedOrderStatus,
      LateReviewNotification? lateReviewNotification});

  $StoredOrderModelCopyWith<$Res>? get storedOrder;
  $LateReviewNotificationCopyWith<$Res>? get lateReviewNotification;
}

/// @nodoc
class _$AppStateCopyWithImpl<$Res, $Val extends AppState>
    implements $AppStateCopyWith<$Res> {
  _$AppStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isBNBVisible = null,
    Object? locale = null,
    Object? deepLinkQRCodeToken = freezed,
    Object? isUserLoaded = freezed,
    Object? isRestaurantsLoaded = freezed,
    Object? isUserDeactivated = freezed,
    Object? storedOrder = freezed,
    Object? storedOrderStatus = null,
    Object? lateReviewNotification = freezed,
  }) {
    return _then(_value.copyWith(
      isBNBVisible: null == isBNBVisible
          ? _value.isBNBVisible
          : isBNBVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as AppLocale,
      deepLinkQRCodeToken: freezed == deepLinkQRCodeToken
          ? _value.deepLinkQRCodeToken
          : deepLinkQRCodeToken // ignore: cast_nullable_to_non_nullable
              as String?,
      isUserLoaded: freezed == isUserLoaded
          ? _value.isUserLoaded
          : isUserLoaded // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isRestaurantsLoaded: freezed == isRestaurantsLoaded
          ? _value.isRestaurantsLoaded
          : isRestaurantsLoaded // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isUserDeactivated: freezed == isUserDeactivated
          ? _value.isUserDeactivated
          : isUserDeactivated // ignore: cast_nullable_to_non_nullable
              as dynamic,
      storedOrder: freezed == storedOrder
          ? _value.storedOrder
          : storedOrder // ignore: cast_nullable_to_non_nullable
              as StoredOrderModel?,
      storedOrderStatus: null == storedOrderStatus
          ? _value.storedOrderStatus
          : storedOrderStatus // ignore: cast_nullable_to_non_nullable
              as StoredOrderStatus,
      lateReviewNotification: freezed == lateReviewNotification
          ? _value.lateReviewNotification
          : lateReviewNotification // ignore: cast_nullable_to_non_nullable
              as LateReviewNotification?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $StoredOrderModelCopyWith<$Res>? get storedOrder {
    if (_value.storedOrder == null) {
      return null;
    }

    return $StoredOrderModelCopyWith<$Res>(_value.storedOrder!, (value) {
      return _then(_value.copyWith(storedOrder: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LateReviewNotificationCopyWith<$Res>? get lateReviewNotification {
    if (_value.lateReviewNotification == null) {
      return null;
    }

    return $LateReviewNotificationCopyWith<$Res>(_value.lateReviewNotification!,
        (value) {
      return _then(_value.copyWith(lateReviewNotification: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppStateImplCopyWith<$Res>
    implements $AppStateCopyWith<$Res> {
  factory _$$AppStateImplCopyWith(
          _$AppStateImpl value, $Res Function(_$AppStateImpl) then) =
      __$$AppStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isBNBVisible,
      AppLocale locale,
      String? deepLinkQRCodeToken,
      dynamic isUserLoaded,
      dynamic isRestaurantsLoaded,
      dynamic isUserDeactivated,
      StoredOrderModel? storedOrder,
      StoredOrderStatus storedOrderStatus,
      LateReviewNotification? lateReviewNotification});

  @override
  $StoredOrderModelCopyWith<$Res>? get storedOrder;
  @override
  $LateReviewNotificationCopyWith<$Res>? get lateReviewNotification;
}

/// @nodoc
class __$$AppStateImplCopyWithImpl<$Res>
    extends _$AppStateCopyWithImpl<$Res, _$AppStateImpl>
    implements _$$AppStateImplCopyWith<$Res> {
  __$$AppStateImplCopyWithImpl(
      _$AppStateImpl _value, $Res Function(_$AppStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isBNBVisible = null,
    Object? locale = null,
    Object? deepLinkQRCodeToken = freezed,
    Object? isUserLoaded = freezed,
    Object? isRestaurantsLoaded = freezed,
    Object? isUserDeactivated = freezed,
    Object? storedOrder = freezed,
    Object? storedOrderStatus = null,
    Object? lateReviewNotification = freezed,
  }) {
    return _then(_$AppStateImpl(
      isBNBVisible: null == isBNBVisible
          ? _value.isBNBVisible
          : isBNBVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      locale: null == locale
          ? _value.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as AppLocale,
      deepLinkQRCodeToken: freezed == deepLinkQRCodeToken
          ? _value.deepLinkQRCodeToken
          : deepLinkQRCodeToken // ignore: cast_nullable_to_non_nullable
              as String?,
      isUserLoaded:
          freezed == isUserLoaded ? _value.isUserLoaded! : isUserLoaded,
      isRestaurantsLoaded: freezed == isRestaurantsLoaded
          ? _value.isRestaurantsLoaded!
          : isRestaurantsLoaded,
      isUserDeactivated: freezed == isUserDeactivated
          ? _value.isUserDeactivated!
          : isUserDeactivated,
      storedOrder: freezed == storedOrder
          ? _value.storedOrder
          : storedOrder // ignore: cast_nullable_to_non_nullable
              as StoredOrderModel?,
      storedOrderStatus: null == storedOrderStatus
          ? _value.storedOrderStatus
          : storedOrderStatus // ignore: cast_nullable_to_non_nullable
              as StoredOrderStatus,
      lateReviewNotification: freezed == lateReviewNotification
          ? _value.lateReviewNotification
          : lateReviewNotification // ignore: cast_nullable_to_non_nullable
              as LateReviewNotification?,
    ));
  }
}

/// @nodoc

class _$AppStateImpl implements _AppState {
  const _$AppStateImpl(
      {this.isBNBVisible = true,
      this.locale = AppLocale.de,
      required this.deepLinkQRCodeToken,
      this.isUserLoaded = false,
      this.isRestaurantsLoaded = false,
      this.isUserDeactivated = false,
      this.storedOrder,
      this.storedOrderStatus = StoredOrderStatus.idle,
      this.lateReviewNotification});

  @override
  @JsonKey()
  final bool isBNBVisible;
  @override
  @JsonKey()
  final AppLocale locale;
  @override
  final String? deepLinkQRCodeToken;
  @override
  @JsonKey()
  final dynamic isUserLoaded;
  @override
  @JsonKey()
  final dynamic isRestaurantsLoaded;
  @override
  @JsonKey()
  final dynamic isUserDeactivated;
  @override
  final StoredOrderModel? storedOrder;
  @override
  @JsonKey()
  final StoredOrderStatus storedOrderStatus;
  @override
  final LateReviewNotification? lateReviewNotification;

  @override
  String toString() {
    return 'AppState(isBNBVisible: $isBNBVisible, locale: $locale, deepLinkQRCodeToken: $deepLinkQRCodeToken, isUserLoaded: $isUserLoaded, isRestaurantsLoaded: $isRestaurantsLoaded, isUserDeactivated: $isUserDeactivated, storedOrder: $storedOrder, storedOrderStatus: $storedOrderStatus, lateReviewNotification: $lateReviewNotification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppStateImpl &&
            (identical(other.isBNBVisible, isBNBVisible) ||
                other.isBNBVisible == isBNBVisible) &&
            (identical(other.locale, locale) || other.locale == locale) &&
            (identical(other.deepLinkQRCodeToken, deepLinkQRCodeToken) ||
                other.deepLinkQRCodeToken == deepLinkQRCodeToken) &&
            const DeepCollectionEquality()
                .equals(other.isUserLoaded, isUserLoaded) &&
            const DeepCollectionEquality()
                .equals(other.isRestaurantsLoaded, isRestaurantsLoaded) &&
            const DeepCollectionEquality()
                .equals(other.isUserDeactivated, isUserDeactivated) &&
            (identical(other.storedOrder, storedOrder) ||
                other.storedOrder == storedOrder) &&
            (identical(other.storedOrderStatus, storedOrderStatus) ||
                other.storedOrderStatus == storedOrderStatus) &&
            (identical(other.lateReviewNotification, lateReviewNotification) ||
                other.lateReviewNotification == lateReviewNotification));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isBNBVisible,
      locale,
      deepLinkQRCodeToken,
      const DeepCollectionEquality().hash(isUserLoaded),
      const DeepCollectionEquality().hash(isRestaurantsLoaded),
      const DeepCollectionEquality().hash(isUserDeactivated),
      storedOrder,
      storedOrderStatus,
      lateReviewNotification);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppStateImplCopyWith<_$AppStateImpl> get copyWith =>
      __$$AppStateImplCopyWithImpl<_$AppStateImpl>(this, _$identity);
}

abstract class _AppState implements AppState {
  const factory _AppState(
      {final bool isBNBVisible,
      final AppLocale locale,
      required final String? deepLinkQRCodeToken,
      final dynamic isUserLoaded,
      final dynamic isRestaurantsLoaded,
      final dynamic isUserDeactivated,
      final StoredOrderModel? storedOrder,
      final StoredOrderStatus storedOrderStatus,
      final LateReviewNotification? lateReviewNotification}) = _$AppStateImpl;

  @override
  bool get isBNBVisible;
  @override
  AppLocale get locale;
  @override
  String? get deepLinkQRCodeToken;
  @override
  dynamic get isUserLoaded;
  @override
  dynamic get isRestaurantsLoaded;
  @override
  dynamic get isUserDeactivated;
  @override
  StoredOrderModel? get storedOrder;
  @override
  StoredOrderStatus get storedOrderStatus;
  @override
  LateReviewNotification? get lateReviewNotification;
  @override
  @JsonKey(ignore: true)
  _$$AppStateImplCopyWith<_$AppStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
