part of 'add_post_bloc.dart';

@freezed
class AddPostEvent with _$AddPostEvent {
  const factory AddPostEvent.changePostImage(File image) = _ChangePostImage;
  const factory AddPostEvent.changeRestaurant(RestaurantModel restaurant) = _ChangeRestaurant;
  const factory AddPostEvent.changeFriends(String friend) = _ChangeFriends;
  const factory AddPostEvent.changeDescription(String description) = _ChangeDescription;
  const factory AddPostEvent.submit() = _Submit;
}
