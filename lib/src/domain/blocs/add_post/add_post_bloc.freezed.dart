// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_post_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddPostEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddPostEventCopyWith<$Res> {
  factory $AddPostEventCopyWith(
          AddPostEvent value, $Res Function(AddPostEvent) then) =
      _$AddPostEventCopyWithImpl<$Res, AddPostEvent>;
}

/// @nodoc
class _$AddPostEventCopyWithImpl<$Res, $Val extends AddPostEvent>
    implements $AddPostEventCopyWith<$Res> {
  _$AddPostEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ChangePostImageImplCopyWith<$Res> {
  factory _$$ChangePostImageImplCopyWith(_$ChangePostImageImpl value,
          $Res Function(_$ChangePostImageImpl) then) =
      __$$ChangePostImageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({File image});
}

/// @nodoc
class __$$ChangePostImageImplCopyWithImpl<$Res>
    extends _$AddPostEventCopyWithImpl<$Res, _$ChangePostImageImpl>
    implements _$$ChangePostImageImplCopyWith<$Res> {
  __$$ChangePostImageImplCopyWithImpl(
      _$ChangePostImageImpl _value, $Res Function(_$ChangePostImageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = null,
  }) {
    return _then(_$ChangePostImageImpl(
      null == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as File,
    ));
  }
}

/// @nodoc

class _$ChangePostImageImpl implements _ChangePostImage {
  const _$ChangePostImageImpl(this.image);

  @override
  final File image;

  @override
  String toString() {
    return 'AddPostEvent.changePostImage(image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangePostImageImpl &&
            (identical(other.image, image) || other.image == image));
  }

  @override
  int get hashCode => Object.hash(runtimeType, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangePostImageImplCopyWith<_$ChangePostImageImpl> get copyWith =>
      __$$ChangePostImageImplCopyWithImpl<_$ChangePostImageImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) {
    return changePostImage(image);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) {
    return changePostImage?.call(image);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) {
    if (changePostImage != null) {
      return changePostImage(image);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) {
    return changePostImage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) {
    return changePostImage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) {
    if (changePostImage != null) {
      return changePostImage(this);
    }
    return orElse();
  }
}

abstract class _ChangePostImage implements AddPostEvent {
  const factory _ChangePostImage(final File image) = _$ChangePostImageImpl;

  File get image;
  @JsonKey(ignore: true)
  _$$ChangePostImageImplCopyWith<_$ChangePostImageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeRestaurantImplCopyWith<$Res> {
  factory _$$ChangeRestaurantImplCopyWith(_$ChangeRestaurantImpl value,
          $Res Function(_$ChangeRestaurantImpl) then) =
      __$$ChangeRestaurantImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RestaurantModel restaurant});

  $RestaurantModelCopyWith<$Res> get restaurant;
}

/// @nodoc
class __$$ChangeRestaurantImplCopyWithImpl<$Res>
    extends _$AddPostEventCopyWithImpl<$Res, _$ChangeRestaurantImpl>
    implements _$$ChangeRestaurantImplCopyWith<$Res> {
  __$$ChangeRestaurantImplCopyWithImpl(_$ChangeRestaurantImpl _value,
      $Res Function(_$ChangeRestaurantImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? restaurant = null,
  }) {
    return _then(_$ChangeRestaurantImpl(
      null == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as RestaurantModel,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RestaurantModelCopyWith<$Res> get restaurant {
    return $RestaurantModelCopyWith<$Res>(_value.restaurant, (value) {
      return _then(_value.copyWith(restaurant: value));
    });
  }
}

/// @nodoc

class _$ChangeRestaurantImpl implements _ChangeRestaurant {
  const _$ChangeRestaurantImpl(this.restaurant);

  @override
  final RestaurantModel restaurant;

  @override
  String toString() {
    return 'AddPostEvent.changeRestaurant(restaurant: $restaurant)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeRestaurantImpl &&
            (identical(other.restaurant, restaurant) ||
                other.restaurant == restaurant));
  }

  @override
  int get hashCode => Object.hash(runtimeType, restaurant);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeRestaurantImplCopyWith<_$ChangeRestaurantImpl> get copyWith =>
      __$$ChangeRestaurantImplCopyWithImpl<_$ChangeRestaurantImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) {
    return changeRestaurant(restaurant);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) {
    return changeRestaurant?.call(restaurant);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) {
    if (changeRestaurant != null) {
      return changeRestaurant(restaurant);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) {
    return changeRestaurant(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) {
    return changeRestaurant?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) {
    if (changeRestaurant != null) {
      return changeRestaurant(this);
    }
    return orElse();
  }
}

abstract class _ChangeRestaurant implements AddPostEvent {
  const factory _ChangeRestaurant(final RestaurantModel restaurant) =
      _$ChangeRestaurantImpl;

  RestaurantModel get restaurant;
  @JsonKey(ignore: true)
  _$$ChangeRestaurantImplCopyWith<_$ChangeRestaurantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeFriendsImplCopyWith<$Res> {
  factory _$$ChangeFriendsImplCopyWith(
          _$ChangeFriendsImpl value, $Res Function(_$ChangeFriendsImpl) then) =
      __$$ChangeFriendsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String friend});
}

/// @nodoc
class __$$ChangeFriendsImplCopyWithImpl<$Res>
    extends _$AddPostEventCopyWithImpl<$Res, _$ChangeFriendsImpl>
    implements _$$ChangeFriendsImplCopyWith<$Res> {
  __$$ChangeFriendsImplCopyWithImpl(
      _$ChangeFriendsImpl _value, $Res Function(_$ChangeFriendsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? friend = null,
  }) {
    return _then(_$ChangeFriendsImpl(
      null == friend
          ? _value.friend
          : friend // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeFriendsImpl implements _ChangeFriends {
  const _$ChangeFriendsImpl(this.friend);

  @override
  final String friend;

  @override
  String toString() {
    return 'AddPostEvent.changeFriends(friend: $friend)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeFriendsImpl &&
            (identical(other.friend, friend) || other.friend == friend));
  }

  @override
  int get hashCode => Object.hash(runtimeType, friend);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeFriendsImplCopyWith<_$ChangeFriendsImpl> get copyWith =>
      __$$ChangeFriendsImplCopyWithImpl<_$ChangeFriendsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) {
    return changeFriends(friend);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) {
    return changeFriends?.call(friend);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) {
    if (changeFriends != null) {
      return changeFriends(friend);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) {
    return changeFriends(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) {
    return changeFriends?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) {
    if (changeFriends != null) {
      return changeFriends(this);
    }
    return orElse();
  }
}

abstract class _ChangeFriends implements AddPostEvent {
  const factory _ChangeFriends(final String friend) = _$ChangeFriendsImpl;

  String get friend;
  @JsonKey(ignore: true)
  _$$ChangeFriendsImplCopyWith<_$ChangeFriendsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeDescriptionImplCopyWith<$Res> {
  factory _$$ChangeDescriptionImplCopyWith(_$ChangeDescriptionImpl value,
          $Res Function(_$ChangeDescriptionImpl) then) =
      __$$ChangeDescriptionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String description});
}

/// @nodoc
class __$$ChangeDescriptionImplCopyWithImpl<$Res>
    extends _$AddPostEventCopyWithImpl<$Res, _$ChangeDescriptionImpl>
    implements _$$ChangeDescriptionImplCopyWith<$Res> {
  __$$ChangeDescriptionImplCopyWithImpl(_$ChangeDescriptionImpl _value,
      $Res Function(_$ChangeDescriptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = null,
  }) {
    return _then(_$ChangeDescriptionImpl(
      null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ChangeDescriptionImpl implements _ChangeDescription {
  const _$ChangeDescriptionImpl(this.description);

  @override
  final String description;

  @override
  String toString() {
    return 'AddPostEvent.changeDescription(description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeDescriptionImpl &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @override
  int get hashCode => Object.hash(runtimeType, description);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeDescriptionImplCopyWith<_$ChangeDescriptionImpl> get copyWith =>
      __$$ChangeDescriptionImplCopyWithImpl<_$ChangeDescriptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) {
    return changeDescription(description);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) {
    return changeDescription?.call(description);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) {
    if (changeDescription != null) {
      return changeDescription(description);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) {
    return changeDescription(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) {
    return changeDescription?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) {
    if (changeDescription != null) {
      return changeDescription(this);
    }
    return orElse();
  }
}

abstract class _ChangeDescription implements AddPostEvent {
  const factory _ChangeDescription(final String description) =
      _$ChangeDescriptionImpl;

  String get description;
  @JsonKey(ignore: true)
  _$$ChangeDescriptionImplCopyWith<_$ChangeDescriptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmitImplCopyWith<$Res> {
  factory _$$SubmitImplCopyWith(
          _$SubmitImpl value, $Res Function(_$SubmitImpl) then) =
      __$$SubmitImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmitImplCopyWithImpl<$Res>
    extends _$AddPostEventCopyWithImpl<$Res, _$SubmitImpl>
    implements _$$SubmitImplCopyWith<$Res> {
  __$$SubmitImplCopyWithImpl(
      _$SubmitImpl _value, $Res Function(_$SubmitImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SubmitImpl implements _Submit {
  const _$SubmitImpl();

  @override
  String toString() {
    return 'AddPostEvent.submit()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmitImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(File image) changePostImage,
    required TResult Function(RestaurantModel restaurant) changeRestaurant,
    required TResult Function(String friend) changeFriends,
    required TResult Function(String description) changeDescription,
    required TResult Function() submit,
  }) {
    return submit();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(File image)? changePostImage,
    TResult? Function(RestaurantModel restaurant)? changeRestaurant,
    TResult? Function(String friend)? changeFriends,
    TResult? Function(String description)? changeDescription,
    TResult? Function()? submit,
  }) {
    return submit?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(File image)? changePostImage,
    TResult Function(RestaurantModel restaurant)? changeRestaurant,
    TResult Function(String friend)? changeFriends,
    TResult Function(String description)? changeDescription,
    TResult Function()? submit,
    required TResult orElse(),
  }) {
    if (submit != null) {
      return submit();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ChangePostImage value) changePostImage,
    required TResult Function(_ChangeRestaurant value) changeRestaurant,
    required TResult Function(_ChangeFriends value) changeFriends,
    required TResult Function(_ChangeDescription value) changeDescription,
    required TResult Function(_Submit value) submit,
  }) {
    return submit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ChangePostImage value)? changePostImage,
    TResult? Function(_ChangeRestaurant value)? changeRestaurant,
    TResult? Function(_ChangeFriends value)? changeFriends,
    TResult? Function(_ChangeDescription value)? changeDescription,
    TResult? Function(_Submit value)? submit,
  }) {
    return submit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ChangePostImage value)? changePostImage,
    TResult Function(_ChangeRestaurant value)? changeRestaurant,
    TResult Function(_ChangeFriends value)? changeFriends,
    TResult Function(_ChangeDescription value)? changeDescription,
    TResult Function(_Submit value)? submit,
    required TResult orElse(),
  }) {
    if (submit != null) {
      return submit(this);
    }
    return orElse();
  }
}

abstract class _Submit implements AddPostEvent {
  const factory _Submit() = _$SubmitImpl;
}

/// @nodoc
mixin _$AddPostState {
  File? get image => throw _privateConstructorUsedError;
  RestaurantModel? get restaurant => throw _privateConstructorUsedError;
  Set<String>? get friends => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  PostStatus get status => throw _privateConstructorUsedError;
  String? get postError => throw _privateConstructorUsedError;
  String? get restaurantError => throw _privateConstructorUsedError;
  String? get imageError => throw _privateConstructorUsedError;
  String? get descriptionError => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AddPostStateCopyWith<AddPostState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddPostStateCopyWith<$Res> {
  factory $AddPostStateCopyWith(
          AddPostState value, $Res Function(AddPostState) then) =
      _$AddPostStateCopyWithImpl<$Res, AddPostState>;
  @useResult
  $Res call(
      {File? image,
      RestaurantModel? restaurant,
      Set<String>? friends,
      String description,
      PostStatus status,
      String? postError,
      String? restaurantError,
      String? imageError,
      String? descriptionError});

  $RestaurantModelCopyWith<$Res>? get restaurant;
}

/// @nodoc
class _$AddPostStateCopyWithImpl<$Res, $Val extends AddPostState>
    implements $AddPostStateCopyWith<$Res> {
  _$AddPostStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? restaurant = freezed,
    Object? friends = freezed,
    Object? description = null,
    Object? status = null,
    Object? postError = freezed,
    Object? restaurantError = freezed,
    Object? imageError = freezed,
    Object? descriptionError = freezed,
  }) {
    return _then(_value.copyWith(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as File?,
      restaurant: freezed == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as RestaurantModel?,
      friends: freezed == friends
          ? _value.friends
          : friends // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as PostStatus,
      postError: freezed == postError
          ? _value.postError
          : postError // ignore: cast_nullable_to_non_nullable
              as String?,
      restaurantError: freezed == restaurantError
          ? _value.restaurantError
          : restaurantError // ignore: cast_nullable_to_non_nullable
              as String?,
      imageError: freezed == imageError
          ? _value.imageError
          : imageError // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionError: freezed == descriptionError
          ? _value.descriptionError
          : descriptionError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RestaurantModelCopyWith<$Res>? get restaurant {
    if (_value.restaurant == null) {
      return null;
    }

    return $RestaurantModelCopyWith<$Res>(_value.restaurant!, (value) {
      return _then(_value.copyWith(restaurant: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AddPostStateImplCopyWith<$Res>
    implements $AddPostStateCopyWith<$Res> {
  factory _$$AddPostStateImplCopyWith(
          _$AddPostStateImpl value, $Res Function(_$AddPostStateImpl) then) =
      __$$AddPostStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {File? image,
      RestaurantModel? restaurant,
      Set<String>? friends,
      String description,
      PostStatus status,
      String? postError,
      String? restaurantError,
      String? imageError,
      String? descriptionError});

  @override
  $RestaurantModelCopyWith<$Res>? get restaurant;
}

/// @nodoc
class __$$AddPostStateImplCopyWithImpl<$Res>
    extends _$AddPostStateCopyWithImpl<$Res, _$AddPostStateImpl>
    implements _$$AddPostStateImplCopyWith<$Res> {
  __$$AddPostStateImplCopyWithImpl(
      _$AddPostStateImpl _value, $Res Function(_$AddPostStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? image = freezed,
    Object? restaurant = freezed,
    Object? friends = freezed,
    Object? description = null,
    Object? status = null,
    Object? postError = freezed,
    Object? restaurantError = freezed,
    Object? imageError = freezed,
    Object? descriptionError = freezed,
  }) {
    return _then(_$AddPostStateImpl(
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as File?,
      restaurant: freezed == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as RestaurantModel?,
      friends: freezed == friends
          ? _value._friends
          : friends // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as PostStatus,
      postError: freezed == postError
          ? _value.postError
          : postError // ignore: cast_nullable_to_non_nullable
              as String?,
      restaurantError: freezed == restaurantError
          ? _value.restaurantError
          : restaurantError // ignore: cast_nullable_to_non_nullable
              as String?,
      imageError: freezed == imageError
          ? _value.imageError
          : imageError // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionError: freezed == descriptionError
          ? _value.descriptionError
          : descriptionError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AddPostStateImpl implements _AddPostState {
  const _$AddPostStateImpl(
      {this.image,
      this.restaurant,
      final Set<String>? friends,
      this.description = '',
      this.status = PostStatus.idle,
      this.postError,
      this.restaurantError,
      this.imageError,
      this.descriptionError})
      : _friends = friends;

  @override
  final File? image;
  @override
  final RestaurantModel? restaurant;
  final Set<String>? _friends;
  @override
  Set<String>? get friends {
    final value = _friends;
    if (value == null) return null;
    if (_friends is EqualUnmodifiableSetView) return _friends;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(value);
  }

  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final PostStatus status;
  @override
  final String? postError;
  @override
  final String? restaurantError;
  @override
  final String? imageError;
  @override
  final String? descriptionError;

  @override
  String toString() {
    return 'AddPostState(image: $image, restaurant: $restaurant, friends: $friends, description: $description, status: $status, postError: $postError, restaurantError: $restaurantError, imageError: $imageError, descriptionError: $descriptionError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddPostStateImpl &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.restaurant, restaurant) ||
                other.restaurant == restaurant) &&
            const DeepCollectionEquality().equals(other._friends, _friends) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.postError, postError) ||
                other.postError == postError) &&
            (identical(other.restaurantError, restaurantError) ||
                other.restaurantError == restaurantError) &&
            (identical(other.imageError, imageError) ||
                other.imageError == imageError) &&
            (identical(other.descriptionError, descriptionError) ||
                other.descriptionError == descriptionError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      image,
      restaurant,
      const DeepCollectionEquality().hash(_friends),
      description,
      status,
      postError,
      restaurantError,
      imageError,
      descriptionError);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AddPostStateImplCopyWith<_$AddPostStateImpl> get copyWith =>
      __$$AddPostStateImplCopyWithImpl<_$AddPostStateImpl>(this, _$identity);
}

abstract class _AddPostState implements AddPostState {
  const factory _AddPostState(
      {final File? image,
      final RestaurantModel? restaurant,
      final Set<String>? friends,
      final String description,
      final PostStatus status,
      final String? postError,
      final String? restaurantError,
      final String? imageError,
      final String? descriptionError}) = _$AddPostStateImpl;

  @override
  File? get image;
  @override
  RestaurantModel? get restaurant;
  @override
  Set<String>? get friends;
  @override
  String get description;
  @override
  PostStatus get status;
  @override
  String? get postError;
  @override
  String? get restaurantError;
  @override
  String? get imageError;
  @override
  String? get descriptionError;
  @override
  @JsonKey(ignore: true)
  _$$AddPostStateImplCopyWith<_$AddPostStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
