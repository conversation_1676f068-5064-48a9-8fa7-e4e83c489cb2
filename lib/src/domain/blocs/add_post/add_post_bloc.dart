import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/models/models.dart';

part 'add_post_event.dart';
part 'add_post_state.dart';
part 'add_post_bloc.freezed.dart';

class AddPostBloc extends Bloc<AddPostEvent, AddPostState> with ValidationMixin {
  final AddPostUseCase _restaurantPostRepository;
  final AddPostCoverUseCase _restaurantPostCoverUseCase;
  final PostUpdatesService _postUpdatesService;
  final MLogger _logger;

  AddPostBloc(
    this._restaurantPostRepository,
    this._restaurantPostCoverUseCase,
    this._postUpdatesService,
    this._logger
  ) : super(const AddPostState()) {
    on<_ChangePostImage>(_changePostImage);
    on<_ChangeRestaurant>(_changeRestaurant);
    on<_ChangeFriends>(_changeFriends);
    on<_ChangeDescription>(_changeDescription);
    on<_Submit>(_submit);
  }

  void _changePostImage(_ChangePostImage event, Emitter<AddPostState> emit) {
    emit(state.copyWith(
      image: event.image,
      imageError: null,
    ));
  }

  void _changeRestaurant(_ChangeRestaurant event, Emitter<AddPostState> emit) {
    emit(state.copyWith(
      restaurant: event.restaurant,
      restaurantError: null,
    ));
  }

  void _changeFriends(_ChangeFriends event, Emitter<AddPostState> emit) {
    final friends = Set<String>.from(state.friends ?? {});
    friends.add(event.friend);
    emit(state.copyWith(friends: friends));
  }

  void _changeDescription(_ChangeDescription event, Emitter<AddPostState> emit) {
    emit(state.copyWith(
      description: event.description,
      descriptionError: null,
    ));
  }

  Future<void> _submit(_Submit event, Emitter<AddPostState> emit) async {
    final restaurantId = state.restaurant?.id;
    final restaurantError = validatePlainValue(restaurantId);

    final imageError = validateImageValue(state.image);
    if (imageError != null) {
      emit(state.copyWith(imageError: imageError));
      return;
    }

    if (restaurantError != null) {
      emit(state.copyWith(restaurantError: restaurantError));
      return;
    }

    final descriptionError = validatePlainValue(state.description);
    if (descriptionError != null) {
      emit(state.copyWith(descriptionError: descriptionError));
      return;
    }

    try {
      emit(state.copyWith(
        status: PostStatus.loading,
        postError: null,
        restaurantError: null,
        descriptionError: null,
        imageError: null,
      ));

      final postResponse = await _restaurantPostRepository(
        restaurantId: restaurantId!,
        friends: state.friends?.toList(),
        description: state.description,
      );
      if (state.image != null) {
        await _restaurantPostCoverUseCase(postId: postResponse.data.id, coverImage: state.image!);
      }

      /// Notify the UI that the posts need to be updated
      _postUpdatesService.needToUpdatePosts(true);

      emit(state.copyWith(status: PostStatus.success));
    } on DioException catch (e, s) {
      _logger.error('Error while adding restaurant post ${e.message}, $s');
      emit(state.copyWith(status: PostStatus.error, postError: e.message));
    } catch (e, s) {
      _logger.error('Error while adding restaurant post $e $s');
      emit(state.copyWith(status: PostStatus.error, postError: e.toString()));
    }

    emit(state.copyWith(status: PostStatus.idle));
  }
}
