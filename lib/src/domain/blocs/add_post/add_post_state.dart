part of 'add_post_bloc.dart';

@freezed
sealed class AddPostState with _$AddPostState {
  const factory AddPostState({
    File? image,
    RestaurantModel? restaurant,
    Set<String>? friends,
    @Default('') String description,
    @Default(PostStatus.idle) PostStatus status,
    String? postError,
    String? restaurantError,
    String? imageError,
    String? descriptionError,
  }) = _AddPostState;
}

enum PostStatus {idle, loading, success, error}
