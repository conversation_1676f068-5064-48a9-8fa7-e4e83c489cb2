import '../models/requests/tutorial/tutorial_states_request.dart';
import '../models/responses/tutorial/tutorial_states_response.dart';
import '../models/responses/tutorial/tutorial_states_update_response.dart';

abstract class TutorialRepository {
  Future<TutorialStatesResponse> getTutorialStates();
  Future<TutorialStatesUpdateResponse> updateTutorialStates(
    TutorialStatesRequest request,
  );
  Future<void> resetAllTutorialStates();
}