import 'package:mutualz/src/domain/models/models.dart';

abstract interface class OrderRepository {
  Future<OrderResponse> createOrder(OrderRequestModel request);
  Future<OrderResponse> updateOrder(String id, OrderRequestModel request);
  Future<OrdersResponse> fetchOrder(String? restaurantId, PaymentStatus? status, SortType? sort);
  Future<PaymentIntentResponse> fetchPaymentIntent(String restaurantId);
  Future<EarnPointsResponse> earnPoints(String orderId);
  Future<PrecalculatedOrderResponse> precalculateOrder(PrecalculatedOrderRequest request);
  Future<void> attachCoupon(String orderId, String couponId);
  Future<void> detachCoupon(String orderId);
  Future<void> createStoredOrder(StoredOrderModel model);
  Future<StoredOrderModel?> readStoredOrder();
  Future<void> clearStoredOrder();
  Future<void> createStoredCart(StoredCartModel model);
  Future<StoredCartModel?> readStoredCart();
  Future<void> clearStoredCart();
  Future<void> notifyServerForCouponsNotification();
  Future<void> sendInvoice(String orderId);
}