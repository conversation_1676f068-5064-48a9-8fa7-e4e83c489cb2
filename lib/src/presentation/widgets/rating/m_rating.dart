import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class MRating extends StatelessWidget {
  final double rating;
  final double starSize;
  final EdgeInsets padding;
  final bool hasLabel;
  final Function(double)? onRatingUpdate;
  final EdgeInsetsGeometry itemPadding;
  final bool isTutorial;

  const MRating({
    this.rating = 0.0,
    this.starSize = 14.0,
    this.padding = EdgeInsets.zero,
    this.hasLabel = true,
    this.onRatingUpdate,
    this.itemPadding = EdgeInsets.zero,
    this.isTutorial = false,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: isTutorial ? BoxDecoration(
        color: MColors.goldenRod,
        borderRadius: BorderRadius.circular(12),
      ) : null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RatingBar.builder(
            initialRating: rating,
            maxRating: 5.0,
            minRating: 0,
            direction: Axis.horizontal,
            allowHalfRating: false,
            itemCount: 5,
            unratedColor: MColors.silverMist,
            itemPadding: itemPadding,
            itemSize: starSize,
            ignoreGestures: onRatingUpdate == null ? true : false,
            itemBuilder: (context, _) => Icon(
              Icons.star,
              color: MColors.scarletEmber,
              size: starSize,
            ),
            onRatingUpdate: onRatingUpdate ?? (rating) {},
          ),
          Visibility(
            visible: hasLabel,
            child: Padding(
              padding: const EdgeInsets.only(left: 6.0),
              child: Text(
                rating.withComa,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  height: 1.25,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
