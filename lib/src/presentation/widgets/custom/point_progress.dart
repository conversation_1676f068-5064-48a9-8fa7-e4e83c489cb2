import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/models/constants/loyalty_status.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class PointProgress extends StatefulWidget {
  final LinearGradient gradient;
  final double width;
  final String label;
  final String oppositeLabel;
  final String svgAsset;
  final String oppositeSvgAsset;
  final Color color;
  final Color oppositeColor;
  final Color extraOppositeColor;
  final Color shadowColor;
  final double internalGap;
  final double from;
  final double to;
  final EdgeInsets margin;
  final bool isReached;
  final String? status;
  final bool showBottomText;

  const PointProgress({
    this.gradient = const LinearGradient(
      stops: [0.0, 1.0],
      colors: [MColors.goldenRod, MColors.pumpkin]
    ),
    this.from = 0.0,
    this.to = 0.0,
    this.width = 240.0,
    this.label = '',
    this.oppositeLabel = '',
    this.svgAsset = '',
    this.oppositeSvgAsset = '',
    this.color = Colors.transparent,
    this.oppositeColor = Colors.transparent,
    this.extraOppositeColor = Colors.transparent,
    this.shadowColor = Colors.transparent,
    this.internalGap = 20.0,
    this.margin = const EdgeInsets.only(bottom: 24.0),
    this.isReached = false,
    this.status,
    this.showBottomText = true,
    super.key
  }) : assert(to >= 0.0 && from >= 0.0);

   PointProgress.silverToGold({
     this.from = 0.0,
     this.to = 0.0,
     this.width = 240.0,
     this.internalGap = 20.0,
     this.margin = const EdgeInsets.only(bottom: 24.0),
     this.isReached = false,
     this.status,
     this.showBottomText = true,
     super.key,
   }) : assert(to >= 0.0 && from >= 0.0),
      gradient = const LinearGradient(
      stops: [0.0, 1.0],
      colors: [MColors.goldenRod, MColors.pumpkin]
    ),
    label = t.custom.pointProgress.silver,
    oppositeLabel = t.custom.pointProgress.gold,
    svgAsset = MAssets.iconSilver,
    oppositeSvgAsset = MAssets.iconGold,
    color = MColors.onyxShadow,
    oppositeColor = MColors.goldenRod,
    extraOppositeColor = MColors.pumpkin,
    shadowColor = MColors.onyxShadow;

  PointProgress.memberToSilver({
    this.from = 0.0,
    this.to = 0.0,
    this.width = 240.0,
    this.internalGap = 20.0,
    this.margin = const EdgeInsets.only(bottom: 24.0),
    this.isReached = false,
    this.status,
    this.showBottomText = true,
    super.key,
  }) : assert(to >= 0.0 && from >= 0.0),
    gradient = const LinearGradient(
      stops: [0.0, 1.0],
      colors: [MColors.coolGray, MColors.charcoalBlack]
    ),
    label = t.custom.pointProgress.member,
    oppositeLabel = t.custom.pointProgress.silver,
    svgAsset = MAssets.iconMember,
    oppositeSvgAsset = MAssets.iconSilver,
    color = MColors.scarletEmber,
    oppositeColor = MColors.onyxShadow,
    extraOppositeColor = MColors.onyxShadow,
    shadowColor = MColors.onyxShadow;

  PointProgress.testToMember({
    this.from = 0.0,
    this.to = 0.0,
    this.width = 240.0,
    this.internalGap = 20.0,
    this.margin = const EdgeInsets.only(bottom: 24.0),
    this.isReached = false,
    this.status,
    this.showBottomText = true,
    super.key,
  }) : assert(to >= 0.0 && from >= 0.0),
    gradient = const LinearGradient(
      stops: [0.0, 1.0],
      colors: [MColors.blushPink, MColors.scarletEmber]
    ),
    label = t.custom.pointProgress.test,
    oppositeLabel = t.custom.pointProgress.member,
    svgAsset = MAssets.iconTest,
    oppositeSvgAsset = MAssets.iconMember,
    color = MColors.azureSky,
    oppositeColor = MColors.scarletEmber,
    extraOppositeColor = MColors.scarletEmber,
    shadowColor = MColors.onyxShadow.withOpacity(.25);

  @override
  State<PointProgress> createState() => _PointProgressState();
}

class _PointProgressState extends State<PointProgress> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: widget.from / widget.to, end: widget.from / widget.to).animate(_controller)
      ..addListener(() {
        setState(() {});
      });
    _controller.forward();
  }

  @override
  void didUpdateWidget(covariant PointProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    var previousAnimatedValue = _animation.value;
    if (oldWidget.status != widget.status && widget.status != LoyaltyStatus.gold.name) {
      previousAnimatedValue = 0.0;
    }

    if (oldWidget.to != widget.to || oldWidget.from != widget.from) {
      _controller.reset();
      _animation = Tween<double>(
        begin: previousAnimatedValue,
        end: widget.from / widget.to,
      ).animate(_controller)
        ..addListener(() {
          setState(() {});
        });
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: widget.width,
      ),
      margin: widget.margin,
      child: Row(
        children: [
          Text(
            widget.label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: widget.isReached ? Colors.transparent : MColors.slateGrayMist,
            ),
          ),
          SizedBox(width: widget.internalGap),
          Expanded(
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(7.0),
                  child: CustomPaint(
                    foregroundPainter: GradientProgressPainter(
                      gradient: widget.gradient,
                      value: (_animation.value >= 0.0 && _animation.value <= 100)
                          ? _animation.value
                          : 0.0,
                    ),
                    child: LinearProgressIndicator(
                      backgroundColor: MColors.silverMist,
                      value: (_animation.value >= 0.0 && _animation.value <= 100)
                          ? _animation.value
                          : 0.0,
                      borderRadius: BorderRadius.circular(7.0),
                      minHeight: 10.0,
                    ),
                  ),
                ),
                Visibility(
                  visible: !widget.isReached,
                  child: Positioned(
                    top: -9.0,
                    left: -14.0,
                    child: Container(
                      width: 28.0,
                      height: 28.0,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: widget.color,
                        boxShadow: widget.shadowColor != Colors.transparent ? [
                          BoxShadow(
                            color: widget.shadowColor.withOpacity(.2),
                            blurRadius: 8.0,
                            offset: const Offset(0, 0),
                          ),
                        ] : null,
                      ),
                      child: Visibility(
                        visible: widget.svgAsset.isNotEmpty,
                        child: Center(
                          child: SvgPicture.asset(
                            widget.svgAsset,
                            width: 15.0,
                            height: 12.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                _PositionedWrapped(
                  isReached: widget.isReached,
                  child: Container(
                    width: 28.0,
                    height: 28.0,
                    transform: Matrix4.translationValues(0, widget.isReached ? 14.0 : 0.0, 0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        stops: const [0.0, 1.0],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [widget.oppositeColor, widget.extraOppositeColor]
                      ),
                      boxShadow: widget.shadowColor != Colors.transparent ? [
                        BoxShadow(
                          color: widget.shadowColor.withOpacity(.2),
                          blurRadius: 8.0,
                          offset: const Offset(0, 0),
                        ),
                      ] : null,
                    ),
                    child: Visibility(
                      visible: widget.oppositeSvgAsset.isNotEmpty,
                      child: Center(
                        child: SvgPicture.asset(
                          widget.oppositeSvgAsset,
                          width: 12.5,
                          height: 12.5,
                        ),
                      ),
                    ),
                  ),
                ),
                if (widget.showBottomText)
                  Positioned(
                    bottom: -32.0,
                    left: 0,
                    right: 0,
                    child: Text(
                      widget.isReached && widget.status != null ?
                        widget.status == LoyaltyStatus.gold.name ? t.custom.youMadeIt : '${widget.status} ${t.custom.isReached}' :
                        t.custom.pointProgress.points(value: widget.from.toInt(), opposite: widget.to.toInt()),
                      style: Theme.of(context).textTheme.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                  )
              ],
            ),
          ),
          SizedBox(width: widget.internalGap),
          Text(
            widget.oppositeLabel,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: widget.isReached ? Colors.transparent : MColors.slateGrayMist,
            ),
          ),
        ],
      ),
    );
  }
}

class _PositionedWrapped extends StatelessWidget {
  final bool isReached;
  final Widget child;

  const _PositionedWrapped({
    required this.child,
    this.isReached = false,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return !isReached ? Positioned(
        top: -9.0,
        right: -14.0,
        child: child,
    ) : Positioned.fill(
      top: -28.0,
      child: Center(
        child: child,
      ),
    );
  }
}


class GradientProgressPainter extends CustomPainter {
  final Gradient gradient;
  final double value;
  final double borderRadius;

  GradientProgressPainter({
    required this.gradient,
    required this.value,
    this.borderRadius = 7.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Rect.fromLTWH(0, 0, size.width * value, size.height);
    RRect rRect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    Paint paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRRect(rRect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}