import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/cards/restaurant_info_tab.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:showcaseview/showcaseview.dart';

class RestaurantButtons extends StatelessWidget {
  final VoidCallback? onDealsTap;
  final VoidCallback? onMenuTap;
  final VoidCallback? onFeedTap;
  final RestaurantInfoTab? active;
  final Color background;
  final List<BoxShadow> shadows;
  final GlobalKey? tutorialDealsKey;
  final GlobalKey? tutorialMenuKey;

  const RestaurantButtons({
    this.tutorialDealsKey,
    this.tutorialMenuKey,
    this.onDealsTap,
    this.onMenuTap,
    this.onFeedTap,
    this.active,
    this.background = Colors.transparent,
    this.shadows = const [],
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 36.0,
      ),
      margin: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 3.0),
      decoration: BoxDecoration(
        color: background,
        borderRadius: BorderRadius.circular(24.0),
        boxShadow: shadows,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: RestaurantInfoTab.values.map((e) {
          final shouldShowTutorial =
              (e == RestaurantInfoTab.deals && tutorialDealsKey != null) || (e == RestaurantInfoTab.menu && tutorialMenuKey != null);

          return Expanded(
            child: InkWell(
              borderRadius: BorderRadius.circular(24.0),
              focusColor: Colors.green,
              onTap: () {
                switch (e) {
                  case RestaurantInfoTab.deals:
                    onDealsTap?.call();
                  case RestaurantInfoTab.menu:
                    onMenuTap?.call();
                  case RestaurantInfoTab.feed:
                    onFeedTap?.call();
                }
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 3.0),
                child: ConditionalWrapper(
                  condition: shouldShowTutorial,
                  wrapper: (child) => TutorialShowcase(
                    showcaseKey: e == RestaurantInfoTab.deals ? tutorialDealsKey! : tutorialMenuKey!,
                    tooltipPosition: TooltipPosition.bottom,
                    container: e == RestaurantInfoTab.deals
                        ? Container(
                            padding: EdgeInsets.only(right: context.width * 0.2, left: context.width * 0.35),
                            alignment: Alignment.centerLeft,
                            child: Transform.translate(
                                offset: const Offset(0, -45),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Transform.translate(
                                      offset: const Offset(-30, 0),
                                      child: SvgPicture.asset(MAssets.tutorialArrow5, height: 80),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(right: 16, bottom: 8),
                                      child: Text(
                                        t.tutorial.restaurant.deals,
                                        style: const TextStyle(fontSize: 14),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          )
                        : Container(
                            padding: EdgeInsets.only(right: 0, left: context.width * 0.61),
                            alignment: Alignment.centerLeft,
                            child: Transform.translate(
                                offset: const Offset(0, -20),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Transform(
                                      transform: Matrix4.identity()
                                      ..translate(-15.0, 0.0, 0.0)
                                      ..scale(-1.0, 1.0),
                                      alignment: Alignment.center,
                                      child: SvgPicture.asset(MAssets.tutorialArrow2, height: 80),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 16, top: 8),
                                      child: Text(
                                        t.tutorial.restaurant.menu,
                                        style: const TextStyle(fontSize: 14),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ),
                    onTap: () {
                      Future.delayed(const Duration(milliseconds: 250), () {
                      final currentTutorialStep = Tutorials.restaurantTutorialStep.value;

                      // If the tutorial is not finished, we need to switch the tab to the other one
                      if (currentTutorialStep > 0) {
                        if (e == RestaurantInfoTab.deals) {
                          onMenuTap?.call();
                        } else if (e == RestaurantInfoTab.menu) {
                          onDealsTap?.call();
                        }
                      }

                      
                        Tutorials.restaurantTutorialStep.value += 1;
                      });
                    },
                    child: child,
                  ),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 7.0),
                    decoration: BoxDecoration(
                      color: e == active ? MColors.scarletEmber : MColors.softCotton,
                      borderRadius: BorderRadius.circular(24.0),
                      boxShadow: [
                        BoxShadow(
                          color: MColors.onyxShadow.withOpacity(.12),
                          offset: const Offset(0, 0),
                          blurRadius: 10.0,
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      e.label.capitalize(),
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            height: 1.25,
                            color: e == active ? MColors.white : MColors.onyxShadow,
                          ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
