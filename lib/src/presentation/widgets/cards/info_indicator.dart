import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class InfoIndicator extends StatelessWidget {
  final String svg;
  final String label;
  final bool isSelected;
  final Color? bgColor;

  const InfoIndicator({
    required this.svg,
    this.label = '',
    this.isSelected = false,
    this.bgColor,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: displayWidth * .12,
      ),
      child: Column(
        children: [
          Container(
            width: 28.0,
            height: 28.0,
            padding: const EdgeInsets.all(4.0),
            decoration: BoxDecoration(
              color: bgColor ?? (isSelected ? MColors.aquaMint : MColors.frostyPearl),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: SvgPicture.asset(
                svg,
                width: 16.0,
                height: 16.0,
                colorFilter: ColorFilter.mode(
                  isSelected ? MColors.onyxShadow : MColors.slateGrayMist,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4.0),
          Text(
            label,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: isSelected ? MColors.onyxShadow : MColors.slateGrayMist,
              fontSize: 8.0,
            ),
            maxLines: 1,
            textAlign: TextAlign.center,
            overflow: TextOverflow.visible,
          ),
        ],
      ),
    );
  }
}
