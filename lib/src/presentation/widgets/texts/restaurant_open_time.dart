import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';

class RestaurantOpenTime extends StatelessWidget {
  final String openingTime;
  final String closingTime;
  final bool hasLabel;
  final bool applyContrastColor;
  final VoidCallback? onInfoTapped;

  const RestaurantOpenTime({
    this.openingTime = '',
    this.closingTime = '',
    this.hasLabel = false,
    this.applyContrastColor = false,
    this.onInfoTapped,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    final l = hasLabel ? '${t.screens.discover.info.timeLabel}   ' : '';
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onInfoTapped,
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minHeight: 20.0,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: Text(
                '$l$openingTime - $closingTime',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: applyContrastColor ? MColors.onyxShadow : MColors.slateGrayMist,
                ),
              ),
            ),
            const SizedBox(width: 8.0),
            ValueListenableBuilder(
              valueListenable: Tutorials.restaurantTutorialStep,
              builder: (context, step, child) {
                return SizedBox(
                  width: 16.0,
                  height: 16.0,
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SvgPicture.asset(
                        MAssets.iconSchedule,
                        width: 16.0,
                        height: 16.0,
                        // colorFilter: const ColorFilter.mode(
                        //   MColors.slateGrayMist,
                        //   BlendMode.srcIn,
                        // ),
                      ),
                      if (step == 0)
                        Positioned(
                          left: -6,
                          top: -6,
                          child: Container(
                            width: 28,
                            height: 28,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: MColors.goldenRod,
                                width: 3,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(width: 4.0),
          ],
        ),
      ),
    );
  }
}
