import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Wrapper builder.
typedef WrapperBuilder = Widget Function(Widget child);

/// Wrap the child with a wrapper if the condition is true.
class ConditionalWrapper extends StatelessWidget {
  const ConditionalWrapper({
    required this.condition,
    required this.wrapper,
    required this.child,
    super.key,
  });

  /// Condition to wrap the child.
  final bool condition;

  /// Wrapper for the child.
  final WrapperBuilder wrapper;

  /// Child to be wrapped.
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return condition ? wrapper(child) : child;
  }
}

/// A generic conditional wrapper that listens to a ValueListenable
/// and wraps the child based on a condition callback.
class ListeningConditionalWrapper<T> extends StatelessWidget {
  const ListeningConditionalWrapper({
    super.key,
    required this.listenable,
    required this.condition,
    required this.wrapper,
    required this.child,
  });

  /// The ValueListenable to listen to.
  final ValueListenable<T> listenable;

  /// A callback that takes the value from the listenable and returns
  /// true if the child should be wrapped.
  final bool Function(T value) condition;

  /// The wrapper to apply when the condition is true.
  final WrapperBuilder wrapper;

  /// The child widget.
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<T>(
      valueListenable: listenable,
      builder: (context, value, builderChild) {
        if (condition(value)) {
          return wrapper(builderChild!);
        }
        return builderChild!;
      },
      child: child,
    );
  }
}