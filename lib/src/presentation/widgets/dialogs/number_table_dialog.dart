import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

Future showNumberTableDialog({
  required BuildContext context,
  VoidCallback? goBack,
}) => showDialog(
  context: context,
  barrierDismissible: false,
  barrierColor: Colors.transparent,
  builder: (BuildContext context) {
    return BasicDialogDecorator(
      child: _NumberTableForm(
        goBack: goBack,
      ),
    );
  },
);

class _NumberTableForm extends StatefulWidget {
  final VoidCallback? goBack;

  const _NumberTableForm({this.goBack});

  @override
  State<_NumberTableForm> createState() => _NumberTableFormState();
}

class _NumberTableFormState extends State<_NumberTableForm> {
  final TextEditingController _tableNumberController = TextEditingController();
  final GlobalKey _numberModalKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    TutorialUtils.scheduleAfterBuild(this, () => Tutorials.showOrderTableNumberTutorial(context, numberModalKey: _numberModalKey));
  }

  @override
  void dispose() {
    _tableNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TutorialShowcase(
      showcaseKey: _numberModalKey,
      tooltipPosition: TooltipPosition.top,
      targetBorderRadius: BorderRadius.circular(50),
      targetPadding: const EdgeInsets.symmetric(vertical: 40, horizontal: 24),
      container: Container(
        padding: EdgeInsets.only(right: context.width * 0.25, bottom: 20),
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Transform(
              transform: Matrix4.identity()..translate(20.0, 70.0, 0.0)..rotateZ(-math.pi / 1.5),
              alignment: Alignment.center,
              child: SvgPicture.asset(MAssets.tutorialArrow5, height: 120),
            ),
            Flexible(
              child: Text(
                  t.tutorial.orders.tableNumber,
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
            ),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 256.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  t.dialogs.numberTable.title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: MColors.charcoalGray, height: 1.1),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 30.0),
                MTextField(
                  textEditingController: _tableNumberController,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onTapOutside: (event) => FocusScope.of(context).unfocus(),
                  hintText: t.dialogs.numberTable.hint,
                ),
              ],
            ),
          ),
          const SizedBox(height: 30.0),
          MElevatedButton(
            width: 256.0,
            height: 50.0,
            onPressed: () {
              final numberTable = int.tryParse(_tableNumberController.text);
              if (numberTable != null && mounted) {
                context.pop(numberTable);
              }
            },
            label: t.buttons.ok,
            suffix: SvgPicture.asset(
              MAssets.arrowRight,
              width: 24.0,
              height: 24.0,
            ),
          ),
          GoBackButton(
            onPressed: () {
              widget.goBack?.call();
              context.pop();
            },
            label: t.buttons.back,
          ),
        ],
      ),
    );
  }
}
