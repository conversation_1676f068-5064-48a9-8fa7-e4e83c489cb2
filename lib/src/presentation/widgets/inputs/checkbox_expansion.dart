import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class CheckboxExpansion extends StatefulWidget {
  final ExtraGroupModel extraGroup;
  final Set<ExtraItemModel> selectedItems;
  final Function(Set<ExtraItemModel>)? onSelect;

  const CheckboxExpansion({
    required this.extraGroup,
    this.onSelect,
    this.selectedItems = const {},
    super.key
  });

  @override
  State<CheckboxExpansion> createState() => _CheckboxExpansionState();
}

class _CheckboxExpansionState extends State<CheckboxExpansion> {
  bool _isExpanded = true;
  final Set<ExtraItemModel> _selectedItems = {};

  @override
  void initState() {
    super.initState();
    _selectedItems.addAll(widget.selectedItems);
  }

  @override
  void didUpdateWidget(covariant CheckboxExpansion oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only sync if the group changed, not when parent updates selectedItems
    // (to avoid clearing local state during normal interaction)
    if (oldWidget.extraGroup != widget.extraGroup) {
      _selectedItems.clear();
      _selectedItems.addAll(widget.selectedItems);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            behavior: HitTestBehavior.opaque,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.extraGroup.name,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Container(
                  width: 30.0,
                  height: 30.0,
                  decoration: BoxDecoration(
                    color: MColors.iceGray.withOpacity(.7),
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  child: Center(
                    child: Icon(
                      !_isExpanded ?
                        Icons.keyboard_arrow_down_rounded : Icons.keyboard_arrow_up_rounded,
                      color: MColors.onyxShadow,
                      size: 24.0,
                    ),
                  ),
                )
              ],
            ),
          ),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: _isExpanded ? Padding(
              padding: const EdgeInsets.only(top: 12.0, left: 10.0),
              child: Column(
                children: widget.extraGroup.items.map((item) {
                  return _StrokeCheckBox(
                    label: item.name,
                    isChecked: _selectedItems.any((selected) => selected.id == item.id),
                    useLocalState: widget.extraGroup.allowMultiple, // Only use local state for multiple selection
                    onChanged: (value) {
                      setState(() {
                        if (widget.extraGroup.allowMultiple) {
                          // Multiple selection allowed - standard toggle logic
                          if (value) {
                            _selectedItems.add(item);
                          } else {
                            _selectedItems.removeWhere((selected) => selected.id == item.id);
                          }
                        } else {
                          // Single selection only - special logic
                          if (value) {
                            // Always clear others and select this one
                            _selectedItems.clear();
                            _selectedItems.add(item);
                          } else {
                            // Just remove this item
                            _selectedItems.removeWhere((selected) => selected.id == item.id);
                          }
                        }
                      });
                      widget.onSelect?.call(_selectedItems);
                    },
                    extra: item.price > 0 ? item.price.inEuro : '',
                  );
                }).toList(),
              ),
            ) : const SizedBox(),
          ),
        ],
      ),
    );
  }
}

class _StrokeCheckBox extends StatefulWidget {
  final String label;
  final String extra;
  final bool isChecked;
  final bool useLocalState;
  final Function(bool) onChanged;

  const _StrokeCheckBox({
    required this.onChanged,
    this.label = '',
    this.extra = '',
    this.isChecked = false,
    this.useLocalState = true,
  });

  @override
  State<_StrokeCheckBox> createState() => _StrokeCheckBoxState();
}

class _StrokeCheckBoxState extends State<_StrokeCheckBox> {
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();
    _isChecked = widget.isChecked;
  }

  @override
  void didUpdateWidget(covariant _StrokeCheckBox oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isChecked != widget.isChecked) {
      setState(() {
        _isChecked = widget.isChecked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentChecked = widget.useLocalState ? _isChecked : widget.isChecked;
    
    return GestureDetector(
      onTap: () {
        if (widget.useLocalState) {
          setState(() {
            _isChecked = !_isChecked;
          });
          widget.onChanged(_isChecked);
        } else {
          // For controlled mode (single selection), don't use setState
          // State will be updated by parent component
          widget.onChanged(!widget.isChecked);
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: const EdgeInsets.only(bottom: 5.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return ScaleTransition(
                  scale: animation,
                  child: child,
                );
              },
              child: currentChecked ?
              SvgPicture.asset(
                MAssets.strokeSelectedCheckBox,
              ) :
              SvgPicture.asset(
                MAssets.strokeUnselectedCheckBox,
              ),
            ),
            const SizedBox(width: 10.0),
            Expanded(
              child: Text(
                widget.label,
                style: Theme.of(context).textTheme.bodyMedium
              ),
            ),
            Visibility(
              visible: widget.extra.isNotEmpty,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.add,
                    size: 16.0,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    widget.extra,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
