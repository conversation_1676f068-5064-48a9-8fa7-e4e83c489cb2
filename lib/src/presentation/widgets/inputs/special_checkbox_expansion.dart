import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/special/special_component.dart';
import 'package:mutualz/src/domain/models/special/special_dish.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special_extras.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/ingridients/special_additions.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class SpecialCheckboxExpansion extends StatefulWidget {
  final String label;
  final List<SpecialDish> dishes;
  final Set<SpecialDish> selectedDishes;
  final Function(Set<SpecialDish>)? onSelect;
  final SpecialComponent? component;

  const SpecialCheckboxExpansion(
      {this.onSelect, this.label = '', this.dishes = const [], this.selectedDishes = const {}, this.component, super.key});

  @override
  State<SpecialCheckboxExpansion> createState() => _SpecialCheckboxExpansionState();
}

class _SpecialCheckboxExpansionState extends State<SpecialCheckboxExpansion> {
  bool _isExpanded = true;
  final Set<SpecialDish> _selectedDishes = {};

  @override
  void initState() {
    super.initState();
    _selectedDishes.addAll(widget.selectedDishes);
  }

  @override
  void didUpdateWidget(covariant SpecialCheckboxExpansion oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDishes != widget.selectedDishes) {
      _selectedDishes.clear();
      _selectedDishes.addAll(widget.selectedDishes);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            behavior: HitTestBehavior.opaque,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.label,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Container(
                  width: 30.0,
                  height: 30.0,
                  decoration: BoxDecoration(
                    color: MColors.iceGray.withOpacity(.7),
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  child: Center(
                    child: Icon(
                      !_isExpanded ? Icons.keyboard_arrow_down_rounded : Icons.keyboard_arrow_up_rounded,
                      color: MColors.onyxShadow,
                      size: 24.0,
                    ),
                  ),
                )
              ],
            ),
          ),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: _isExpanded
                ? Padding(
                    padding: const EdgeInsets.only(top: 12.0, left: 10.0),
                    child: Column(
                      children: widget.dishes.map((dish) {
                        return _StrokeCheckBox(
                          label: dish.name,
                          isChecked: _selectedDishes.singleWhereOrNull((selectedDish) => selectedDish.id == dish.id) != null,
                          onChanged: (isChecked) async {
                            if (isChecked && dish.extras.isNotEmpty) {
                              final result = await showSpecialAdditions(
                                context: context,
                                dish: dish,
                                component: widget.component,
                                type: AdditionsType.add,
                              );

                              if (result is ReturnedSpecialExtras) {
                                // Convert grouped data back to ExtraGroupModel list
                                final List<ExtraGroupModel> updatedExtras = dish.extras.map((group) {
                                  final selectedItems = result.groupedData[group.id] ?? [];
                                  return group.copyWith(items: selectedItems);
                                }).toList();

                                final newDish = SpecialDish(
                                  id: dish.id,
                                  name: dish.name,
                                  extras: updatedExtras,
                                );
                                setState(() {
                                  _selectedDishes.clear();
                                  _selectedDishes.add(newDish);
                                });
                                widget.onSelect?.call(_selectedDishes);
                              }
                            } else if (isChecked && dish.extras.isEmpty) {
                              final newDish = SpecialDish(
                                id: dish.id,
                                name: dish.name,
                              );
                              setState(() {
                                _selectedDishes.clear();
                                _selectedDishes.add(newDish);
                              });
                              widget.onSelect?.call(_selectedDishes);
                            } else {
                              final updatedSelectedDishes = _selectedDishes.where((d) => d.id != dish.id).toSet();
                              setState(() {
                                _selectedDishes.clear();
                                _selectedDishes.addAll(updatedSelectedDishes);
                              });
                              widget.onSelect?.call(_selectedDishes);
                            }
                          },
                          onEditTap: () async {
                            // Convert current extras to grouped format for editing
                            final currentDish = _selectedDishes.singleWhereOrNull((d) => d.id == dish.id);
                            final Map<String, List<ExtraItemModel>> currentExtras = {};

                            if (currentDish != null) {
                              for (final group in currentDish.extras) {
                                currentExtras[group.id] = group.items;
                              }
                            }

                            final result = await showSpecialAdditions(
                              context: context,
                              dish: dish,
                              type: AdditionsType.edit,
                              selectedExtras: currentExtras,
                            );

                            if (result is ReturnedSpecialExtras) {
                              // Convert grouped data back to ExtraGroupModel list
                              final List<ExtraGroupModel> updatedExtras = dish.extras.map((group) {
                                final selectedItems = result.groupedData[group.id] ?? [];
                                return group.copyWith(items: selectedItems);
                              }).toList();

                              final updatedSelectedDishes = _selectedDishes.map((d) {
                                if (d.id == dish.id) {
                                  return dish.copyWith(extras: updatedExtras);
                                }
                                return d;
                              }).toSet();

                              setState(() {
                                _selectedDishes.clear();
                                _selectedDishes.addAll(updatedSelectedDishes);
                              });

                              widget.onSelect?.call(_selectedDishes);
                            }
                          },
                          extra: _selectedDishes.singleWhereOrNull((d) => d.id == dish.id) != null
                              ? _selectedDishes
                                  .firstWhere((d) => d.id == dish.id)
                                  .extras
                                  .expand((group) => group.items)
                                  .map((item) => item.name)
                                  .join(', ')
                              : '',
                        );
                      }).toList(),
                    ),
                  )
                : const SizedBox(),
          ),
        ],
      ),
    );
  }
}

class _StrokeCheckBox extends StatefulWidget {
  final String label;
  final String extra;
  final bool isChecked;
  final Function(bool) onChanged;
  final VoidCallback? onEditTap;

  const _StrokeCheckBox({
    required this.onChanged,
    this.onEditTap,
    this.label = '',
    this.extra = '',
    this.isChecked = false,
  });

  @override
  State<_StrokeCheckBox> createState() => _StrokeCheckBoxState();
}

class _StrokeCheckBoxState extends State<_StrokeCheckBox> {
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();
    _isChecked = widget.isChecked;
  }

  @override
  void didUpdateWidget(covariant _StrokeCheckBox oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isChecked != widget.isChecked) {
      _isChecked = widget.isChecked;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: widget.key,
      margin: const EdgeInsets.only(bottom: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                print('onTap');
                widget.onChanged(!_isChecked);
              },
              child: Row(
                children: [
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    transitionBuilder: (child, animation) {
                      return ScaleTransition(
                        scale: animation,
                        child: child,
                      );
                    },
                    child: _isChecked
                        ? SvgPicture.asset(
                            MAssets.strokeSelectedCheckBox,
                          )
                        : SvgPicture.asset(
                            MAssets.strokeUnselectedCheckBox,
                          ),
                  ),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: Text(widget.label, style: Theme.of(context).textTheme.bodyMedium),
                  ),
                ],
              ),
            ),
          ),
          Visibility(
            visible: widget.isChecked && widget.extra.isNotEmpty,
            child: GestureDetector(
              onTap: widget.onEditTap,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: SvgPicture.asset(
                  MAssets.iconEdit,
                  width: 20.0,
                  height: 24.0,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
