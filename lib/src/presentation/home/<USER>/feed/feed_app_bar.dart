import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'dart:math' as math;

class FeedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final SystemUiOverlayStyle? systemOverlayStyle;
  final VoidCallback? onSearchTap;
  final VoidCallback? onAddTap;
  final GlobalKey addButtonKey;
  final GlobalKey searchButtonKey;
  final ValueNotifier<bool>? isAddButtonHighlighted;

  const FeedAppBar({
    this.systemOverlayStyle,
    this.onSearchTap,
    this.onAddTap,
    required this.addButton<PERSON>ey,
    required this.searchButton<PERSON>ey,
    this.isAddButtonHighlighted,
    super.key,
  });

  static const double searchAppBarH = 56.0;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: systemOverlayStyle ?? Theme.of(context).appBarTheme.systemOverlayStyle,
      backgroundColor: Colors.transparent,
      foregroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.transparent,
      forceMaterialTransparency: true,
      elevation: 0.0,
      leadingWidth: 106.0,
      leading: Padding(
        padding: const EdgeInsets.only(left: 32.0),
        child: SvgPicture.asset(
          MAssets.logo,
          width: 74.0,
          height: 18.0,
        ),
      ),
      actions: [
        UnconstrainedBox(
          child: TutorialShowcase(
            showcaseKey: searchButtonKey,
            container: Container(
              padding: EdgeInsets.only(right: 100, left: context.width * 0.2),
              child: Transform.translate(
                  offset: const Offset(0, -20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Transform.translate(
                        offset: const Offset(-20, 0),
                        child: SvgPicture.asset(MAssets.tutorialArrow1, height: 80),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 16, top: 8),
                        child: Text(
                          t.tutorial.feed.search,
                          style: const TextStyle(fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
            ),
            targetShapeBorder: const OutlineCircleBorder(
              gap: 5,
              side: BorderSide(color: MColors.goldenRod, width: 4),
            ),
            tooltipPosition: TooltipPosition.bottom,
            targetPadding: const EdgeInsets.all(6),
            child: SizedBox(
              width: 24.0,
              height: 24.0,
              child: GestureDetector(onTap: onSearchTap, child: SvgPicture.asset(MAssets.iconSearchField)),
            ),
          ),
        ),
        const SizedBox(width: 20.0),
        Padding(
          padding: const EdgeInsets.only(right: 32.0),
          child: UnconstrainedBox(
            child: TutorialShowcase(
              showcaseKey: addButtonKey,
              container: Container(
                padding: EdgeInsets.only(left: context.width * 0.3, right: 70),
                alignment: Alignment.centerLeft,
                child: Transform.translate(
                  offset: const Offset(0, -30),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Transform(
                        transform: Matrix4.identity(),
                        alignment: Alignment.center,
                        child: SvgPicture.asset(MAssets.tutorialArrow2, height: 80),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 16, top: 8),
                        child: Text(
                          t.tutorial.feed.addPost,
                          style: const TextStyle(fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              tooltipPosition: TooltipPosition.bottom,
              onTargetClick: () => Future.delayed(const Duration(milliseconds: 500), () => isAddButtonHighlighted?.value = true),
              onBarrierClick: () => Future.delayed(const Duration(milliseconds: 500), () => isAddButtonHighlighted?.value = false),
              child: ValueListenableBuilder<bool>(
                valueListenable: isAddButtonHighlighted ?? ValueNotifier<bool>(false),
                builder: (context, isHighlighted, child) {
                  return Container(
                    width: 36.0,
                    height: 36.0,
                    decoration: BoxDecoration(
                      color: isHighlighted ? MColors.goldenRod : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: GestureDetector(
                      onTap: onAddTap,
                      child: UnconstrainedBox(
                        child: SizedBox(
                          width: 24.0,
                          height: 24.0,
                          child: SvgPicture.asset(MAssets.iconAdd),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(searchAppBarH);
}
