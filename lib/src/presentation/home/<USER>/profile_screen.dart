import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/app/app_bloc.dart';
import 'package:mutualz/src/domain/blocs/auth/auth_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/home/<USER>/profile/setttings_list.dart';
import 'package:mutualz/src/presentation/home/<USER>/profile/user_image_shimmer.dart';
import 'package:mutualz/src/presentation/home/<USER>/profile/user_posts_list.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/settings/screens/general_settings_screen.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/utils/color_utils.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with SingleTickerProviderStateMixin {
  late final AuthBloc _authBloc;
  late TabController _tabController;
  late final ScrollController _innerScrollController = ScrollController();
  late final ScrollController _nestedScrollController = ScrollController();
  bool isPersistentHeaderPinned = false;

  final GlobalKey _tutorialSubscriptionsButtonKey = GlobalKey();
  final GlobalKey _tutorialOrdersButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _authBloc = context.read<AuthBloc>();
    _tabController = TabController(length: 2, vsync: this, animationDuration: Duration.zero);
    _tabController.addListener(_tabHandler);
    _innerScrollController.addListener(_innerScrollHandler);
    _nestedScrollController.addListener(_nestedScrollHandler);

    TutorialUtils.scheduleAfterBuild(this, () => Tutorials.showProfileTutorialPart1(context, subscriptionsButtonKey: _tutorialSubscriptionsButtonKey, ordersButtonKey: _tutorialOrdersButtonKey));
  }

  void _tabHandler() {
    setState(() {});
  }

  void _nestedScrollHandler() {
    if (_nestedScrollController.offset > 0 && !isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = true;
      });
    } else if (_nestedScrollController.offset <= 0 && isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = false;
      });
    }
  }

  void _innerScrollHandler() {
    if (_nestedScrollController.hasClients) {
      _nestedScrollController.jumpTo(_innerScrollController.offset);
    }

    if (_innerScrollController.offset > 0 && !isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = true;
      });
    } else if (_innerScrollController.offset <= 0 && isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_tabHandler);
    _tabController.dispose();
    _innerScrollController.removeListener(_innerScrollHandler);
    _innerScrollController.dispose();
    _nestedScrollController.removeListener(_nestedScrollHandler);
    _nestedScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      bloc: _authBloc,
      builder: (context, state) {
        return Scaffold(
          backgroundColor: MColors.softCotton,
          appBar: AppBar(
            backgroundColor: MColors.softCotton,
            surfaceTintColor: MColors.softCotton,
            actions: [
              /// Notifications and Chats icons
              // Padding(
              //   padding: const EdgeInsets.only(right: 20.0),
              //   child: GestureDetector(
              //     onTap: () => context.pushNamed(MRoutes.chats),
              //     child: SvgPicture.asset(
              //       MAssets.iconChats,
              //       width: 24.0,
              //       height: 24.0,
              //     ),
              //   ),
              // ),
              // Padding(
              //   padding: const EdgeInsets.only(right: 42.0),
              //   child: GestureDetector(
              //     onTap: () {
              //       //print('Notifications');
              //     },
              //     child: SvgPicture.asset(
              //       MAssets.iconNotifications,
              //       width: 24.0,
              //       height: 24.0,
              //     ),
              //   ),
              // ),
            ],
          ),
          body: SafeArea(
            bottom: false,
            child: NestedScrollView(
              physics: state.userStatus == UserStatus.loading
                  ? const NeverScrollableScrollPhysics()
                  : const AlwaysScrollableScrollPhysics(),
              controller: _nestedScrollController,
              headerSliverBuilder: (context, innerBoxIsScrolled) => [
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverHeaderDelegate(
                    child: Container(
                        color: MColors.softCotton,
                        child: switch (state.userStatus) {
                          UserStatus.loading => const UserImageShimmer(),
                          UserStatus.success => GestureDetector(
                            onTap: () => context.pushNamed(MRoutes.userProfileSettings),
                            behavior: HitTestBehavior.opaque,
                            child: Column(
                              children: [
                                MAvatar(
                                  size: isPersistentHeaderPinned ? 65.0 : 105.0,
                                  imageUrl: state.user?.personalInfo.imageUrl,
                                  borderColor: state.user?.profileColor != null ?
                                   ColorUtils.hexToColor(state.user!.profileColor!) : MColors.scarletEmber,
                                  isYoursAvatar: true,
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                                  child: AnimatedDefaultTextStyle(
                                    style: isPersistentHeaderPinned ?
                                    Theme.of(context).textTheme.headlineSmall!.copyWith(
                                      fontSize: 20.0,
                                    ) : Theme.of(context).textTheme.headlineSmall!,
                                    duration: Duration.zero,
                                    child: Text(
                                      state.user?.name ?? '',
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          UserStatus.error => FailedLoadData(
                            onRetry: () => _authBloc.add(const AuthEvent.fetchUser()),
                          ),
                          UserStatus.idle => const SizedBox.expand(),
                        }
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: () => context.pushNamed(MRoutes.followers),
                              behavior: HitTestBehavior.opaque,
                              child: Column(
                                children: [
                                  Text(
                                    state.user?.followersCount.toString() ?? '0',
                                    style: Theme.of(context).textTheme.bodyLarge,
                                  ),
                                  Text(
                                    t.screens.profile.followers,
                                    style: Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            GestureDetector(
                              onTap: () => context.pushNamed(MRoutes.following),
                              behavior: HitTestBehavior.opaque,
                              child: Column(
                                children: [
                                  Text(
                                    state.user?.followingCount.toString() ?? '0',
                                    style: Theme.of(context).textTheme.bodyLarge,
                                  ),
                                  Text(
                                    t.screens.profile.following,
                                    style: Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12.0),
                        ListeningConditionalWrapper<bool>(
                          listenable: Tutorials.isProfileTutorialActive,
                          condition: (value) => value,
                          wrapper: (child) => TutorialShowcase(
                              showcaseKey: _tutorialSubscriptionsButtonKey,
                              tooltipPosition: TooltipPosition.bottom,
                              onTap: () => _tabController.animateTo(1, duration: Duration.zero),
                              container: Container(
                                padding: EdgeInsets.only(right: context.width * 0.1, left: context.width * 0.2, top: 30),
                                alignment: Alignment.centerLeft,
                                child:  Row(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Transform(
                                      transform: Matrix4.identity()..rotateZ(-math.pi / 4),
                                      alignment: Alignment.center,
                                      child: SvgPicture.asset(MAssets.tutorialArrow2, height: 60),
                                    ),
                                    Flexible(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 46),
                                        child: Text(
                                          t.tutorial.profile.subscriptions,
                                          style: const TextStyle(fontSize: 14),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              targetShapeBorder: OutlineRoundedRectangleBorder(
                                side: const BorderSide(
                                  color: MColors.goldenRod,
                                  width: 3,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: child),
                          child: MElevatedButton(
                            width: 150.0,
                            height: 36.0,
                            label: t.screens.profile.subscriptions,
                            background: Colors.transparent,
                            isEnabledBoxShadow: false,
                            borderColor: MColors.onyxShadow,
                            borderWidth: 1.0,
                            borderRadius: 5.0,
                            labelStyle: Theme.of(context).textTheme.bodyMedium,
                            overlayColor: MColors.silverMist.withOpacity(.2),
                            onPressed: () => context.pushNamed(MRoutes.subscriptionOverview),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverTabDelegate(
                    child: ColoredBox(
                      color: MColors.softCotton,
                      child: Center(
                        child: Container(
                          decoration: BoxDecoration(
                            color: MColors.white,
                            borderRadius: BorderRadius.circular(24.0),
                            boxShadow: [
                              BoxShadow(
                                color: MColors.black.withOpacity(.05),
                                blurRadius: 20.0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          constraints: const BoxConstraints(
                            maxHeight: 40.0,
                          ),
                          child: TabBar(
                            controller: _tabController,
                            indicatorColor: Colors.transparent,
                            dividerHeight: 0.0,
                            labelColor: MColors.onyxShadow,
                            padding: const EdgeInsets.symmetric(horizontal: 4.0),
                            isScrollable: false,
                            physics: const NeverScrollableScrollPhysics(),
                            tabAlignment: TabAlignment.center,
                            labelPadding: EdgeInsets.zero,
                            overlayColor: WidgetStateProperty.all<Color>(Colors.transparent),
                            tabs: [
                              Tab(
                                child: TabItem(
                                  isActive: _tabController.index == 0,
                                  label: t.screens.profile.tabs.post,
                                  activeColor: MColors.silverMist,
                                ),
                              ),
                              Tab(
                                child: TabItem(
                                  isActive: _tabController.index == 1,
                                  label: t.screens.profile.tabs.settings,
                                  activeColor: MColors.silverMist,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
              body: Padding(
                padding: const EdgeInsets.only(
                  left: 24.0,
                  right: 24.0,
                ),
                child: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    const UserPostsList(),
                    SettingsList(
                      ordersButtonKey: _tutorialOrdersButtonKey,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  _SliverHeaderDelegate({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 150.0;

  @override
  double get minExtent => 102.0;

  @override
  bool shouldRebuild(_SliverHeaderDelegate oldDelegate) {
    return true;
  }
}

class _SliverTabDelegate extends SliverPersistentHeaderDelegate {
  _SliverTabDelegate({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 60.0;

  @override
  double get minExtent => 60.0;

  @override
  bool shouldRebuild(_SliverTabDelegate oldDelegate) {
    return true;
  }
}