import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/presentation/home/<USER>/feed/feed_app_bar.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/restaurant_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class FeedScreen extends StatefulWidget {
  const FeedScreen({super.key});

  @override
  State<FeedScreen> createState() => _FeedScreenState();
}

class _FeedScreenState extends State<FeedScreen> {
  late final FeedBloc _feedBloc = getIt<FeedBloc>()
    ..add(const FeedEvent.fetchPosts());
  late final DiscoverBloc _discoverBloc;

  final GlobalKey _addButtonKey = GlobalKey();
  final GlobalKey _searchButtonKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();

    TutorialUtils.scheduleAfterBuild(this, () {
      Tutorials.showFeedTutorial(context, addButtonKey: _addButtonKey, searchButtonKey: _searchButtonKey);
    });
  }

  @override
  void dispose() {
    _feedBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _feedBloc,
      child: Scaffold(
        backgroundColor: MColors.white,
        appBar: FeedAppBar(
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          onAddTap: () => context.pushNamed(MRoutes.addPost,
              extra: _discoverBloc.state.restaurants),
          onSearchTap: () => context.pushNamed(MRoutes.feedSearch),
          addButtonKey: _addButtonKey,
          searchButtonKey: _searchButtonKey,
          isAddButtonHighlighted: Tutorials.feedTutorialAddButtonHighlighted,
        ),
        body: RefreshIndicator(
          onRefresh: () async => _feedBloc.add(const FeedEvent.fetchPosts()),
          child: SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: BlocBuilder<FeedBloc, FeedState>(
                  bloc: _feedBloc,
                  builder: (context, state) {
                    return switch (state.status) {
                      FeedStatus.idle => const SizedBox(),
                      FeedStatus.loading => const PostsSkeleton(),
                      FeedStatus.success => state.posts.isEmpty
                          ? const Center(child: EmptyData())
                          : PostsList(
                              posts: state.posts,
                              onRestaurantTitleTap: (restaurantId) {
                                final restaurant = _discoverBloc
                                    .state.restaurants
                                    .singleWhereOrNull(
                                        (r) => r.id == restaurantId);

                                if (restaurant == null) return;

                                context.pushNamed(MRoutes.restaurant,
                                    extra: RestaurantNavBundle(
                                        restaurant: restaurant));
                              },
                              onCommentsTap: (postId) => context
                                  .pushNamed(MRoutes.comments, extra: postId),
                              onFavoriteTap: (postId, isFavorite) {
                                if (state.favoriteStatus ==
                                    PostFavoriteStatus.loading) return;
                                _feedBloc.add(FeedEvent.toggleFavorite(
                                  postId: postId,
                                  isFavorite: isFavorite,
                                  withRequest: true,
                                ));
                              },
                              onDelete: (postId) {
                                context.pop();
                                _feedBloc.add(FeedEvent.deletePost(postId));
                              },
                              onReport: (postId) {
                                context.pop();
                                context.pushNamed(MRoutes.reportUserPost,
                                    extra: postId);
                              },
                              onBlock: (postId) {
                                context.pop();
                                context.pushNamed(MRoutes.blockUserPost,
                                    extra: postId);
                              },
                            ),
                      FeedStatus.error => FailedLoadData(
                          onRetry: () =>
                              _feedBloc.add(const FeedEvent.fetchPosts()),
                        ),
                    };
                  }),
            ),
          ),
        ),
      ),
    );
  }
}
