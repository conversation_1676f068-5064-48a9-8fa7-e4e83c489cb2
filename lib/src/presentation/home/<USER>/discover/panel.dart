import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/extensions/context_extensions.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/geoposition_loader.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/panel_item.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:mutualz/src/presentation/widgets/buttons/filter_button.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'dart:math' as math;

class DiscoverPanel extends StatefulWidget {
  final VoidCallback? onTapChat;
  final VoidCallback? onTapFilter;
  final VoidCallback? onTapScanner;
  final VoidCallback? onTapPosition;
  final bool isGeoPositionLoading;
  final GlobalKey scannerButtonKey;

  const DiscoverPanel({
    super.key, 
    required this.scannerButtonKey,
    this.onTapChat,
    this.onTapFilter,
    this.onTapScanner,
    this.onTapPosition,
    this.isGeoPositionLoading = false,
  });

  @override
  State<DiscoverPanel> createState() => _DiscoverPanelState();
}

class _DiscoverPanelState extends State<DiscoverPanel> {
  late final DiscoverBloc _discoverBloc;

  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Visibility(
                visible: widget.onTapChat != null,
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: PanelItem(
                    image: MAssets.chat,
                    width: 50.0,
                    height: 50.0,
                    iconWidth: 32.0,
                    iconHeight: 32.0,
                    onTap: widget.onTapChat,
                  ),
                ),
              ),
              Visibility(
                visible: widget.onTapPosition != null,
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: widget.isGeoPositionLoading ?
                  const GeoPositionLoader() :
                  PanelItem(
                    image: MAssets.position,
                    onTap: widget.onTapPosition,
                  ),
                ),
              ),
              Visibility(
                visible: widget.onTapFilter != null,
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: BlocBuilder<DiscoverBloc, DiscoverState>(
                    bloc: _discoverBloc,
                    builder: (context, state) {
                      if (state.status == RestaurantsStatus.success) {
                        return FilterButton(
                          svgIcon: MAssets.filter,
                          width: 50.0,
                          height: 50.0,
                          iconWidth: 32.0,
                          iconHeight: 32.0,
                          background: state.filterCount > 0 ? MColors.scarletEmber : MColors.white,
                          onTap: widget.onTapFilter,
                          filterCount: state.filterCount,
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        Visibility(
          visible: widget.onTapScanner != null,
          child: ListeningConditionalWrapper<bool>(
            listenable: Tutorials.mapQRCodeTutorialActive,
            condition: (value) => value,
            wrapper: (child) => TutorialShowcase(
              showcaseKey: widget.scannerButtonKey,
              onTap: () => Tutorials.mapQRCodeTutorialActive.value = false,
              targetShapeBorder: const OutlineCircleBorder(
                gap: 3,
                side: BorderSide(color: MColors.goldenRod, width: 6),
              ),
              container: Container(
                padding: EdgeInsets.only(right: 80, left: context.width * 0.2),
                child: Transform.translate(
                  offset: const Offset(0, 30),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(right: 16, bottom: 8),
                        child: Text(
                          t.tutorial.map.qrCode,
                          style: const TextStyle(fontSize: 14),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Transform(
                        transform: Matrix4.identity()
                          ..scale(-1.0, 1.0)
                          ..rotateZ(math.pi),
                        alignment: Alignment.center,
                        child: SvgPicture.asset(MAssets.tutorialArrow2, height: 100),
                      ),
                    ],
                  ),
                ),
              ),
              child: child,
            ),
            child: PanelItem(
              image: MAssets.iconFilledScanner,
              onTap: widget.onTapScanner,
            ),
          ),
        ),
      ],
    );
  }
}
