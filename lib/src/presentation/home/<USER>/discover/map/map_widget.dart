import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class MapWidget extends StatefulWidget {
  final Position? userPosition;
  final City? preferredCity;
  final Function(String) onMarkerTap;
  final List<RestaurantModel> markers;

  const MapWidget({
    required this.onMarkerTap,
    this.userPosition,
    this.markers = const [],
    this.preferredCity,
    super.key
  });

  @override
  State<MapWidget> createState() => _MapWidgetState();
}

class _MapWidgetState extends State<MapWidget> {
  final Completer<GoogleMapController> _completer = Completer<GoogleMapController>();
  late final DiscoverBloc _discoverBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;

  final LatLng _initialDefault = City.values.first.coordinates;

  static const double _zoomDefault = 12.0;

  late String _mapStyle;

  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    if (widget.userPosition != null) {
      _goCameraToUserPosition(widget.userPosition!);
    }
    rootBundle.loadString(MAssets.mapSilverStyleJSON).then((string) {
      _mapStyle = string;
    });
  }

  @override
  void didUpdateWidget(covariant MapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userPosition != widget.userPosition && widget.userPosition != null) {
      _goCameraToUserPosition(widget.userPosition!);
    }
  }

  Future<void> _goCameraToUserPosition(Position position) async {
    final controller = await _completer.future;
    controller.animateCamera(CameraUpdate.newCameraPosition(
      CameraPosition(
        target: LatLng(widget.userPosition!.latitude, widget.userPosition!.longitude),
        zoom: _zoomDefault,
      )
    ));
  }

  Future<void> _goCameraToCityPosition(LatLng coordinates) async {
    final controller = await _completer.future;
    controller.animateCamera(CameraUpdate.newCameraPosition(
      CameraPosition(
        target: coordinates,
        zoom: _zoomDefault,
      )
    ));
  }

  Future<void> onMapCreated(GoogleMapController controller) async {
    try {
      controller.setMapStyle(_mapStyle);
      _completer.complete(controller);
    } catch (e) {
      ///Todo state when map is not loaded
      log('Error with map initialized: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DiscoverBloc, DiscoverState>(
      bloc: _discoverBloc,
      listener: (context, state) {
        _goCameraToCityPosition(state.city?.coordinates ?? _initialDefault);
      },
      child: BlocBuilder<UserLoyaltyBloc, UserLoyaltyState>(
        bloc: _userLoyaltyBloc,
        builder: (context, state) {
          return GoogleMap(
            markers: widget.markers.map((restaurant) =>
              Marker(
                markerId: MarkerId(restaurant.id),
                icon: restaurant.createIcon(state.loyalty),
                position: LatLng(
                  restaurant.address.location.coordinates.last,
                  restaurant.address.location.coordinates.first,
                ),
                onTap: () => widget.onMarkerTap(restaurant.id),
              ),
            ).toSet(),
            mapType: MapType.normal,
            myLocationButtonEnabled: false,
            myLocationEnabled: false,
            compassEnabled: false,
            mapToolbarEnabled: false,
            zoomControlsEnabled: true,
            indoorViewEnabled: true,
            onMapCreated: onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _initialDefault,
              zoom: _zoomDefault,
            ),
          );
        },
      ),
    );
  }
}
