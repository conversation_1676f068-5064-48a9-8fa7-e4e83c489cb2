import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/extensions/context_extensions.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/filter/restaurant_filter.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:math' as math;

Future showRestaurantFilterModal({
  required BuildContext context,
  required VoidCallback onComplete
}) async {
  return showModalBottomSheet(
    context: context,
    barrierColor: Colors.transparent,
    backgroundColor: MColors.white,
    isScrollControlled: true,
    builder: (context) {
      return const _RestaurantFilter();
    },
  ).whenComplete(onComplete);
}

class _RestaurantFilter extends StatefulWidget {
  const _RestaurantFilter();

  @override
  State<_RestaurantFilter> createState() => _RestaurantFilterState();
}

class _RestaurantFilterState extends State<_RestaurantFilter> {
  late final DiscoverBloc _discoverBloc;
  late final AppBloc _appBloc;

  static const double _bigHeightK = .85;
  static const double _smallHeightK = .4;

  double _heightK = _smallHeightK;

  Set<Filter> _filterFoodTypes = {};
  Set<Filter> _filterDietPreferences = {};
  Set<Filter> _filterFoods = {};
  Set<Filter> _filterRating = {};

  final GlobalKey _dragHandleKey = GlobalKey();
  bool _isTutorialShown = false;

  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();
    _appBloc = context.read<AppBloc>();
    _filterFoodTypes = _discoverBloc.swapChosenFilterToFirst(
      _discoverBloc.state.selectedTypesFood,
      FilterFoodType.values.toSet()
    );
    _filterDietPreferences = _discoverBloc.swapChosenFilterToFirst(
      _discoverBloc.state.selectedDietPrefs,
      FilterDietPreferences.values.toSet()
    );
    _filterFoods = _discoverBloc.swapChosenFilterToFirst(
      _discoverBloc.state.selectedFood,
      FilterFood.values.toSet()
    );

    if (!_isTutorialShown) {
      TutorialUtils.scheduleAfterBuild(this, () => Tutorials.showMapFilterTutorial(context, dragHandleKey: _dragHandleKey));
      setState(() => _isTutorialShown = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragUpdate: (details) {
        if (details.delta.dy > 0) {
          _appBloc.add(const AppEvent.setBNBVisible(true));
          setState(() {
            _heightK = _smallHeightK;
          });
        } else {
          _appBloc.add(const AppEvent.setBNBVisible(false));
          setState(() {
            _heightK = _bigHeightK;
          });
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(MSizes.cardBorderRadius),
            topRight: Radius.circular(MSizes.cardBorderRadius),
          ),
        ),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * _heightK,
        ),
        child: BlocBuilder<DiscoverBloc, DiscoverState>(
          bloc: _discoverBloc,
          builder: (context, state) {
            return Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: 
                    ListeningConditionalWrapper<bool>(
                      listenable: Tutorials.mapFilterTutorialActive,
                      condition: (value) => value,
                      wrapper: (child) => TutorialShowcase(
                        showcaseKey: _dragHandleKey,
                        container: Container(
                          padding: EdgeInsets.only(right: context.width * 0.33, bottom: 16),
                          child: Transform.translate(
                            offset: const Offset(0, 0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 16, bottom: 8),
                                  child: Text(
                                    t.tutorial.filter.info,
                                    style: const TextStyle(fontSize: 14),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Transform(
                                  transform: Matrix4.identity()
                                    ..rotateZ(math.pi / 1.5),
                                  alignment: Alignment.center,
                                  child: SvgPicture.asset(MAssets.tutorialArrow4, height: 80),
                                ),
                              ],
                            ),
                          ),
                        ),
                        targetShapeBorder: OutlineRoundedRectangleBorder(
                          gap: 6,
                          side: const BorderSide(color: MColors.goldenRod, width: 12),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: child,
                      ),
                      child: Container(
                        width: 40.0,
                        height: 5.0,
                        decoration: BoxDecoration(
                          color: MColors.slateGrayMist,
                          borderRadius: BorderRadius.circular(2.5),
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 12.0,
                  right: 32.0,
                  child: LikeButton(
                    isLiked: state.isFavoriteSelected,
                    onTap: () {
                      if (state.status == RestaurantsStatus.loading) return;
                      _discoverBloc.add(DiscoverEvent.isFavoriteSelectedUpdate(!state.isFavoriteSelected));
                      _discoverBloc.add(const DiscoverEvent.filterRestaurants());
                    },
                  ),
                ),
                Positioned.fill(
                  top: MSizes.dialogVPadding,
                  bottom: _heightK == _bigHeightK ? 0.0 : MSizes.dialogVPadding,
                  child: Container(
                    padding: EdgeInsets.only(
                      top: 10.0,
                      bottom: _heightK == _bigHeightK ? 0.0 :
                      MediaQuery.of(context).size.width * MSizes.kHeightBnB
                          - MSizes.dialogVPadding,
                    ),
                    child: Column(
                      children: [
                        _FilterWrapper(
                          isScrollable: _heightK != _bigHeightK,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              RestaurantFilter(
                                  label: t.filters.foodTypeTitle,
                                  items: _filterFoodTypes.toList(),
                                  selectedItems: state.selectedTypesFood,
                                  isDisabled: state.status == RestaurantsStatus.loading,
                                  onSelected: (selectedItems) {
                                    _discoverBloc.add(DiscoverEvent.selectedTypesFoodUpdate(selectedItems));
                                    _discoverBloc.add(const DiscoverEvent.filterRestaurants());
                                  }
                              ),
                              const SizedBox(height: 10.0),
                              RestaurantFilter(
                                  label: t.filters.dietPreferencesTitle,
                                  items: _filterDietPreferences.toList(),
                                  selectedItems: state.selectedDietPrefs,
                                  isDisabled: state.status == RestaurantsStatus.loading,
                                  onSelected: (selectedItems) {
                                    _discoverBloc.add(DiscoverEvent.selectedDietPrefsUpdate(selectedItems));
                                    _discoverBloc.add(const DiscoverEvent.filterRestaurants());
                                  }
                              ),
                              const SizedBox(height: 10.0),
                              RestaurantFilter(
                                  label: t.filters.foodTitle,
                                  items: _filterFoods.toList(),
                                  selectedItems: state.selectedFood,
                                  isDisabled: state.status == RestaurantsStatus.loading,
                                  onSelected: (selectedItems) {
                                    _discoverBloc.add(DiscoverEvent.selectedFoodUpdate(selectedItems));
                                    _discoverBloc.add(const DiscoverEvent.filterRestaurants());
                                  }
                              ),
                              const SizedBox(height: 10.0),
                              RestaurantFilter(
                                  label: t.filters.sortTitle,
                                  items: const [FilterBy.rating],
                                  selectedItems: state.selectedRating,
                                  isDisabled: state.status == RestaurantsStatus.loading,
                                  onSelected: (selectedItems) {
                                    _discoverBloc.add(DiscoverEvent.selectedRatingUpdate(selectedItems));
                                    _discoverBloc.add(const DiscoverEvent.filterRestaurants());
                                  }
                              ),
                              const SizedBox(height: 10.0),
                            ],
                          ),
                        ),
                        _FilterContentWrapper(
                          isExpanded: _heightK == _bigHeightK,
                          child: Visibility(
                            visible: _heightK == _bigHeightK,
                            child: AnimatedOpacity(
                              duration: const Duration(milliseconds: 300),
                              opacity: _heightK == _bigHeightK ? 1.0 : 0.0,
                              child: Padding(
                                padding: EdgeInsets.only(top: _heightK == _bigHeightK ? 20.0 : 0.0),
                                child: switch (state.status) {
                                 RestaurantsStatus.idle => const SizedBox(),
                                 RestaurantsStatus.loading => const RestaurantListSkeleton(),
                                 RestaurantsStatus.success => state.filteredRestaurants.isNotEmpty ?
                                 ListView.builder(
                                   itemCount: state.filteredRestaurants.length,
                                   itemBuilder: (context, index) {
                                    final restaurant = state.filteredRestaurants[index];
                                     return RestaurantCard(
                                       restaurant: restaurant,
                                       isShowOpenHours: false,
                                       onFavoriteTapped: () {
                                         !restaurant.isFavorite ?
                                         _discoverBloc.add(DiscoverEvent.addToFavorite(restaurant.id)) :
                                         _discoverBloc.add(DiscoverEvent.removeFromFavorite(restaurant.id));
                                       }
                                     );
                                   },
                                 ) : const Center(
                                   child: Text('No restaurants found'),
                                 ),
                                 RestaurantsStatus.error => const Center(
                                  child: Text(
                                    'Error',
                                    style: TextStyle(
                                      fontSize: 16.0,
                                      height: 1.25,
                                      fontFamily: 'Roboto',
                                      color: MColors.slateGrayMist,
                                    ),
                                  ),
                                 ),
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _FilterWrapper extends StatelessWidget {
  final Widget child;
  final bool isScrollable;

  const _FilterWrapper({
    required this.child,
    this.isScrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    return isScrollable ? Expanded(
      child: SingleChildScrollView(
        child: child
      ),
    ) : child;
  }
}

class _FilterContentWrapper extends StatelessWidget {
  final Widget child;
  final bool isExpanded;

  const _FilterContentWrapper({
    required this.child,
    this.isExpanded = false,
  });

  @override
  Widget build(BuildContext context) {
    return isExpanded ? Expanded(
      child: child,
    ) : child;
  }
}
