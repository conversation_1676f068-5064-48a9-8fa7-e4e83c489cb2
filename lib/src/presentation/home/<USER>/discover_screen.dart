import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/review/late_review_notification.dart';
import 'package:mutualz/src/domain/services/navigation/navigation_event_service.dart';
import 'package:mutualz/src/domain/services/notifications/notification_service.dart';
import 'package:mutualz/src/domain/utils/scanner_utils.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/decoration/app_bar_decoration.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/filter/restaurant_filter_modal.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/info/restaurant_info_modal.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/map/map_widget.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover/panel.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/order_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/restaurant_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/scanner/models/scanner_nav_bundle.dart';
import 'package:mutualz/src/presentation/splash/screens/splash_screen.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';

import '../../review /screens/late_review_screen.dart';

class DiscoverScreen extends StatefulWidget {
  const DiscoverScreen({super.key});

  @override
  State<DiscoverScreen> createState() => _DiscoverScreenState();
}

class _DiscoverScreenState extends State<DiscoverScreen> {
  late final DiscoverBloc _discoverBloc;
  late final AppBloc _appBloc;
  late final AuthBloc _authBloc;
  late final CouponsBloc _couponsBloc;
  late final NavigationEventService _navigationEventService;
  StreamSubscription<String>? _restaurantNavigationSubscription;
  static const List<String> _cities = ['Oldenburg', 'Berlin', 'Osnabrück'];
  static const String mapKey = 'MapWidget';
  
  final GlobalKey _scannerButtonKey = GlobalKey();

  String city = _cities.first;

  @override
  void initState() {
    super.initState();
    _appBloc = context.read<AppBloc>();
    _authBloc = context.read<AuthBloc>();
    _discoverBloc = context.read<DiscoverBloc>()
      ..add(const DiscoverEvent.fetchRestaurants(City.oldenburg));
    _couponsBloc = context.read<CouponsBloc>();
    _couponsBloc.add(const CouponsEvent.notifyServerForCouponsNotification());
    _navigationEventService = getIt<NavigationEventService>();
    
    _checkPendingNotification();
    _setupNavigationListener();
  }
  
  /// Checks for any pending notifications from the splash screen
  void _checkPendingNotification() {
    // Small delay to ensure screen is fully rendered
    Future.delayed(const Duration(milliseconds: 300), () {
      final pendingMessage = SplashScreen.getPendingMessage();
      if (pendingMessage != null) {
        final notificationType = pendingMessage.data['notificationType'];
        if (notificationType == 'late_review' || 
            notificationType == 'couponRefreshmentAvailable') {
          _appBloc.handleInitialMessage(pendingMessage);
        }
      }
    });
  }

  void _setupNavigationListener() {
    _restaurantNavigationSubscription = _navigationEventService.restaurantNavigationStream.listen((restaurantId) {
      if (!mounted) return;

      // Wait for restaurants to be loaded before navigating
      if (!_appBloc.state.isRestaurantsLoaded || _discoverBloc.state.status != RestaurantsStatus.success) {
        // Listen for state changes and navigate when ready
        StreamSubscription<AppState>? appStateSubscription;
        StreamSubscription<DiscoverState>? discoverStateSubscription;

        void checkAndNavigate() {
          if (_appBloc.state.isRestaurantsLoaded && _discoverBloc.state.status == RestaurantsStatus.success) {
            appStateSubscription?.cancel();
            discoverStateSubscription?.cancel();

            final restaurant = _discoverBloc.state.getRestaurantById(restaurantId);
            if (restaurant != null && mounted) {
              context.pushNamed(
                MRoutes.restaurant,
                extra: RestaurantNavBundle(restaurant: restaurant),
              );
            } else {
              debugPrint('Restaurant not found with id: $restaurantId');
            }
          }
        }

        appStateSubscription = _appBloc.stream.listen((_) => checkAndNavigate());
        discoverStateSubscription = _discoverBloc.stream.listen((_) => checkAndNavigate());
        
        return;
      }

      final restaurant = _discoverBloc.state.getRestaurantById(restaurantId);

      if (restaurant != null && mounted) {
        context.pushNamed(
          MRoutes.restaurant,
          extra: RestaurantNavBundle(restaurant: restaurant),
        );
      } else {
        debugPrint('Restaurant not found with id: $restaurantId');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return MultiBlocListener(
      listeners: [
        BlocListener<AppBloc, AppState>(
          bloc: _appBloc,
          listener: (context, state) {
            print('SHOW_LATE_REVIEW');
            print(state.lateReviewNotification);
            if (state.lateReviewNotification != null) {
              showLateReviewDialog(
                context: context,
                restaurantId: state.lateReviewNotification!.restaurantId
              ).whenComplete(() {
                _appBloc.add(const AppEvent.clearShowReview());
              });
            }
          },
          listenWhen: (previous, current) {
            return previous.lateReviewNotification == null &&
              current.lateReviewNotification != null;
          },
        ),
        BlocListener<AppBloc, AppState>(
          bloc: _appBloc,
          listener: (context, state) {
            if (state.deepLinkQRCodeToken == null &&
                state.isUserLoaded &&
                state.isRestaurantsLoaded &&
                state.storedOrder != null &&
                state.storedOrderStatus == StoredOrderStatus.success &&
                mounted) {
              final storedOrder = state.storedOrder!;
              context.pushNamed(
                MRoutes.orderFlow,
                extra: OrderNavBundle(
                  selfService: storedOrder.selfService,
                  orderAndPayOnly: storedOrder.orderAndPayOnly,
                  restaurantId: storedOrder.restaurantId,
                  restaurantName: storedOrder.restaurantName,
                  isRestaurantFavorite: storedOrder.isRestaurantFavorite,
                  stripeAccountId: storedOrder.stripeAccountId,
                  isFirstOrder: storedOrder.isFirstOrder,
                  dietPreferences: storedOrder.dietPreferences.toList(),
                ),
              );
            }
          },
          listenWhen: (previous, current) {
            return current.deepLinkQRCodeToken == null &&
                current.isUserLoaded &&
                current.isRestaurantsLoaded &&
                current.lateReviewNotification == null &&
                current.storedOrder != null &&
                current.storedOrderStatus == StoredOrderStatus.success;
          },
        ),
        BlocListener<AppBloc, AppState>(
          bloc: _appBloc,
          listener: (context, state) {
            if (state.deepLinkQRCodeToken != null &&
                state.isUserLoaded &&
                state.isRestaurantsLoaded &&
                mounted) {
              final restaurantId = ScannerUtils.getRestaurantIdByToken(
                  state.deepLinkQRCodeToken!);
              final restaurant = _discoverBloc.state.restaurants
                  .singleWhereOrNull((r) => r.id == restaurantId);

              if (restaurant != null) {
                _authBloc.add(AuthEvent.initiateRestaurantSession(
                    state.deepLinkQRCodeToken!));
                _appBloc.add(const AppEvent.setDeepLinkQRCodeToken(null));

                context.pushNamed(
                  MRoutes.orderFlow,
                  extra: OrderNavBundle(
                    selfService: restaurant.selfService,
                    orderAndPayOnly: restaurant.orderAndPayOnly,
                    restaurantId: restaurantId,
                    restaurantName: restaurant.name,
                    isRestaurantFavorite: restaurant.isFavorite,
                    stripeAccountId: restaurant.owner?.stripeAccountId ?? '',
                    isFirstOrder: _authBloc.state.user?.isFirstOrder ?? true,
                    dietPreferences: restaurant.dietPreferences,
                  ),
                );
              }
            }
          },
          listenWhen: (previous, current) {
            return current.deepLinkQRCodeToken != null &&
                current.isUserLoaded &&
                current.isRestaurantsLoaded;
          },
        ),
        BlocListener<AuthBloc, AuthState>(
          bloc: _authBloc,
          listener: (context, state) {
            // Показываем туториал только когда пользователь аутентифицирован и туториалы загружены
            if (state.isAuthenticated && 
                state.tutorialStatus == TutorialStatus.success && 
                state.tutorialStates != null) {
              TutorialUtils.scheduleAfterBuild(this, () {
                Tutorials.showMapQRCodeTutorial(context, scannerButtonKey: _scannerButtonKey);
              });
            }
          },
          listenWhen: (previous, current) {
            // Слушаем только переход в состояние готовности туториалов
            // И только если пользователь изменился или туториалы только что загрузились
            return current.isAuthenticated &&
                   current.tutorialStatus == TutorialStatus.success &&
                   current.tutorialStates != null &&
                   current.user != null &&
                   (previous.tutorialStatus != TutorialStatus.success ||
                    previous.user?.id != current.user?.id);
          },
        ),
      ],
      child: Scaffold(
        backgroundColor: MColors.softCotton,
        appBar:
            const DiscoverAppBar(systemOverlayStyle: SystemUiOverlayStyle.dark),
        body: SafeArea(
          bottom: false,
          child: Stack(
            fit: StackFit.expand,
            clipBehavior: Clip.none,
            children: [
              Positioned.fill(
                top: -MediaQuery.of(context).size.height * 0.2,
                bottom: -MediaQuery.of(context).size.height * 0.2,
                child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height * 1.5,
                    child: BlocConsumer<DiscoverBloc, DiscoverState>(
                      bloc: _discoverBloc,
                      listener: (context, state) {
                        if (state.userPositionStatus ==
                                UserPositionStatus.error &&
                            state.userPositionError != null) {
                          showErrorDialog(
                            context: context,
                            message: state.userPositionError!,
                          );
                        }

                        if (state.status == RestaurantsStatus.error &&
                            state.error != null) {
                          showErrorConfirmDialog(
                            context: context,
                            message: state.error!,
                            onConfirm: () {
                              _discoverBloc.add(DiscoverEvent.fetchRestaurants(
                                  state.city ?? City.oldenburg));
                            },
                          );
                        }
                      },
                      builder: (context, state) {
                        final filteredRestaurants = state.filteredRestaurants;
                        return MapWidget(
                          key: ValueKey(
                              Platform.isAndroid && state.userPosition == null
                                  ? null
                                  : mapKey),
                          userPosition: state.userPosition,
                          markers: state.filteredRestaurants,
                          onMarkerTap: (id) {
                            final generalRestaurant = filteredRestaurants
                                .firstWhere((r) => r.id == id);
                            showRestaurantInfoModal(
                                context: context,
                                id: generalRestaurant.id,
                                onTitleTap: () {
                                  final restaurant =
                                      _discoverBloc.state.getRestaurantById(id);
                                  context.pop();
                                  if (restaurant == null) return;
                                  context.pushNamed(
                                    MRoutes.restaurant,
                                    extra: RestaurantNavBundle(
                                      restaurant: restaurant,
                                    ),
                                  );
                                },
                                onDealsTap: () {
                                  final restaurant =
                                      _discoverBloc.state.getRestaurantById(id);
                                  context.pop();
                                  if (restaurant == null) return;
                                  context.pushNamed(
                                    MRoutes.restaurant,
                                    extra: RestaurantNavBundle(
                                      restaurant: restaurant,
                                      tab: RestaurantInfoTab.deals,
                                    ),
                                  );
                                },
                                onMenuTap: () {
                                  final restaurant =
                                      _discoverBloc.state.getRestaurantById(id);
                                  context.pop();
                                  if (restaurant == null) return;
                                  context.pushNamed(
                                    MRoutes.restaurant,
                                    extra: RestaurantNavBundle(
                                      restaurant: restaurant,
                                      tab: RestaurantInfoTab.menu,
                                    ),
                                  );
                                },
                                onFeedTap: () {
                                  final restaurant =
                                      _discoverBloc.state.getRestaurantById(id);
                                  context.pop();
                                  if (restaurant == null) return;
                                  context.pushNamed(
                                    MRoutes.restaurant,
                                    extra: RestaurantNavBundle(
                                      restaurant: restaurant,
                                      tab: RestaurantInfoTab.feed,
                                    ),
                                  );
                                });
                          },
                        );
                      },
                    )),
              ),
              Positioned(
                top: -displayWidth * 0.32,
                left: 0.0,
                right: 0.0,
                child: const AppBarDecoration(),
              ),
              Positioned(
                bottom: 30.0 + displayWidth * MSizes.kHeightBnB,
                right: 16.0,
                left: 16.0,
                child: BlocBuilder<DiscoverBloc, DiscoverState>(
                  bloc: _discoverBloc,
                  builder: (context, state) {
                    return DiscoverPanel(
                      scannerButtonKey: _scannerButtonKey,
                      isGeoPositionLoading: state.userPositionStatus ==
                          UserPositionStatus.loading,
                      onTapFilter: () {
                        showRestaurantFilterModal(
                            context: context,
                            onComplete: () {
                              _appBloc.add(const AppEvent.setBNBVisible(true));
                            });
                      },
                      onTapScanner: () => context.pushNamed(
                        MRoutes.scanner,
                        extra: ScannerNavBundle(
                          restaurants: state.restaurants,
                          isFirstOrder:
                              _authBloc.state.user?.isFirstOrder ?? false,
                        ),
                      ),
                      onTapPosition: () {
                        if (state.userPositionStatus ==
                            UserPositionStatus.loading) return;
                        _discoverBloc
                            .add(const DiscoverEvent.getUserPosition());
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _restaurantNavigationSubscription?.cancel();
    super.dispose();
  }
}
