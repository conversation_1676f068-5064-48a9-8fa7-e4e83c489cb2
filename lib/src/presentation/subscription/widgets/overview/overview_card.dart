import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_icon.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class OverviewCard extends StatelessWidget {
  final VoidCallback? onTap;
  final String svgIcon;
  final Color iconBackground;
  final Color shadowColor;
  final String label;
  final String discount;
  final double progress;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final double minHeight;
  final bool isActive;
  final LinearGradient? iconGradient;

  const OverviewCard({
    this.onTap,
    required this.svgIcon,
    required this.discount,
    this.iconBackground = Colors.white,
    this.shadowColor = Colors.white,
    this.label = '',
    this.progress = 0.0,
    this.minHeight = 120.0,
    this.margin = const EdgeInsets.only(bottom: 20.0),
    this.padding = const EdgeInsets.only(top: 20.0, bottom: 20.0, left: 32.0, right: 16.0),
    this.isActive = false,
    this.iconGradient,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        constraints: BoxConstraints(
          minHeight: minHeight,
        ),
        margin: margin,
        padding: padding,
        decoration: BoxDecoration(
          color: Colors.white,
          gradient: isActive ? MStyles.activeCard : null,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: MColors.onyxShadow.withOpacity(.08),
              blurRadius: 4.0,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                OverviewIcon(
                  svgIcon: svgIcon,
                  background: iconBackground,
                  shadowColor: shadowColor,
                  gradient: iconGradient,
                ),
                const SizedBox(width: 12.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 3.0),
                      child: Text(
                        label,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          height: 1.0
                        ),
                      ),
                    ),
                    Visibility(
                      visible: progress > 0.0,
                      child: SizedBox(
                        width: 42.0,
                        child: LinearProgressIndicator(
                          value: progress,
                          color: MColors.onyxShadow,
                          backgroundColor: MColors.silverStone,
                          minHeight: 2.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('$discount %', style: Theme.of(context).textTheme.headlineLarge),
                Text(
                  t.screens.subscription.discountOnTotalBill,
                  style: Theme.of(context).textTheme.labelLarge,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  softWrap: true,
                  maxLines: 2,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
