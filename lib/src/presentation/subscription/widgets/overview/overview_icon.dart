import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class OverviewIcon extends StatelessWidget {
  final String svgIcon;
  final Color background;
  final Color shadowColor;
  final LinearGradient? gradient;
  final double width;
  final double height;
  final double iconSize;
  final double borderRadius;

  const OverviewIcon({
    required this.svgIcon,
    this.background = Colors.white,
    this.shadowColor = Colors.white,
    this.gradient,
    this.width = 38.0,
    this.height = 38.0,
    this.iconSize = 24.0,
    this.borderRadius = 10.0,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: background,
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: gradient,
        boxShadow: [
          BoxShadow(
            color: shadowColor.withOpacity(.23),
            blurRadius: 10.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      alignment: Alignment.center,
      child: SvgPicture.asset(
        svgIcon,
        width: iconSize,
        height: iconSize,
      ),
    );
  }
}
