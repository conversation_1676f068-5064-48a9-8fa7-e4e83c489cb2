import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/subscription/subscription_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/subscription/widgets/modal/restaraunt_points.dart';
import 'package:mutualz/src/presentation/subscription/widgets/modal/subscription_button.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_icon.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/buttons/m_elevated_button.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/error_dialog.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/success_dialog.dart';
import 'package:slang/builder/utils/string_extensions.dart';

const bool _paymentsTemporarilyDisabled = true;

enum SubscriptionType {
  test,
  member,
  silver,
  gold;

  String get name => switch (this) {
    SubscriptionType.test => t.screens.subscription.levels.test,
    SubscriptionType.member => t.screens.subscription.levels.member,
    SubscriptionType.silver => t.screens.subscription.levels.silver,
    SubscriptionType.gold => t.screens.subscription.levels.gold,
  };
}

Future<void> showSubscriptionModelInfo({
  required BuildContext context,
  required SubscriptionType type,
  double points = 0,
  double maxMemberPoints = 0,
  List<UserLoyaltyModel> userLoyalties = const [],
  bool showPaymentPanel = false,
}) => showDialog(
  context: context,
  barrierColor: Colors.white.withOpacity(.6),
  builder: (context) => BasicDialogDecorator(
    child: _SubscriptionModelInfo(
      type: type,
      points: points,
      maxMemberPoints: maxMemberPoints,
      showPaymentPanel: showPaymentPanel,
      userLoyalties: userLoyalties,
    ),
  ),
);

class _SubscriptionModelInfo extends StatefulWidget {
  final SubscriptionType type;
  final double points;
  final double maxMemberPoints;
  final bool showPaymentPanel;
  final List<UserLoyaltyModel> userLoyalties;

  const _SubscriptionModelInfo({
    required this.type,
    this.points = 0,
    this.maxMemberPoints = 0,
    this.showPaymentPanel = false,
    this.userLoyalties = const [],
  });

  @override
  State<_SubscriptionModelInfo> createState() => _SubscriptionModelInfoState();
}

class _SubscriptionModelInfoState extends State<_SubscriptionModelInfo> {
  late final SubscriptionBloc _subscriptionBloc = context.read<SubscriptionBloc>();
  late final AuthBloc _authBloc = context.read<AuthBloc>();
  late final UserLoyaltyBloc _userLoyaltyBloc = context.read<UserLoyaltyBloc>();

  @override
  Widget build(BuildContext context) {
    return  Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        OverviewIcon(
          svgIcon: switch (widget.type) {
            SubscriptionType.test => MAssets.iconTest,
            SubscriptionType.member => MAssets.iconMember,
            SubscriptionType.silver => MAssets.iconSilver,
            SubscriptionType.gold => MAssets.iconGold,
          },
          background: switch (widget.type) {
            SubscriptionType.test => MColors.azureSky,
            SubscriptionType.member => MColors.scarletEmber,
            SubscriptionType.silver => MColors.black,
            SubscriptionType.gold => MColors.apricotSunset,
          },
          shadowColor: switch (widget.type) {
            SubscriptionType.test => MColors.azureSky,
            SubscriptionType.member => MColors.white,
            SubscriptionType.silver => MColors.black,
            SubscriptionType.gold => MColors.apricotSunset,
          },
          gradient: switch (widget.type) {
            SubscriptionType.test => null,
            SubscriptionType.member => null,
            SubscriptionType.silver => null,
            SubscriptionType.gold => const LinearGradient(
              colors: [
                MColors.tangerineOrange,
                MColors.vividYellow,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          },
        ),
        const SizedBox(height: 12.0),
        Text(
          switch (widget.type) {
            SubscriptionType.test => t.screens.subscription.levels.test,
            SubscriptionType.member => t.screens.subscription.levels.member,
            SubscriptionType.silver => t.screens.subscription.levels.silver,
            SubscriptionType.gold => t.screens.subscription.levels.gold,
          },
          style: Theme.of(context).textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12.0),
        Text(
          switch (widget.type) {
            SubscriptionType.test => t.screens.subscription.levelsDescription.test,
            SubscriptionType.member => t.screens.subscription.levelsDescription.member,
            SubscriptionType.silver => t.screens.subscription.levelsDescription.silver,
            SubscriptionType.gold => t.screens.subscription.levelsDescription.gold,
          },
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: MColors.mediumGray,
          ),
          textAlign: TextAlign.center,
        ),
        BlocBuilder<AuthBloc, AuthState>(
          bloc: _authBloc,
          builder: (context, state) {
            return Visibility(
            visible: state.user != null &&
              state.user!.hasCancelledSubscription &&
              state.user!.availableSubscriptionTill != null &&
              widget.type == SubscriptionType.member,
              child: Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Text(
                  t.screens.subscription.info(date: state.user!.availableSubscriptionTill ?? ''),
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: MColors.scarletEmber,
                  ),
                ),
              ),
            );
          }
        ),
        const SizedBox(height: 12.0),
        Visibility(
          visible: widget.type == SubscriptionType.member && widget.showPaymentPanel,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: BlocConsumer<SubscriptionBloc, SubscriptionState>(
                    bloc: _subscriptionBloc,
                    listener: (context, state) {
                      if (state.createSubscriptionStatus == CreateSubscriptionStatus.success ||
                          state.cancelSubscriptionStatus == CancelSubscriptionStatus.success) {
                        showSuccessDialog(context: context).whenComplete(() {
                          _authBloc.add(const AuthEvent.fetchUser());
                          _userLoyaltyBloc.add(const UserLoyaltyEvent.fetchUserLoyalty());
                          if (mounted) context.pop();
                        });
                      }

                      if (state.createSubscriptionStatus == CreateSubscriptionStatus.error &&
                          state.errorCreateMessage != null) {
                        showErrorDialog(
                          context: context,
                          message: state.errorCreateMessage!,
                        ).whenComplete(() {
                          _userLoyaltyBloc.add(const UserLoyaltyEvent.fetchUserLoyalty());
                        });
                      }

                      if (state.cancelSubscriptionStatus == CancelSubscriptionStatus.error &&
                          state.errorCancelMessage != null) {
                        showErrorDialog(
                          context: context,
                          message: state.errorCancelMessage!,
                        ).whenComplete(() {
                          _userLoyaltyBloc.add(const UserLoyaltyEvent.fetchUserLoyalty());
                        });
                      }
                    },
                    builder: (context, state) {
                      return BlocBuilder<AuthBloc, AuthState>(
                        bloc: _authBloc,
                        buildWhen: (previous, current) => previous.user != current.user,
                        builder: (context, authState) {
                          if (_paymentsTemporarilyDisabled) {
                            return const SizedBox.shrink();
                          }
                          
                          return SubscriptionButton(
                            label: authState.user != null && authState.user!.hasActiveSubscription  ?
                            t.screens.subscription.buttons.cancel :
                              authState.user!.hasCancelledSubscription ?
                            t.screens.subscription.buttons.reactivate :
                            t.screens.subscription.buttons.upgrade,
                            isLoading: state.createSubscriptionStatus == CreateSubscriptionStatus.loading ||
                              state.cancelSubscriptionStatus == CancelSubscriptionStatus.loading,
                            onTap: () {
                              authState.user!.hasActiveSubscription ?
                              _subscriptionBloc.add(const SubscriptionEvent.cancelSubscription()) :
                              _subscriptionBloc.add(SubscriptionEvent.createSubscription(
                                authState.user!.hasCancelledSubscription,
                              ));
                            },
                          );
                        }
                      );
                    }
                  ),
                ),
                // const SizedBox(width: 12.0),
                // Expanded(
                //   child: SubscriptionButton(
                //     label: t.screens.subscription.buttons.recommend,
                //     onTap: () {},
                //   ),
                // ),
              ],
            ),
          ),
        ),
        switch (widget.type) {
          SubscriptionType.test => const SizedBox(),
          SubscriptionType.member => BlocBuilder<AuthBloc, AuthState>(
            bloc: _authBloc,
            builder: (context, state) => Visibility(
              visible: state.user != null && !state.user!.hasActiveSubscription,
              child: RestaurantPoints(
                points:  widget.points > widget.maxMemberPoints ?
                '${widget.type.name.capitalize()} ${t.custom.isReached}' : '${widget.points.toInt()} / ${widget.maxMemberPoints.toInt()}',
                progress: widget.points / widget.maxMemberPoints,
              ),
            ),
          ),
          SubscriptionType.silver => Column(
            children: widget.userLoyalties.where((e) =>
            e.restaurant != null && e.status == null && e.restaurant?.loyalty != null &&
                e.restaurant?.loyalty?.pointsForSilver != null  &&
                e.restaurant?.loyalty?.pointsForGold != null
            ).map((e) => RestaurantPoints(
              progress: e.points / e.restaurant!.loyalty!.pointsForSilver.toInt(),
              points: e.points > (e.restaurant?.loyalty?.pointsForSilver ?? 0).toInt() ?
              '${widget.type.name.capitalize()} ${t.custom.isReached}' : '${e.points.toInt()} / ${e.restaurant!.loyalty?.pointsForSilver.toInt() ?? 0}',
              restaurantName: e.restaurant?.name ?? '',
            )).toList(),
          ),
          SubscriptionType.gold => Column(
            children: widget.userLoyalties.where((e) =>
            e.restaurant != null && e.restaurant?.loyalty != null &&
                e.restaurant?.loyalty?.pointsForSilver != null &&
                e.restaurant?.loyalty?.pointsForGold != null
            ).map((e) => RestaurantPoints(
              progress: e.points / e.restaurant!.loyalty!.pointsForGold.toInt(),
              points: (e.points > (e.restaurant?.loyalty?.pointsForGold ?? 0)) ?
              '${widget.type.name.capitalize()} ${t.custom.isReached}' : '${e.points.toInt()} / ${e.restaurant!.loyalty?.pointsForGold.toInt() ?? 0}',
              restaurantName: e.restaurant?.name ?? '',
            )).toList(),
          ),
        },
        MElevatedButton(
          margin: const EdgeInsets.only(top: 24.0),
          width: 256.0,
          height: 50.0,
          background: MColors.white,
          boxShadowColor: const Color(0xFF606060).withOpacity(.2),
          textColor: MColors.deepBlack,
          boxShadowRadius: 20.0,
          label: t.buttons.ok,
          onPressed: () => context.pop(),
        ),
      ],
    );
  }
}
