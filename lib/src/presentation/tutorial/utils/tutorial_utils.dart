import 'package:flutter/material.dart';

class TutorialUtils {
  static void scheduleAfterBuild(
    State state,
    VoidCallback callback, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(delay, () {
        if (state.mounted) {
          callback();
        }
      });
    });
  }

  static void pushWithoutAnimation(BuildContext context, Widget screen) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionDuration: Duration.zero,
        reverseTransitionDuration: Duration.zero,
      ),
    );
  }
}