import 'package:flutter/material.dart';

class OutlineCircleBorder extends CircleBorder {
  const OutlineCircleBorder({
    this.gap = 1,
    required BorderSide side,
  }) : super(side: side);

  final double gap;

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    final inflatedRect = rect.inflate(gap);
    return Path()..addOval(inflatedRect);
  }

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()..addOval(rect);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    if (side == BorderSide.none) return;
    
    final inflatedRect = rect.inflate(gap);
    final paint = Paint()
      ..color = side.color
      ..strokeWidth = side.width
      ..style = PaintingStyle.stroke;
    
    canvas.drawOval(inflatedRect, paint);
  }

  @override
  OutlineCircleBorder copyWith({BorderSide? side, double? eccentricity}) {
    return OutlineCircleBorder(
      side: side ?? this.side,
      gap: gap,
    );
  }
}

class OutlineRoundedRectangleBorder extends RoundedRectangleBorder {
  const OutlineRoundedRectangleBorder({
    this.gap = 1,
    required BorderSide side,
    super.borderRadius = BorderRadius.zero,
  }) : super(side: side);

  final double gap;

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    final inflatedRect = rect.inflate(gap);
    final radius = borderRadius.resolve(textDirection);
    return Path()..addRRect(radius.toRRect(inflatedRect));
  }

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    final radius = borderRadius.resolve(textDirection);
    return Path()..addRRect(radius.toRRect(rect));
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    if (side == BorderSide.none) return;
    
    final inflatedRect = rect.inflate(gap);
    final radius = borderRadius.resolve(textDirection);
    final paint = Paint()
      ..color = side.color
      ..strokeWidth = side.width
      ..style = PaintingStyle.stroke;
    
    canvas.drawRRect(radius.toRRect(inflatedRect), paint);
  }

  @override
  OutlineRoundedRectangleBorder copyWith({BorderSide? side, BorderRadiusGeometry? borderRadius}) {
    return OutlineRoundedRectangleBorder(
      side: side ?? this.side,
      borderRadius: borderRadius ?? this.borderRadius,
      gap: gap,
    );
  }
}
