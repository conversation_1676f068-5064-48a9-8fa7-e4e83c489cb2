import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_icon.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class LoyaltyTutorialPageSix extends StatelessWidget {
  const LoyaltyTutorialPageSix({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Image.asset(
            MAssets.loyaltyTutorialLevels,
            fit: BoxFit.contain,
          ),
        ),
        Text(
          t.tutorial.loyalty.oneCalendarMonth.toUpperCase(),
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: MColors.onyxShadow, fontSize: 26),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 40),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.18),
          child: Text(
            t.tutorial.loyalty.subscription,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: MColors.charcoalGray,
                  fontSize: 19,
                  height: 1.5,
                ),
          ),
        ),
      ],
    );
  }
}
