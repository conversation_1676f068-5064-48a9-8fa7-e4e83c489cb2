import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class LoyaltyTutorialPageOne extends StatelessWidget {
  const LoyaltyTutorialPageOne({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 40, top: 20),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'LOYALTY-\nPROGRAMM',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: MColors.onyxShadow,
                  ),
            ),
          ),
        ),
        const SizedBox(height: 50),
        Image.asset(
          MAssets.loyaltyTutorialLoyalty,
          fit: BoxFit.contain,
          height: context.height * 0.4,
        ),
        const SizedBox(height: 40),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.18),
          child: Text(
            t.tutorial.loyalty.fourLevels,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: MColors.charcoalGray,
                ),
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
