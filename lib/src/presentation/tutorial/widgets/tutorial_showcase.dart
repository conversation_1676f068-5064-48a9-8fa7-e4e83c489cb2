import 'package:flutter/material.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:showcaseview/showcaseview.dart';

class TutorialShowcase extends StatelessWidget {
  final GlobalKey showcaseKey;
  final Widget child;
  final Widget container;
  final double? width;
  final double? height;
  final TooltipPosition tooltipPosition;
  final double overlayOpacity;
  final Color overlayColor;
  final bool disableBarrierInteraction;
  final bool disableMovingAnimation;
  final BorderRadius? targetBorderRadius;
  final EdgeInsets? targetPadding;
  final VoidCallback? onTargetClick;
  final VoidCallback? onBarrierClick;
  final VoidCallback? onTap;
  final OutlinedBorder? targetShapeBorder;
  final double targetBorderWidth;
  final Clip? clipBehavior;
  final bool enabled;
  final bool disposeOnTap;

  const TutorialShowcase({
    super.key,
    required this.showcaseKey,
    required this.child,
    required this.container,
    this.width,
    this.height,
    this.tooltipPosition = TooltipPosition.top,
    this.overlayOpacity = 0.92,
    this.overlayColor = Colors.white,
    this.disableBarrierInteraction = false,
    this.disableMovingAnimation = true,
    this.targetBorderRadius,
    this.targetPadding,
    this.onTargetClick,
    this.onBarrierClick,
    this.targetShapeBorder,
    this.targetBorderWidth = 4,
    this.clipBehavior,
    this.onTap,
    this.enabled = true,
    this.disposeOnTap = false,
  });

  @override
  Widget build(BuildContext context) {
    if (!enabled) {
      return child;
    }

    return Showcase.withWidget(
      key: showcaseKey,
      width: width ?? context.width * 0.92,
      height: height,
      tooltipPosition: tooltipPosition,
      overlayOpacity: overlayOpacity,
      overlayColor: overlayColor.withAlpha(200),
      disableBarrierInteraction: disableBarrierInteraction,
      disableMovingAnimation: disableMovingAnimation,
      disposeOnTap: disposeOnTap,
      onTargetClick: (onTap != null || onTargetClick != null) ? () {
        (onTap ?? onTargetClick!)();

        Future.microtask(() {
          if (context.mounted) {
            ShowCaseWidget.of(context).next();
          }
        });
      } : null,
      onBarrierClick: onTap ?? onBarrierClick,
      targetShapeBorder: targetShapeBorder ?? const OutlineCircleBorder(gap: 1, side: BorderSide.none),
      targetBorderRadius: targetBorderRadius,
      targetPadding: targetPadding ?? EdgeInsets.zero,
      container: container,
      child: child,
    );
  }
}
