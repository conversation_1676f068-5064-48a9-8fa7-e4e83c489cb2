import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/subscription/widgets/overview/overview_icon.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class LoyaltyTutorialPageThree extends StatelessWidget {
  const LoyaltyTutorialPageThree({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        Text(
          '${t.tutorial.loyalty.level.toUpperCase()} 1',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: MColors.onyxShadow),
        ),
        const SizedBox(height: 40),
        PointProgress.memberToSilver(
          from: 70,
          to: 100,
          width: context.width * 0.8,
          showBottomText: false,
        ),
        const SizedBox(height: 30),
        const OverviewIcon(
          svgIcon: MAssets.iconMember,
          background: MColors.scarletEmber,
          iconSize: 80,
          width: 120,
          height: 120,
          borderRadius: 32,
        ),
        const SizedBox(height: 30),
        Text(
          t.loyalty.member.toUpperCase(),
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: MColors.onyxShadow),
        ),
        const SizedBox(height: 20),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.width * 0.18),
          child: Text(
            t.tutorial.loyalty.member,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: MColors.charcoalGray,
                  height: 1.5,
                ),
          ),
        ),
        // const Spacer(),
      ],
    );
  }
}
