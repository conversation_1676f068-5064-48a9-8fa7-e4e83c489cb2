import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/extensions/date_time_extensions.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/constants/loyalty_magic_numbers.dart';
import 'package:mutualz/src/domain/models/constants/loyalty_status.dart';
import 'package:mutualz/src/domain/models/restaurant/restaurant_loyalty.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:mutualz/src/domain/models/user/user.dart';
import 'package:mutualz/i18n/strings.g.dart';

class LoyaltyWidget extends StatefulWidget {
  final String restaurantId;
  final RestaurantLoyalty? restaurantLoyalty;
  final bool showSubscriptionDiscount;

  const LoyaltyWidget({
    required this.restaurantId,
    this.restaurantLoyalty,
    this.showSubscriptionDiscount = false,
    super.key
  });

  @override
  State<LoyaltyWidget> createState() => _LoyaltyWidgetState();
}

class _LoyaltyWidgetState extends State<LoyaltyWidget> {
  late final UserLoyaltyBloc _userLoyaltyBloc;

  @override
  void initState() {
    super.initState();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
  }
  

  /// Returns the discount info for the current loyalty status
  ({int days, String? percent})? _getDiscountInfo(User user, UserLoyaltyState loyaltyState) {
    final currentLoyalty = loyaltyState.loyalty?.loyalty.firstWhereOrNull(
      (element) => element.restaurant?.id == widget.restaurantId,
    );

    // Priority order: Gold > Silver > Member > None
    return switch (currentLoyalty?.status) {
      LoyaltyStatus.gold => (
        days: DateTimeExtension.daysUntilEndOfCurrentMonth,
        percent: '15'
      ),
      LoyaltyStatus.silver => (
        days: DateTimeExtension.daysUntilEndOfCurrentMonth,
        percent: '10'
      ),
      _ when user.hasActiveSubscription && user.subscription?.expireAt != null => (
        days: user.subscription!.expireAt.daysFromNow,
        percent: '5'
      ),
      _ => (
        days: DateTimeExtension.daysUntilEndOfCurrentMonth,
        percent: null
      ),
    };
  }

  String? _getSubscriptionDiscountText(User? user, UserLoyaltyState loyaltyState) {
    if (user == null) return null;

    final discountInfo = _getDiscountInfo(user, loyaltyState);
    if (discountInfo == null || discountInfo.days <= 0) return null;

    return discountInfo.percent != null
        ? t.screens.subscription.discount.withPercent(n: discountInfo.days, percent: discountInfo.percent!)
        : t.screens.subscription.daysRemaining(n: discountInfo.days);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showSubscriptionDiscount) ...[
          BlocBuilder<UserLoyaltyBloc, UserLoyaltyState>(
            bloc: _userLoyaltyBloc,
            builder: (context, loyaltyState) {
              return BlocBuilder<AuthBloc, AuthState>(
                builder: (context, authState) {
                  final discountText = _getSubscriptionDiscountText(authState.user, loyaltyState);
                  if (discountText == null) {
                    return const SizedBox.shrink();
                  }
                  return Center(
                    child: Text(
                      discountText,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
        GestureDetector(
          onTap: () => context.pushNamed(MRoutes.subscriptionOverview),
          child: BlocBuilder<UserLoyaltyBloc, UserLoyaltyState>(
            bloc: _userLoyaltyBloc,
            builder: (context, loyaltyState) {
          if (loyaltyState.loyalty != null &&
              loyaltyState.loyalty?.status == LoyaltyStatus.test) {
            return PointProgress.testToMember(
              margin: const EdgeInsets.only(bottom: 24.0, top: 12.0),
              from: loyaltyState.loyalty?.totalPoints ?? 0.0,
              to: loyaltyMemberPoints,
              width: double.infinity,
            );
          }

          final currentLoyalty = loyaltyState.loyalty?.loyalty.firstWhereOrNull(
            (element) => element.restaurant?.id == widget.restaurantId,
          );

          if (loyaltyState.loyalty == null ||
              loyaltyState.loyalty?.status == LoyaltyStatus.test && currentLoyalty == null) {
            return PointProgress.testToMember(
              margin: const EdgeInsets.only(bottom: 24.0, top: 12.0),
              from: loyaltyState.loyalty?.totalPoints ?? 0.0,
              to:loyaltyState.loyalty?.memberStatusPoints ?? 0.0,
              width: double.infinity,
            );
          }

          if (currentLoyalty == null &&
              loyaltyState.loyalty?.status == LoyaltyStatus.member &&
              loyaltyState.loyalty?.memberStatusPoints != null) {
            return PointProgress.memberToSilver(
              margin: const EdgeInsets.only(bottom: 24.0, top: 12.0),
              from: loyaltyState.loyalty?.memberStatusPoints ?? 0.0,
              to: widget.restaurantLoyalty?.pointsForSilver ?? 0.0,
              width: double.infinity,
            );
          }

          if (currentLoyalty != null && loyaltyState.loyalty?.status == LoyaltyStatus.member &&
              currentLoyalty.restaurant != null && currentLoyalty.status == null
          ) {
            return PointProgress.memberToSilver(
              margin: const EdgeInsets.only(bottom: 24.0, top: 12.0),
              from: currentLoyalty.points,
              to: currentLoyalty.restaurant?.loyalty?.pointsForSilver ?? currentLoyalty.points,
              width: double.infinity,
            );
          }

          if (currentLoyalty != null &&
              currentLoyalty.restaurant != null &&
              (currentLoyalty.status == LoyaltyStatus.silver ||
                  currentLoyalty.status == LoyaltyStatus.gold)
          ) {
            return PointProgress.silverToGold(
              margin: const EdgeInsets.only(bottom: 24.0, top: 12.0),
              from: currentLoyalty.points,
              to: currentLoyalty.restaurant!.loyalty!.pointsForGold,
              width: double.infinity,
              isReached: currentLoyalty.status == LoyaltyStatus.gold &&
                currentLoyalty.points >= currentLoyalty.restaurant!.loyalty!.pointsForGold,
              status: currentLoyalty.status?.name,
            );
          }

          return const SizedBox();
            },
          ),
        ),
      ],
    );
  }
}
