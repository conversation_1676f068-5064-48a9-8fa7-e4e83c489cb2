import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/onboarding/onboarding_bloc.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/onboarding/widgets/lifestyle_selection.dart';
import 'package:mutualz/src/presentation/onboarding/widgets/onboarding_title.dart';
import 'package:mutualz/src/presentation/onboarding/widgets/personal_info_selection.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});
  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final OnboardingBloc _bloc = getIt<OnboardingBloc>();

  double _calculateHeight(OnboardingType type) => type == OnboardingType.personalInfo ? .55 : type == OnboardingType.lifestyle ? .6 : .75;

  @override
  Widget build(BuildContext context) {
    final t = Translations.of(context);
    final tls = t.screens.onboarding.lifestyle;
    final tts = t.screens.onboarding.taste;
    return BlocProvider(
      create: (_) => _bloc,
      child: BlocConsumer<OnboardingBloc, OnboardingState>(
        listener: (context, state) {
          if (state.status == OnboardingStatus.success) {
            context.goNamed(MRoutes.notificationPermission);
          }

          if (state.status == OnboardingStatus.error && state.error != null) {
            showErrorConfirmDialog(
              context: context,
              message: state.error!,
              cancelLabel: t.buttons.skip,
              onConfirm: () {
                _bloc.add(const OnboardingEvent.next());
              },
              onCancel: () {
                context.goNamed(MRoutes.notificationPermission);
              },
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            body: SafeArea(
              bottom: false,
              child: Stack(
                children: [
                  Positioned(
                    top: 40.0,
                    left: 32.0,
                    right: 32.0,
                    child: OnBoardingTitle(
                      title: state.type == OnboardingType.personalInfo 
                        ? t.screens.onboarding.personalInfo.title
                        : state.type == OnboardingType.lifestyle 
                          ? tls.title 
                          : tts.title,
                      description: state.type == OnboardingType.personalInfo 
                        ? t.screens.onboarding.personalInfo.description
                        : state.type == OnboardingType.lifestyle 
                          ? tls.description 
                          : tts.description,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * _calculateHeight(state.type),
                      decoration: BoxDecoration(
                        color: MColors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(MSizes.cardBorderRadius),
                          topRight: Radius.circular(MSizes.cardBorderRadius),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: MColors.black.withOpacity(.05),
                            blurRadius: 4.0,
                            offset: const Offset(0, -2.0),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 50.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 32.0),
                              child: switch (state.type) {
                                OnboardingType.personalInfo => PersonalInfoSelection(
                                  key: const Key('PersonalInfoSelection'),
                                  gender: state.gender,
                                  firstName: state.firstName,
                                  lastName: state.lastName,
                                  phoneNumber: state.phoneNumber,
                                  onGenderChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeGender(value)),
                                  onFirstNameChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeFirstName(value)),
                                  onLastNameChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeLastName(value)),
                                  onPhoneNumberChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changePhoneNumber(value)),
                                ),
                                OnboardingType.lifestyle => LifeStyleSelection(
                                  key: const Key('LifeStyleSelection'),
                                  job: state.job,
                                  allergy: state.allergy.map((e) => e.label).toList().join(', '),
                                  diet: state.diet.map((e) => e.label).join(', '),
                                  onJobChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeJob(value)),
                                  onAllergyChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeAllergy(value)),
                                  onDietChanged: (value) =>
                                    _bloc.add(OnboardingEvent.changeDiet(value)),
                                ),
                                OnboardingType.drink => TasteSelection(
                                  key: const Key('DrinkSelection'),
                                  title: t.screens.onboarding.drinks.title,
                                  initialItems: state.drinks,
                                  items: drinks,
                                  onSelect: (value) =>
                                    _bloc.add(OnboardingEvent.changeDrinks(value.toList())),
                                ),
                                OnboardingType.food => TasteSelection(
                                  key: const Key('FoodSelection'),
                                  title: t.screens.onboarding.food.title,
                                  initialItems: state.food,
                                  items: foods,
                                  onSelect: (value) =>
                                    _bloc.add(OnboardingEvent.changeFood(value.toList())),
                                ),
                                OnboardingType.event => TasteSelection(
                                  key: const Key('EventSelection'),
                                  title: t.screens.onboarding.events.title,
                                  initialItems: state.events,
                                  items: events,
                                  onSelect: (value) =>
                                    _bloc.add(OnboardingEvent.changeEvents(value.toList())),
                                ),
                              }
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(
                              left: 32.0, right: 32.0, top: 16.0, bottom: 16.0),
                            child: MElevatedButton(
                              isDisabled: _bloc.isDisabled,
                              isLoading: state.status == OnboardingStatus.loading,
                              onPressed: () => _bloc.add(const OnboardingEvent.next()),
                              suffix: Padding(
                                padding: const EdgeInsets.only(left: 2.0),
                                child: SvgPicture.asset(MAssets.arrowRight),
                              ),
                              label: t.screens.onboarding.nextButtonLabel,
                            ),
                          ),
                          Visibility(
                            visible: state.type != OnboardingType.personalInfo,
                            child: GestureDetector(
                              onTap: () => _bloc.add(const OnboardingEvent.back()),
                              child: Text(
                                t.screens.onboarding.backButtonLabel,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: MColors.midnightNavySlate,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      ),
    );
  }
}
