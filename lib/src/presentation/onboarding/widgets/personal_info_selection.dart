import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/domain/models/constants/gender.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class PersonalInfoSelection extends StatefulWidget {
  final Gender? gender;
  final String firstName;
  final String lastName;
  final String phoneNumber;
  final Function(Gender) onGenderChanged;
  final Function(String) onFirstNameChanged;
  final Function(String) onLastNameChanged;
  final Function(String) onPhoneNumberChanged;

  const PersonalInfoSelection({
    required this.onGenderChanged,
    required this.onFirstNameChanged,
    required this.onLastNameChanged,
    required this.onPhoneNumberChanged,
    this.gender,
    this.firstName = '',
    this.lastName = '',
    this.phoneNumber = '',
    super.key,
  });

  @override
  State<PersonalInfoSelection> createState() => _PersonalInfoSelectionState();
}

class _PersonalInfoSelectionState extends State<PersonalInfoSelection> {
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _phoneNumberController;

  final FocusNode _firstNameFN = FocusNode();
  final FocusNode _lastNameFN = FocusNode();
  final FocusNode _phoneNumberFN = FocusNode();

  @override
  void initState() {
    super.initState();
    _firstNameController = TextEditingController(text: widget.firstName);
    _lastNameController = TextEditingController(text: widget.lastName);
    _phoneNumberController = TextEditingController(text: widget.phoneNumber);
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneNumberController.dispose();
    _firstNameFN.dispose();
    _lastNameFN.dispose();
    _phoneNumberFN.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final t = Translations.of(context);
    
    return ListView(
      children: [
        MExpansionTile(
          title: widget.gender?.label ?? '',
          color: MColors.silverMist,
          placeholder: t.screens.auth.registration.labels.gender,
          items: Gender.values.map((e) => e.label).toList(),
          isMultiplyChoice: false,
          onSelected: (value) {
            final gender = Gender.values.firstWhere((e) => e.label == value.first);
            widget.onGenderChanged(gender);
          },
        ),
        MTextField(
          hintText: t.screens.auth.registration.labels.firstName,
          textEditingController: _firstNameController,
          focusNode: _firstNameFN,
          inputFormatters: [
            FilteringTextInputFormatter.allow(regExpLatinExtendedWithSpecials),
          ],
          keyboardType: TextInputType.text,
          textCapitalization: TextCapitalization.words,
          onChanged: widget.onFirstNameChanged,
        ),
        const SizedBox(height: 16.0),
        MTextField(
          hintText: t.screens.auth.registration.labels.lastName,
          textEditingController: _lastNameController,
          focusNode: _lastNameFN,
          inputFormatters: [
            FilteringTextInputFormatter.allow(regExpLatinExtendedWithSpecials),
          ],
          keyboardType: TextInputType.text,
          textCapitalization: TextCapitalization.words,
          onChanged: widget.onLastNameChanged,
        ),
        const SizedBox(height: 16.0),
        MPhoneField(
          hintText: t.screens.auth.registration.labels.phone,
          textEditingController: _phoneNumberController,
          focusNode: _phoneNumberFN,
          initialValue: null,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            InputFormatters.phoneMaskInputFormatter,
          ],
          keyboardType: Platform.isIOS
              ? const TextInputType.numberWithOptions(signed: true, decimal: true)
              : TextInputType.phone,
          onChanged: (phone) => widget.onPhoneNumberChanged('${phone.code}${phone.number}'),
        ),
      ],
    );
  }
}