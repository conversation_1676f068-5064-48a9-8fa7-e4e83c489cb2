import 'dart:convert';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/app/app_bloc.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});
  @override
  State<SplashScreen> createState() => _SplashScreenState();
  
  static RemoteMessage? _pendingInitialMessage;
  
  static RemoteMessage? getPendingMessage() {
    final message = _pendingInitialMessage;
    _pendingInitialMessage = null;
    return message;
  }
  
  static void savePendingMessage(RemoteMessage message) {
    _pendingInitialMessage = message;
  }
}

class _SplashScreenState extends State<SplashScreen> {
  final TokenHandler _tokenHandler = getIt<TokenHandler>();
  final MLogger _logger = getIt<MLogger>();
  RemoteMessage? _initialMessage;
  
  @override
  void initState() {
    super.initState();
    _checkForInitialMessage();
  }
  
  Future<void> _checkForInitialMessage() async {
    try {
      // Check if app was launched by FCM notification click
      final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      
      if (initialMessage != null) {
        _initialMessage = initialMessage;
      }
      
      // Check if app was launched by local notification click
      final notificationService = getIt<NotificationService>();
      final launchDetails = await notificationService
          .getFlutterLocalNotificationsPlugin()
          .getNotificationAppLaunchDetails();
          
      if (launchDetails != null && 
          launchDetails.didNotificationLaunchApp && 
          launchDetails.notificationResponse?.payload != null) {
        try {
          final payload = launchDetails.notificationResponse!.payload!;
          final data = json.decode(payload);
          
          if (
            data['notificationType'] == 'late_review' || 
            data['notificationType'] == 'couponRefreshmentAvailable'
          ) {
            _initialMessage = RemoteMessage(data: data);
          }
        } catch (e) {
          _logger.error('Error parsing notification payload: $e');
        }
      }
    } catch (e) {
      _logger.error('Error checking for initial message: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final t = Translations.of(context);
    return Hero(
      tag: 'splash',
      child: Scaffold(
        backgroundColor: Colors.black,
        body: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                DefaultTextStyle(
                  style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                    color: Colors.white,
                  ),
                  child: AnimatedTextKit(
                    totalRepeatCount: 1,
                    animatedTexts: [
                      TyperAnimatedText(
                        t.screens.splash.title.toUpperCase(),
                        speed: const Duration(milliseconds: 200),
                        curve: Curves.easeInOut,
                      ),
                    ],
                    onFinished: () {
                      _tokenHandler.tokensValidityCheck().then((isValid) {
                        _tokenHandler.changeTokenValidity(isValid);
                        
                        // Save message for processing after navigation
                        if (_initialMessage != null) {
                          SplashScreen.savePendingMessage(_initialMessage!);
                        }
                        
                        if (isValid) {
                          context.goNamed(MRoutes.discover);
                        } else {
                          context.goNamed(MRoutes.auth);
                        }
                      }).catchError((e) {
                        context.goNamed(MRoutes.auth);
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
