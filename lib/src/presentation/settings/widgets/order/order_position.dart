import 'package:flutter/material.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/domain/models/orders/special_order_component.dart';

import '../../../theme/theme.dart';

class OrderPosition extends StatelessWidget {
  final String name;
  final int quantity;
  final String price;
  final List<ExtraModel> extras;
  final List<SpecialOrderComponent> components;

  const OrderPosition({
    this.name = '',
    this.quantity = 1,
    this.price = '',
    this.extras = const [],
    this.components = const [],
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                   '$quantity x $name',
                  style: Theme.of(context).textTheme.labelLarge,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
              ),
              const SizedBox(width: 4.0),
              Text(
                price,
                style: Theme.of(context).textTheme.labelLarge,
              ),
            ],
          ),
          Visibility(
            visible: extras.isNotEmpty,
            child: Column(
              children: extras.map((extra) {
                return Container(
                  margin: const EdgeInsets.only(top: 4.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          '- ${extra.name}',
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                      ),
                      if (extra.price > 0)
                        Text(
                          extra.price.inEuro,
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          Visibility(
            visible: components.isNotEmpty,
            child: Column(
              children: components.map((component) {
                return Container(
                  margin: const EdgeInsets.only(top: 4.0),
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              '- ${component.dish?.name ?? ''}',
                              style: Theme.of(context).textTheme.labelLarge,
                            ),
                          ),
                        ],
                      ),
                      Visibility(
                        visible: component.component.extras.isNotEmpty,
                        child: Column(
                          children: component.component.extras.map((extra) {
                            return Container(
                              margin: const EdgeInsets.only(top: 4.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 16.0),
                                    child: Text(
                                      '- ${extra.name}',
                                      style: Theme.of(context).textTheme.labelLarge,
                                    ),
                                  ),
                                  if (extra.price > 0)
                                    Text(
                                      extra.price.inEuro,
                                      style: Theme.of(context).textTheme.labelLarge,
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
