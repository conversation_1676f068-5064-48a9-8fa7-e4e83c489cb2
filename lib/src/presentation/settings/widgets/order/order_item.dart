import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/constants/order_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/orders/order_detail_item_model.dart';
import 'package:mutualz/src/domain/models/orders/special_inner_component.dart';
import 'package:mutualz/src/domain/models/orders/special_order_component.dart';
import 'package:mutualz/src/domain/usecases/usecases.dart';
import 'package:mutualz/src/presentation/settings/widgets/order/order_position.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class OrderItem extends StatefulWidget {
  final OrderModel order;

  const OrderItem({required this.order, super.key});

  @override
  State<OrderItem> createState() => _OrderItemState();
}

class _OrderItemState extends State<OrderItem> with AutomaticKeepAliveClientMixin {
  bool _isExpanded = false;
  bool _isSendingInvoice = false;

  Future<void> _sendInvoice() async {
    if (_isSendingInvoice) return;

    setState(() {
      _isSendingInvoice = true;
    });

    try {
      final sendInvoiceUseCase = getIt<SendInvoiceUseCase>();
      await sendInvoiceUseCase(orderId: widget.order.id);

      if (mounted) {
        showSuccessDialog(context: context);
      }
    } catch (e) {
      if (mounted) {
        showErrorDialog(
          context: context,
          message: t.errors.unknown,
          title: t.dialogs.error.title,
        );
      }
    } finally {
      if (mounted)
        setState(() {
          _isSendingInvoice = false;
        });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: DottedBorder(
          color: MColors.onyxShadow,
          strokeWidth: 1.0,
          borderType: BorderType.RRect,
          radius: const Radius.circular(5.0),
          dashPattern: const [3, 3],
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            decoration: const BoxDecoration(
              color: MColors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Flexible(
                      child: Text(
                        widget.order.date,
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 1,
                        softWrap: false,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8.0),
                    Flexible(
                      child: Text(
                        '#${widget.order.orderNumber}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4.0),
                Text(
                  widget.order.restaurant?.name ?? '',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(height: 1.0),
                  maxLines: 2,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4.0),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        t.screens.orders.total,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Text(
                      widget.order.totalAmount.inEuro,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 4.0),
                Visibility(
                  visible: widget.order.memberDiscount > 0.0,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            t.screens.orders.memberDiscount,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        Text(
                          '- ${widget.order.memberDiscount.inEuro}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.order.couponDiscount > 0.0,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            t.screens.orders.couponDiscount,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        Text(
                          '- ${widget.order.couponDiscount.inEuro}',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.order.totalAmountAfterDiscount > 0.0 && widget.order.totalAmountAfterDiscount != widget.order.totalAmount,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            t.screens.orders.totalWithMutualz,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        Text(
                          widget.order.totalAmountAfterDiscount.inEuro,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 4.0),
                Text(
                  t.screens.orders.summary,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: _isExpanded ? MColors.onyxShadow : MColors.slateGrayMist,
                      ),
                ),
                const SizedBox(height: 4.0),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  reverseDuration: const Duration(milliseconds: 300),
                  child: _isExpanded
                      ? Column(children: [
                          ...widget.order.details.expand((d) => d.items).map((e) {
                            if (e.type == OrderType.dish && e.dish != null) {
                              return OrderPosition(
                                name: e.dish!.name,
                                quantity: e.quantity,
                                price: e.total.inEuro,
                                extras: e.dish!.extras
                                    .expand((group) => group.items.map((item) => ExtraModel(
                                          id: item.id,
                                          name: item.name,
                                          price: item.price,
                                          type: group.type,
                                        )))
                                    .toList(),
                              );
                            } else if (e.type == OrderType.special && e.special != null) {
                              return OrderPosition(
                                name: e.special!.name,
                                quantity: e.quantity,
                                price: e.total.inEuro,
                                components: e.special?.components != null
                                    ? e.special!.components.map((c) {
                                        final dish = c.dishes.first;
                                        return SpecialOrderComponent(
                                          componentId: c.id,
                                          component: SpecialInnerComponent(
                                              id: c.id,
                                              name: c.name,
                                              extras: c.dishes
                                                  .expand((d) => d.extras.expand((group) => group.items.map((item) => SpecialExtraModel(
                                                        id: item.id,
                                                        name: item.name,
                                                        price: item.price,
                                                        type: group.type,
                                                      ))))
                                                  .toList()),
                                          dish: dish,
                                        );
                                      }).toList()
                                    : [],
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          }).toList(),
                          Visibility(
                            visible: widget.order.taxAmount > 0.0,
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                const SizedBox(height: 4.0),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Text('MWST %', style: Theme.of(context).textTheme.bodyMedium),
                                        Text('${widget.order.taxRate.withoutTrailingZero} %',
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal)),
                                      ],
                                    ),
                                    Column(
                                      children: [
                                        Text('MWST', style: Theme.of(context).textTheme.bodyMedium),
                                        Text(widget.order.taxAmount.toString(),
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal)),
                                      ],
                                    ),
                                    Text('+', style: Theme.of(context).textTheme.bodyMedium),
                                    Column(
                                      children: [
                                        Text('Netto', style: Theme.of(context).textTheme.bodyMedium),
                                        Text(widget.order.netAmount.toString(),
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal)),
                                      ],
                                    ),
                                    Text('=', style: Theme.of(context).textTheme.bodyMedium),
                                    Column(
                                      children: [
                                        Text('Brutto', style: Theme.of(context).textTheme.bodyMedium),
                                        Text(widget.order.totalAmount.toString(),
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal)),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8.0),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: Container(
                                    height: 36,
                                    decoration: const BoxDecoration(
                                      color: MColors.scarletEmber,
                                      shape: BoxShape.circle,
                                    ),
                                    child: IconButton(
                                      onPressed: _isSendingInvoice ? null : _sendInvoice,
                                      icon: _isSendingInvoice
                                          ? const SizedBox(
                                              width: 16,
                                              height: 16,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                valueColor: AlwaysStoppedAnimation<Color>(MColors.white),
                                              ),
                                            )
                                          : const Icon(
                                              Icons.email,
                                              color: MColors.white,
                                              size: 20,
                                            ),
                                      tooltip: t.screens.orders.sendInvoice,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ])
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
