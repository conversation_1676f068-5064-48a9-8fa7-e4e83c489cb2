import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/schedule/schedule_model.dart';
import 'package:mutualz/src/presentation/loyalty/widgets/loyalty_widget.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/restaurant_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/order_nav_bundle.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/buttons/book_table_button.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/deals/restaurant_deals.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/decoration/restaurant_background.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/feed/restaurant_feed.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/menu/restaurant_menu.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/modals/booking/table_booking_modal.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/modals/info/info_modal.dart';
import 'package:mutualz/src/presentation/restaurant/widgets/modals/rating/rating_modal.dart';
import 'package:mutualz/src/presentation/scanner/models/scanner_nav_bundle.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/outlines.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/cards/info_indicator.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class RestaurantScreen extends StatefulWidget {
  final RestaurantNavBundle bundle;

  const RestaurantScreen({required this.bundle, super.key});

  @override
  State<RestaurantScreen> createState() => _RestaurantScreenState();
}

class _RestaurantScreenState extends State<RestaurantScreen>
    with AutomaticKeepAliveClientMixin {
  late final DiscoverBloc _discoverBloc;
  late final RestaurantBloc _restaurantBloc = getIt<RestaurantBloc>();
  late final RestaurantMenuBloc _restaurantMenuBloc =
      getIt<RestaurantMenuBloc>();
  late final RestaurantFeedBloc _restaurantFeedBloc =
      getIt<RestaurantFeedBloc>();
  late final RestaurantSpecialsBloc _restaurantSpecialsBloc =
      getIt<RestaurantSpecialsBloc>();
  late final CouponsBloc _couponsBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;
  final ScrollController _scrollController = ScrollController();
  bool isPersistentHeaderPinned = false;
  late final AuthBloc _authBloc;

  final GlobalKey _tutorialMainInfoKey = GlobalKey();
  final GlobalKey _tutorialLikeButtonKey = GlobalKey();
  final GlobalKey _tutorialDietPreferencesKey = GlobalKey();
  final GlobalKey _tutorialDietPreferences2Key = GlobalKey(); 
  final GlobalKey _tutorialDealsKey = GlobalKey();
  final GlobalKey _tutorialMenuKey = GlobalKey();
  
  bool tutorialAlreadyStarted = false;

  @override
  void initState() {
    super.initState();
    _discoverBloc = context.read<DiscoverBloc>();
    _authBloc = context.read<AuthBloc>();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    _scrollController.addListener(_scrollHandler);
    _restaurantBloc.add(RestaurantEvent.changeTab(widget.bundle.tab));
    _restaurantBloc
        .add(RestaurantEvent.setRestaurant(widget.bundle.restaurant));
    _couponsBloc = context.read<CouponsBloc>();
    _couponsBloc.add(CouponsEvent.fetchCoupons(widget.bundle.restaurant.id));
  }

  void _scrollHandler() {
    if (_scrollController.offset > 140 && !isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = true;
      });
    } else if (_scrollController.offset < 140 && isPersistentHeaderPinned) {
      setState(() {
        isPersistentHeaderPinned = false;
      });
    }
  }

  @override
  void dispose() {
    _couponsBloc.add(const CouponsEvent.clearCoupons());
    _scrollController.removeListener(_scrollHandler);
    _scrollController.dispose();
    _restaurantBloc.close();
    _restaurantMenuBloc.close();
    _restaurantFeedBloc.close();
    _restaurantSpecialsBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => _restaurantBloc),
        BlocProvider(
            create: (_) => _restaurantMenuBloc
              ..add(
                  RestaurantMenuEvent.fetchMenu(widget.bundle.restaurant.id))),
        BlocProvider(
            create: (_) => _restaurantFeedBloc
              ..add(RestaurantFeedEvent.fetchRestaurantPosts(
                  widget.bundle.restaurant.id))),
        BlocProvider(
            create: (_) => _restaurantSpecialsBloc
              ..add(RestaurantSpecialsEvent.fetchSpecials(
                  widget.bundle.restaurant.id))),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<CouponsBloc, CouponsState>(
            bloc: _couponsBloc,
            listener: (context, state) {
              if (state.couponsStatus == CouponsStatus.error &&
                  state.error != null) {
                showErrorDialog(
                  context: context,
                  message: state.error!,
                );
              }
            },
          ),
          BlocListener<RestaurantBloc, RestaurantState>(
            bloc: _restaurantBloc,
            listener: (context, state) {
              if (state.restaurant != null && !tutorialAlreadyStarted) {
                // We use this to avoid showing the tutorial multiple times when tab switches
                tutorialAlreadyStarted = true;
                Future.delayed(const Duration(seconds: 1), () {
                  Tutorials.showRestaurantTutorial(context, mainInfoKey: _tutorialMainInfoKey, likeButtonKey: _tutorialLikeButtonKey, dietPreferencesKey: _tutorialDietPreferencesKey, dietPreferences2Key: _tutorialDietPreferences2Key, dealsKey: _tutorialDealsKey, menuKey: _tutorialMenuKey);
                });
              }
            },
          ),
        ],
        child: Scaffold(
          backgroundColor: MColors.white,
          body: CustomScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            slivers: [
              SliverAppBar(
                floating: false,
                pinned: true,
                systemOverlayStyle: isPersistentHeaderPinned
                    ? SystemUiOverlayStyle.dark
                    : SystemUiOverlayStyle.light,
                expandedHeight: 200.0,
                collapsedHeight: 56.0,
                surfaceTintColor: MColors.white,
                backgroundColor: MColors.white,
                leading: Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () => context.pop(),
                    child: Container(
                      width: 30.0,
                      height: 30.0,
                      margin: const EdgeInsets.only(left: 16.0),
                      // child: Center(
                      //   child: SvgPicture.asset(
                      //     !isPersistentHeaderPinned ?
                      //       MAssets.iconBackLight :
                      //       MAssets.iconBackDark,
                      //     width: 30.0,
                      //     height: 30.0,
                      //   ),
                      // ),
                      child: Center(
                        child: SvgPicture.asset(
                          MAssets.iconBackDark,
                          width: 30.0,
                          height: 30.0,
                        ),
                      ),
                    ),
                  ),
                ),
                actions: [
                  Visibility(
                    visible: !isPersistentHeaderPinned,
                    child: BookTableButton(
                      isPersistentHeaderPinned: !isPersistentHeaderPinned,
                      onPressed: () {
                        showTableBookingDialog(
                          context: context,
                          restaurant: widget.bundle.restaurant,
                        );
                      },
                    ),
                  ),
                ],
                automaticallyImplyLeading: false,
                flexibleSpace: FlexibleSpaceBar(
                  collapseMode: CollapseMode.pin,
                  stretchModes: const [
                    StretchMode.zoomBackground,
                    StretchMode.fadeTitle,
                  ],
                  background: RestaurantBackground(
                    image: widget.bundle.restaurant.coverUrl,
                  ),
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverTitleHeaderDelegate(
                  child: AnimatedContainer(
                    duration: Duration.zero,
                    color: MColors.white,
                    padding: const EdgeInsets.only(
                        top: 24.0, left: 32.0, right: 32.0),
                    child: Builder(
                      builder: (context) => TutorialShowcase(
                        showcaseKey: _tutorialMainInfoKey,
                        onTap: () => Future.delayed(const Duration(milliseconds: 400), () => Tutorials.restaurantTutorialStep.value += 1),
                        container: Padding(
                          padding: EdgeInsets.only(right: context.width * 0.05, left: context.width * 0.5),
                          child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                 Padding(
                                  padding: const EdgeInsets.only(right: 16, top: 8),
                                  child: Text(
                                    t.tutorial.restaurant.rating,
                                    style: const TextStyle(fontSize: 14),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Transform(
                                  transform: Matrix4.identity()
                                  ..translate(-30.0, 0.0, 0.0)
                                  ..rotateZ(math.pi / 1.3),
                                  child: SvgPicture.asset(MAssets.tutorialArrow3, height: 20),
                                ),
                              ],
                            ),
                        ),
                        tooltipPosition: TooltipPosition.top,
                        targetPadding: const EdgeInsets.all(6),
                        targetBorderRadius: BorderRadius.circular(4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                showRatingDialog(
                                  context: context,
                                  rating: widget.bundle.restaurant.rating,
                                );
                              },
                              child: ValueListenableBuilder(valueListenable: Tutorials.restaurantTutorialStep,
                                builder: (context, step, child) {
                                  return MRating(
                                      padding: const EdgeInsets.symmetric(horizontal: 4),
                                      rating: widget.bundle.restaurant.averageRating,
                                      isTutorial: step == 0,
                                    );
                                },
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 2.0),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: RestaurantTitle(
                                      text: widget.bundle.restaurant.name,
                                      maxLines: 1,
                                    ),
                                  ),
                                  TutorialShowcase(
                                    showcaseKey: _tutorialLikeButtonKey,
                                    onTap: () => Tutorials.restaurantTutorialStep.value += 1,
                                    tooltipPosition: TooltipPosition.bottom,
                                    container: Container(
                                      padding: EdgeInsets.only(right: 20, left: context.width * 0.5, top: 20),
                                      alignment: Alignment.centerLeft,
                                      child: Transform.translate(
                                        offset: const Offset(0, -20),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Transform(
                                              transform: Matrix4.identity()..rotateZ(-math.pi / 6),
                                              alignment: Alignment.center,
                                              child: SvgPicture.asset(MAssets.tutorialArrow2, height: 60),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(right: 16, top: 16),
                                              child: Text(
                                                t.tutorial.restaurant.favorite,
                                                style: const TextStyle(fontSize: 14),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    targetShapeBorder: OutlineRoundedRectangleBorder(
                                      gap: 1,
                                      side: const BorderSide(
                                        color: MColors.goldenRod,
                                        width: 6,
                                      ),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: BlocBuilder<RestaurantBloc, RestaurantState>(
                                      bloc: _restaurantBloc,
                                      builder: (context, state) {
                                        return state.restaurant != null
                                            ? LikeButton(
                                                isLiked: state.restaurant!.isFavorite,
                                                onTap: () => state.restaurant!.isFavorite
                                                    ? _restaurantBloc.add(const RestaurantEvent.removeFromFavorite())
                                                    : _restaurantBloc.add(const RestaurantEvent.addToFavorite()),
                                              )
                                            : const SizedBox();
                                      },
                                    ),
                                  ),
                                  
                                ],
                              ),
                            ),
                            Wrap(
                              children: [
                                RestaurantFoodType(
                                  items: widget.bundle.restaurant.foodType,
                                  maxLines: 1,
                                ),
                                const SizedBox(width: 8.0),
                                RestaurantOpenTime(
                                  openingTime: widget.bundle.restaurant.schedule
                                      .getOpeningTime(),
                                  closingTime: widget.bundle.restaurant.schedule
                                      .getClosingTime(),
                                  hasLabel: true,
                                  applyContrastColor: true,
                                  onInfoTapped: () => showInfoDialog(
                                    context: context,
                                    schedule: widget.bundle.restaurant.schedule,
                                  ),
                                ),
                              ],
                            ),
                            Flexible(
                              child: RestaurantAddress(
                                street: widget.bundle.restaurant.address.street,
                                zipCode: widget.bundle.restaurant.address.zipCode,
                                city: widget.bundle.restaurant.address.city,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: TutorialShowcase(
                          showcaseKey: _tutorialDietPreferences2Key,
                          onTap: () => Future.delayed(const Duration(milliseconds: 300), () => Tutorials.restaurantTutorialStep.value += 1),
                          container: Container(
                            padding: EdgeInsets.only(left: context.width * 0.25, right: context.width * 0.2),
                            alignment: Alignment.centerLeft,
                            child: Transform.translate(
                                offset: const Offset(0, 0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 16, bottom: 8),
                                      child: Text(
                                        t.tutorial.restaurant.dietPreferencesConfirmed,
                                        style: const TextStyle(fontSize: 14),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Transform(
                                      transform: Matrix4.identity()
                                      ..scale(-1.0, 1.0)
                                      ..rotateZ(math.pi / 1.4),
                                      alignment: Alignment.center,
                                      child: SvgPicture.asset(MAssets.tutorialArrow1, height: 80),
                                    ),
                                  ],
                                ),
                              ),
                          ),
                          targetShapeBorder: const OutlineCircleBorder(
                            side: BorderSide(
                              color: MColors.goldenRod,
                              width: 6,
                            ),
                            gap: 3,
                          ),
                          child: TutorialShowcase(
                            showcaseKey: _tutorialDietPreferencesKey,
                            onTap: () =>
                                Future.delayed(const Duration(milliseconds: 300), () => Tutorials.restaurantTutorialStep.value += 1),
                            container: Container(
                              padding: EdgeInsets.only(right: context.width * 0.2, left: context.width * 0.27),
                              alignment: Alignment.centerLeft,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(right: 16, bottom: 8),
                                    child: Text(
                                      t.tutorial.restaurant.dietPreferences,
                                      style: const TextStyle(fontSize: 14),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  Transform(
                                    transform: Matrix4.identity()
                                      ..scale(-1.0, 1.0)
                                      ..rotateZ(math.pi / 1.4),
                                    alignment: Alignment.center,
                                    child: SvgPicture.asset(MAssets.tutorialArrow1, height: 80),
                                  ),
                                ],
                              ),
                            ),
                            targetShapeBorder: const OutlineCircleBorder(
                              side: BorderSide(
                                color: MColors.goldenRod,
                                width: 6,
                              ),
                              gap: 3,
                            ),
                            child: ValueListenableBuilder(
                              valueListenable: Tutorials.restaurantTutorialStep,
                              builder: (context, step, child) {
                                const diet = FilterDietPreferences.vegan;

                                if (step == 2) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                                    child: InfoIndicator(
                                      svg: diet.image,
                                      label: diet.label,
                                      isSelected: false,
                                    ),
                                  );
                                } else if (step == 3) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                                    child: InfoIndicator(
                                      svg: diet.image,
                                      label: diet.label,
                                      isSelected: false,
                                      bgColor: MColors.scarletEmber,
                                    ),
                                  );
                                }

                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                        ),
                      ),
                      ValueListenableBuilder(
                          valueListenable: Tutorials.restaurantTutorialStep,
                          builder: (context, step, child) {
                            if (step == 2 || step == 3) {
                              return const SizedBox.shrink();
                            }
                            // default
                            return Align(
                              alignment: Alignment.center,
                              child: InfoIndicatorsCard(
                                showBottomPanel: false,
                                withShadow: false,
                                dietPreferences: widget.bundle.restaurant.dietPreferences,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                  vertical: 4.0,
                                ),
                                height: 40.0,
                              ),
                            );
                          },
                        ),
                      const SizedBox(height: 12.0),
                      LoyaltyWidget(
                        restaurantId: widget.bundle.restaurant.id,
                        restaurantLoyalty: widget.bundle.restaurant.loyalty,
                        showSubscriptionDiscount: true,
                      ),
                      const SizedBox(height: 24.0),
                      BlocBuilder<AuthBloc, AuthState>(
                        bloc: _authBloc,
                        builder: (context, state) {
                          return MElevatedButton(
                            suffix: Padding(
                              padding: const EdgeInsets.only(left: 2.0),
                              child: SvgPicture.asset(MAssets.arrowRight),
                            ),
                            customBoxShadow: MStyles.restaurantButtonShadow,
                            label: t.screens.restaurant.orderPayButton,
                            onPressed: () async {
                              if (!widget.bundle.restaurant.isCurrentlyOpen) {
                                showErrorDialog(
                                  context: context,
                                  message: t.scanner.errorRestaurantClosed(name: widget.bundle.restaurant.name),
                                );
                                return;
                              }

                              if (state.user?.restaurantIdWithValidSession !=
                                  widget.bundle.restaurant.id) {
                                context.pushNamed(
                                  MRoutes.scanner,
                                  extra: ScannerNavBundle(
                                    isFirstOrder:
                                        state.user?.isFirstOrder ?? true,
                                    restaurants:
                                        _discoverBloc.state.restaurants,
                                    scanningFromRestaurantId:
                                        widget.bundle.restaurant.id,
                                  ),
                                );
                                return;
                              }

                              final result = await context.pushNamed(
                                MRoutes.orderFlow,
                                extra: OrderNavBundle(
                                  restaurantId: widget.bundle.restaurant.id,
                                  restaurantName: widget.bundle.restaurant.name,
                                  isRestaurantFavorite:
                                      widget.bundle.restaurant.isFavorite,
                                  stripeAccountId: widget.bundle.restaurant
                                          .owner?.stripeAccountId ??
                                      '',
                                  isFirstOrder:
                                      state.user?.isFirstOrder ?? true,
                                  dietPreferences:
                                      widget.bundle.restaurant.dietPreferences,
                                  orderAndPayOnly:
                                      widget.bundle.restaurant.orderAndPayOnly,
                                  selfService:
                                      widget.bundle.restaurant.selfService,
                                ),
                              );
                              if (result != null &&
                                  result is bool &&
                                  result == true) {
                                _userLoyaltyBloc.add(
                                    const UserLoyaltyEvent.fetchUserLoyalty());
                              }
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 12.0),
                    ],
                  ),
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverPanelHeaderDelegate(
                  child: Container(
                    color: MColors.white,
                    padding: const EdgeInsets.only(
                      left: 32.0,
                      right: 32.0,
                    ),
                    child: BlocBuilder<RestaurantBloc, RestaurantState>(
                      bloc: _restaurantBloc,
                      builder: (context, state) {
                        return RestaurantButtons(
                          tutorialDealsKey: _tutorialDealsKey,
                          tutorialMenuKey: _tutorialMenuKey,
                          active: state.activeTab,
                          onDealsTap: () => _restaurantBloc.add(
                              const RestaurantEvent.changeTab(
                                  RestaurantInfoTab.deals)),
                          onMenuTap: () => _restaurantBloc.add(
                              const RestaurantEvent.changeTab(
                                  RestaurantInfoTab.menu)),
                          onFeedTap: () => _restaurantBloc.add(
                              const RestaurantEvent.changeTab(
                                  RestaurantInfoTab.feed)),
                        );
                      },
                    ),
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: BlocBuilder<RestaurantBloc, RestaurantState>(
                    bloc: _restaurantBloc,
                    builder: (context, state) {
                      return switch (state.activeTab) {
                        RestaurantInfoTab.deals => RestaurantDeals(
                            restaurantId: widget.bundle.restaurant.id),
                        RestaurantInfoTab.menu => RestaurantMenu(
                            restaurantId: widget.bundle.restaurant.id),
                        RestaurantInfoTab.feed => RestaurantFeed(
                            restaurantId: widget.bundle.restaurant.id),
                      };
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class _SliverTitleHeaderDelegate extends SliverPersistentHeaderDelegate {
  _SliverTitleHeaderDelegate({required this.child});

  final Widget child;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 150.0;

  @override
  double get minExtent => 146.0;

  @override
  bool shouldRebuild(_SliverTitleHeaderDelegate oldDelegate) {
    return false;
  }
}

class _SliverPanelHeaderDelegate extends SliverPersistentHeaderDelegate {
  _SliverPanelHeaderDelegate({required this.child});

  final Widget child;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 60.0;

  @override
  double get minExtent => 60.0;

  @override
  bool shouldRebuild(_SliverPanelHeaderDelegate oldDelegate) {
    return false;
  }
}
