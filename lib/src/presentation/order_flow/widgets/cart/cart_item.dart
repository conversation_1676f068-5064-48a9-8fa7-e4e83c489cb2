import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class CartItem extends StatefulWidget {
  final String restaurantId;
  final DishModel dish;
  final int quantity;
  final bool hasDivider;
  final bool isExtended;
  final bool hasNotes;
  final VoidCallback? onRemove;
  final VoidCallback? onAddData;
  final VoidCallback? onEdit;
  final bool isActive;
  final double price;
  final bool isEditable;
  final String? notes;
  final Map<String, List<ExtraItemModel>> selectedExtras;

  const CartItem({
    required this.restaurantId,
    required this.dish,
    this.quantity = 1,
    this.hasDivider = true,
    this.isExtended = false,
    this.hasNotes = false,
    this.onRemove,
    this.onAddData,
    this.onEdit,
    this.isActive = true,
    this.price = 0.0,
    this.isEditable = true,
    this.notes,
    this.selectedExtras = const {},
    super.key
  });

  @override
  State<CartItem> createState() => _CartItemState();
}

class _CartItemState extends State<CartItem> {
  int _quantity = 1;

  @override
  void initState() {
    super.initState();
    _quantity = widget.quantity;
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0, left: 32.0, right: 32.0),
      width: displayWidth,
      constraints: const BoxConstraints(
        minHeight: 100.0,
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Visibility(
                visible: widget.dish.coverUrl != null && widget.isExtended,
                child: Container(
                  width: 90.0,
                  height: 90.0,
                  margin: const EdgeInsets.only(right: 10.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: MImage(
                      image: widget.dish.coverUrl ?? '',
                      width: 90.0,
                      height: 90.0,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// Todo: Implement MRating widget where rating will done on backend
                    // MRating(
                    //   rating: widget.dish.rating,
                    //   starSize: 12.0,
                    //   hasLabel: false,
                    // ),
                    Text(
                      widget.isActive ? widget.dish.name :
                      '${widget.quantity} x ${widget.dish.name}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        height: 1.1,
                        color: widget.isActive ? MColors.onyxShadow : MColors.slateGrayMist,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4.0),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          widget.dish.description,
                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: widget.isActive ?
                              MColors.onyxShadow : MColors.slateGrayMist.withOpacity(.6),
                          ),
                          maxLines: 5,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          widget.price.inEuro,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: widget.isActive ? MColors.onyxShadow : MColors.slateGrayMist,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: widget.isActive,
                child: Container(
                  padding: const EdgeInsets.only(left: 32.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 70.0,
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: widget.onRemove,
                              child: Container(
                                width: 32.0,
                                height: 32.0,
                                decoration: BoxDecoration(
                                  color: widget.onRemove != null ? MColors.white : MColors.white.withOpacity(.8),
                                  borderRadius: BorderRadius.circular(10.0),
                                  boxShadow: [
                                    BoxShadow(
                                      color: MColors.silverStone.withOpacity(.3),
                                      blurRadius: 4.0,
                                      offset: const Offset(0, 0),
                                    ),
                                  ],
                                ),
                                alignment: Alignment.center,
                                child: SvgPicture.asset(
                                  MAssets.iconClose,
                                  width: 24.0,
                                  height: 24.0,
                                  colorFilter: ColorFilter.mode(
                                    widget.onRemove != null ? MColors.onyxShadow : MColors.slateGrayMist,
                                    BlendMode.srcIn
                                  ),
                                ),
                              ),
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: widget.dish.extras.isEmpty ? widget.onAddData : widget.onEdit,
                              child: Container(
                                width: 32.0,
                                height: 32.0,
                                decoration: BoxDecoration(
                                  color: widget.hasNotes ?
                                  MColors.scarletEmber : MColors.white,
                                  borderRadius: BorderRadius.circular(10.0),
                                  boxShadow: [
                                    BoxShadow(
                                      color: MColors.silverStone.withOpacity(.3),
                                      blurRadius: 4.0,
                                      offset: const Offset(0, 0),
                                    ),
                                  ],
                                ),
                                alignment: Alignment.center,
                                child: SvgPicture.asset(
                                  MAssets.iconEdit,
                                  width: 24.0,
                                  height: 24.0,
                                ),
                                /// Uncomment to show pin icon
                                // widget.dish.extras.isEmpty ? SvgPicture.asset(
                                //   MAssets.iconPin,
                                //   width: 24.0,
                                //   height: 24.0,
                                //   colorFilter: ColorFilter.mode(
                                //     widget.hasNotes ? MColors.scarletEmber : MColors.onyxShadow,
                                //     BlendMode.srcIn
                                //   ),
                                // ) : SvgPicture.asset(
                                //   MAssets.iconEdit,
                                //   width: 24.0,
                                //   height: 24.0,
                                // ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: widget.dish.extras.isEmpty,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 20.0),
                          child: MNumberPickerButton(
                            width: 70.0,
                            value: _quantity,
                            onSubtract: () {
                              if (_quantity == 1) return;

                              if (_quantity > 1) {
                                setState(() {
                                  _quantity = _quantity - 1;
                                });
                              }

                              if (context.read<OrderFlowBloc>().checkIsDishInCart(widget.dish)) {
                                context.read<OrderFlowBloc>()
                                  .add(OrderFlowEvent.addDishToCart(
                                    restaurantId: widget.restaurantId,
                                    dish: widget.dish,
                                    quantity: _quantity,
                                    notes: widget.notes
                                ));
                              }
                            },
                            onAdd: () {
                              setState(() {
                                _quantity = _quantity + 1;
                              });
                              if (context.read<OrderFlowBloc>().checkIsDishInCart(widget.dish)) {
                                context.read<OrderFlowBloc>()
                                  .add(OrderFlowEvent.addDishToCart(
                                    restaurantId: widget.restaurantId,
                                    dish: widget.dish,
                                    quantity: _quantity,
                                    notes: widget.notes
                                ));
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16.0),
          Visibility(
            visible: widget.hasDivider,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: displayWidth * .7,
              ),
              child: const Divider(
                height: 0.3,
                color: MColors.onyxShadow,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
