import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special_extras.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/inputs/checkbox_expansion.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

enum AdditionsType { add, edit }

Future showSpecialAdditions({
  required BuildContext context,
  required SpecialDish dish,
  SpecialComponent? component,
  Map<String, List<ExtraItemModel>> selectedExtras = const {},
  AdditionsType type = AdditionsType.add,
}) =>
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => BasicDialogDecorator(
        borderRadius: 30.0,
        child: _SpecialAdditions(
          dish: dish,
          component: component,
          selectedExtras: selectedExtras,
          type: type,
        ),
      ),
    );

class _SpecialAdditions extends StatefulWidget {
  final SpecialDish dish;
  final SpecialComponent? component;
  final Map<String, List<ExtraItemModel>> selectedExtras;
  final AdditionsType type;

  const _SpecialAdditions({
    required this.dish,
    this.component,
    this.selectedExtras = const {},
    this.type = AdditionsType.add,
  });

  @override
  State<_SpecialAdditions> createState() => _SpecialAdditionsState();
}

class _SpecialAdditionsState extends State<_SpecialAdditions> {
  final Map<String, List<ExtraItemModel>> _groupedExtras = {};

  double get _extrasTotal {
    double total = 0.0;
    for (final items in _groupedExtras.values) {
      total += items.fold(0.0, (acc, item) => acc + item.price);
    }
    return total;
  }

  String get _predictablePrice => (_extrasTotal).inEuro;

  @override
  void initState() {
    super.initState();
    _groupedExtras.addAll(widget.selectedExtras);
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.dish.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          height: 1.1,
                        ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20.0),
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: displayWidth * .7,
          ),
          child: const Divider(
            height: 0.3,
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        Expanded(
            child: SingleChildScrollView(
          child: Column(
            children: [
              Column(
                children: widget.dish.extras.map((group) => _buildGroupedExtrasSection(group)).toList(),
              ),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: displayWidth * .7,
                ),
                child: const Divider(
                  height: 0.3,
                  color: MColors.onyxShadow,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      _predictablePrice,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ],
          ),
        )),
        const SizedBox(height: 20.0),
        MElevatedButton(
          prefix: widget.type == AdditionsType.add
              ? const Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Icon(
                    Icons.add,
                    color: MColors.white,
                  ),
                )
              : const Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Icon(
                    Icons.edit,
                    color: MColors.white,
                  ),
                ),
          label: widget.type == AdditionsType.add ? t.buttons.add : t.buttons.edit,
          onPressed: () => context.pop(
            ReturnedSpecialExtras(
              groupedData: _groupedExtras,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupedExtrasSection(ExtraGroupModel group) {
    return Visibility(
      visible: group.items.isNotEmpty,
      child: CheckboxExpansion(
        extraGroup: group,
        selectedItems: _groupedExtras[group.id]?.toSet() ?? {},
        onSelect: (selectedItems) {
          setState(() {
            _groupedExtras[group.id] = selectedItems.toList();
          });
        },
      ),
    );
  }
}
