import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special_extras.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

enum AdditionsType { add, edit }

Future showSpecialAdditions({
  required BuildContext context,
  required SpecialDish dish,
  SpecialComponent? component,
  List<SpecialExtraModel> selectedExtras = const [],
  AdditionsType type = AdditionsType.add,
}) => showDialog(
  context: context,
  barrierColor: Colors.transparent,
  builder: (context) => BasicDialogDecorator(
    borderRadius: 30.0,
    child: _SpecialAdditions(
      dish: dish,
      component: component,
      selectedExtras: selectedExtras,
      type: type,
    ),
  ),
);

class _SpecialAdditions extends StatefulWidget {
  final SpecialDish dish;
  final SpecialComponent? component;
  final List<SpecialExtraModel> selectedExtras;
  final AdditionsType type;

  const _SpecialAdditions({
    required this.dish,
    this.component,
    this.selectedExtras = const [],
    this.type = AdditionsType.add,
  });

  @override
  State<_SpecialAdditions> createState() => _SpecialAdditionsState();
}

class _SpecialAdditionsState extends State<_SpecialAdditions> {
  final Set<SpecialExtraModel> _extras = {};
  double get _extrasTotal => _extras.fold(0.0, (acc, extra) => acc + extra.price);
  String get _predictablePrice => (_extrasTotal).inEuro;

  @override
  void initState() {
    super.initState();
    _extras.addAll(widget.selectedExtras);
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.dish.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      height: 1.1,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20.0),
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: displayWidth * .7,
          ),
          child: const Divider(
            height: 0.3,
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Column(
                    children: [
                      Visibility(
                        visible: widget.dish.sauces.isNotEmpty,
                        child: SpecialExtrasCheckboxExpansion(
                          label: t.screens.restaurant.menu.sauces,
                          extras: widget.dish.sauces,
                          selectedExtras: _extras.where((e) => e.type == ExtraType.sauce).toSet(),
                          allowMultiplyChoice: widget.component?.allowMultipleSauces[widget.dish.id] ?? widget.dish.allowMultipleSauces,
                          onSelect: (extras) {
                            setState(() {
                              final candidatesToRemoving = _extras.where((e) => e.type == ExtraType.sauce).toSet();
                              _extras.removeAll(candidatesToRemoving);
                              _extras.addAll(extras);
                            });
                          },
                        ),
                      ),
                      Visibility(
                        visible: widget.dish.extras.isNotEmpty,
                        child: SpecialExtrasCheckboxExpansion(
                          label: t.screens.restaurant.menu.extras,
                          extras: widget.dish.getExtras,
                          selectedExtras: _extras.where((e) => e.type == ExtraType.extras).toSet(),
                          allowMultiplyChoice: widget.component?.allowMultipleExtras[widget.dish.id] ?? widget.dish.allowMultipleExtras,
                          onSelect: (extras) {
                            setState(() {
                              final candidatesToRemoving = _extras.where((e) => e.type == ExtraType.extras).toSet();
                              _extras.removeAll(candidatesToRemoving);
                              _extras.addAll(extras);
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxWidth: displayWidth * .7,
                    ),
                    child: const Divider(
                      height: 0.3,
                      color: MColors.onyxShadow,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          _predictablePrice,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
        ),
        const SizedBox(height: 20.0),
        MElevatedButton(
          prefix: widget.type == AdditionsType.add ? const Padding(
            padding: EdgeInsets.only(right: 8.0),
            child: Icon(
              Icons.add,
              color: MColors.white,
            ),
          ) : const Padding(
            padding: EdgeInsets.only(right: 8.0),
            child: Icon(
              Icons.edit,
              color: MColors.white,
            ),
          ),
          label: widget.type == AdditionsType.add ? t.buttons.add : t.buttons.edit,
          onPressed: () => context.pop(
            ReturnedSpecialExtras(
              data: _extras,
            ),
          ),
        ),
      ],
    );
  }
}
