import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_extras.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/inputs/checkbox_expansion.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

enum AdditionsType { add, edit }

Future showAdditions({
  required BuildContext context,
  required DishModel dish,
  Map<String, List<ExtraItemModel>> selectedGroupedExtras = const {},
  String? notes,
  AdditionsType type = AdditionsType.add,
}) =>
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => BasicDialogDecorator(
        borderRadius: 30.0,
        child: _Additions(
          dish: dish,
          notes: notes,
          selectedGroupedExtras: selectedGroupedExtras,
          type: type,
        ),
      ),
    );

class _Additions extends StatefulWidget {
  final DishModel dish;
  final String? notes;
  final Map<String, List<ExtraItemModel>> selectedGroupedExtras;
  final AdditionsType type;

  const _Additions({
    required this.dish,
    this.selectedGroupedExtras = const {},
    this.notes,
    this.type = AdditionsType.add,
  });

  @override
  State<_Additions> createState() => _AdditionsState();
}

class _AdditionsState extends State<_Additions> {
  final Map<String, Set<ExtraItemModel>> _groupedExtras = {};
      
  double get _totalExtrasPrice {
    double total = 0.0;
    _groupedExtras.values.forEach((items) {
      total += items.fold(0.0, (acc, item) => acc + item.price);
    });
    return total;
  }

  String get _predictablePrice => (_totalExtrasPrice + widget.dish.price).inEuro;
  final TextEditingController _specialRequirementsController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _specialRequirementsController.text = widget.notes ?? '';
    
    // Initialize grouped extras
    widget.selectedGroupedExtras.forEach((groupId, items) {
      _groupedExtras[groupId] = Set.from(items);
    });
  }

  @override
  void didUpdateWidget(covariant _Additions oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update grouped extras if they changed from outside
    if (oldWidget.selectedGroupedExtras != widget.selectedGroupedExtras) {
      _groupedExtras.clear();
      widget.selectedGroupedExtras.forEach((groupId, items) {
        _groupedExtras[groupId] = Set.from(items);
      });
    }
  }

  @override
  void dispose() {
    _specialRequirementsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// Todo: Implement MRating widget where rating will done on backend
                  // MRating(
                  //   rating: widget.dish.rating,
                  //   starSize: 12.0,
                  //   hasLabel: false,
                  // ),
                  Text(
                    widget.dish.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          height: 1.1,
                        ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        widget.dish.description,
                        style: Theme.of(context).textTheme.labelMedium,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        widget.dish.price.inEuro,
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20.0),
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: displayWidth * .7,
          ),
          child: const Divider(
            height: 0.3,
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        Expanded(
            child: SingleChildScrollView(
          child: Column(
            children: [
              Column(
                children: [
                  // Render grouped extras
                  if (widget.dish.hasExtras) 
                    ...widget.dish.extras.map((group) => 
                      _buildGroupedExtrasSection(group)
                    ).toList(),
                ],
              ),
              const SizedBox(height: 20.0),
              Text(
                t.screens.cart.dialogNotes.title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 20.0),
              MTextField(
                textEditingController: _specialRequirementsController,
                hintText: t.screens.cart.dialogNotes.hint,
                minLines: 2,
                maxLines: 2,
              ),
              const SizedBox(height: 20.0),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: displayWidth * .7,
                ),
                child: const Divider(
                  height: 0.3,
                  color: MColors.onyxShadow,
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.dish.price.inEuro,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: MColors.slateGrayMist,
                          ),
                    ),
                    Row(
                      children: [
                        const Icon(
                          Icons.add,
                          color: MColors.slateGrayMist,
                          size: 16.0,
                        ),
                        const SizedBox(width: 4.0),
                        Text(
                          _totalExtrasPrice.inEuro,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: MColors.slateGrayMist,
                                  ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: displayWidth * .7,
                ),
                child: const Divider(
                  height: 0.3,
                  color: MColors.onyxShadow,
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      _predictablePrice,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ],
          ),
        )),
        const SizedBox(height: 20.0),
        MElevatedButton(
          prefix: widget.type == AdditionsType.add
              ? const Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Icon(
                    Icons.add,
                    color: MColors.white,
                  ),
                )
              : const Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Icon(
                    Icons.edit,
                    color: MColors.white,
                  ),
                ),
          label:
              widget.type == AdditionsType.add ? t.buttons.add : t.buttons.edit,
          onPressed: () => context.pop(
            ReturnedExtras(
              groupedData: _groupedExtras.map((groupId, items) => 
                MapEntry(groupId, items.toList())
              ),
              notes: _specialRequirementsController.text.isNotEmpty
                  ? _specialRequirementsController.text
                  : '',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupedExtrasSection(ExtraGroupModel group) {
    return Visibility(
      visible: group.items.isNotEmpty,
      child: CheckboxExpansion(
        extraGroup: group,
        selectedItems: _groupedExtras[group.id] ?? {},
        onSelect: (selectedItems) {
          setState(() {
            _groupedExtras[group.id] = selectedItems;
          });
        },
      ),
    );
  }
}