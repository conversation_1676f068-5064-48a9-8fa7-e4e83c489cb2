import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_group_model.dart';
import 'package:mutualz/src/domain/models/extras/extra_item_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_extras.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/inputs/checkbox_expansion.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

enum AdditionsType { add, edit }

Future showAdditions({
  required BuildContext context,
  required DishModel dish,
  Map<String, List<ExtraItemModel>> selectedGroupedExtras = const {},
  String? notes,
  AdditionsType type = AdditionsType.add,
}) =>
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => BasicDialogDecorator(
        borderRadius: 30.0,
        child: _Additions(
          dish: dish,
          notes: notes,
          selectedGroupedExtras: selectedGroupedExtras,
          type: type,
        ),
      ),
    );

class _Additions extends StatefulWidget {
  final DishModel dish;
  final String? notes;
  final Map<String, List<ExtraItemModel>> selectedGroupedExtras;
  final AdditionsType type;

  const _Additions({
    required this.dish,
    this.selectedGroupedExtras = const {},
    this.notes,
    this.type = AdditionsType.add,
  });

  @override
  State<_Additions> createState() => _AdditionsState();
}

class _AdditionsState extends State<_Additions> {
  final Map<String, Set<ExtraItemModel>> _groupedExtras = {};
      
  double get _totalExtrasPrice {
    double total = 0.0;
    _groupedExtras.values.forEach((items) {
      total += items.fold(0.0, (acc, item) => acc + item.price);
    });
    return total;
  }

  String get _predictablePrice => (_totalExtrasPrice + widget.dish.price).inEuro;
  final TextEditingController _specialRequirementsController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _specialRequirementsController.text = widget.notes ?? '';
    
    // Initialize grouped extras
    widget.selectedGroupedExtras.forEach((groupId, items) {
      _groupedExtras[groupId] = Set.from(items);
    });
  }

  @override
  void didUpdateWidget(covariant _Additions oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update grouped extras if they changed from outside
    if (oldWidget.selectedGroupedExtras != widget.selectedGroupedExtras) {
      _groupedExtras.clear();
      widget.selectedGroupedExtras.forEach((groupId, items) {
        _groupedExtras[groupId] = Set.from(items);
      });
    }
  }

  @override
  void dispose() {
    _specialRequirementsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(32.0, 32.0, 32.0, 20.0),
          decoration: const BoxDecoration(
            color: MColors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30.0),
              topRight: Radius.circular(30.0),
            ),
          ),
          child: Column(
            children: [
              Text(
                widget.dish.name,
                style: Theme.of(context).textTheme.displaySmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8.0),
              RichText(
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodyMedium,
                  children: [
                    TextSpan(
                      text: 'Price:',
                    ),
                    const TextSpan(text: ' '),
                    TextSpan(
                      text: _predictablePrice,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: MColors.scarletEmber,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20.0),
        Expanded(
            child: SingleChildScrollView(
          child: Column(
            children: [
              Column(
                children: [
                  // Render grouped extras
                  if (widget.dish.hasExtras) 
                    ...widget.dish.extras.map((group) => 
                      _buildGroupedExtrasSection(group)
                    ).toList(),
                ],
              ),
              const SizedBox(height: 20.0),
              Text(
                t.screens.cart.dialogNotes.title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 20.0),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: TextField(
                  controller: _specialRequirementsController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: t.screens.cart.dialogNotes.hint,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: BorderSide(
                        color: MColors.scarletEmber,
                        width: 2.0,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: BorderSide(
                        color: MColors.silverMist,
                        width: 1.0,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 40.0),
            ],
          ),
        )),
        Container(
          width: double.infinity,
          margin: const EdgeInsets.fromLTRB(32.0, 0.0, 32.0, 40.0),
          child: FloatingActionButton.extended(
            backgroundColor: MColors.scarletEmber,
            elevation: 0.0,
            icon: Icon(
              widget.type == AdditionsType.add
                  ? Icons.add
                  : Icons.edit,
              color: MColors.white,
            ),
            label: Text(
              widget.type == AdditionsType.add ? t.buttons.add : t.buttons.edit,
              style: const TextStyle(color: MColors.white),
            ),
            onPressed: () => context.pop(
              ReturnedExtras(
                groupedData: _groupedExtras.map((groupId, items) => 
                  MapEntry(groupId, items.toList())
                ),
                notes: _specialRequirementsController.text.isNotEmpty
                    ? _specialRequirementsController.text
                    : '',
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupedExtrasSection(ExtraGroupModel group) {
    return Visibility(
      visible: group.items.isNotEmpty,
      child: CheckboxExpansion(
        extraGroup: group,
        selectedItems: _groupedExtras[group.id] ?? {},
        onSelect: (selectedItems) {
          setState(() {
            _groupedExtras[group.id] = selectedItems;
          });
        },
      ),
    );
  }
}