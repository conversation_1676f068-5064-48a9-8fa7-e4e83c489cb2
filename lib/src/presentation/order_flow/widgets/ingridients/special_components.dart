import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special_extras.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/ingridients/special_additions.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/basic_dialog_decorator.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

enum SpecialComponentsType { add, edit }

Future showSpecialComponents({
  required BuildContext context,
  required SpecialModel special,
  SpecialComponentsType type = SpecialComponentsType.add,
  String? notes,
  Set<SpecialComponent> selectedComponents = const {},
}) => showDialog(
  context: context,
  barrierColor: Colors.transparent,
  builder: (context) => BasicDialogDecorator(
    borderRadius: 30.0,
    child: _SpecialComponents(
      special: special,
      notes: notes,
      type: type,
      selectedComponents: selectedComponents,
    ),
  ),
);

class _SpecialComponents extends StatefulWidget {
  final SpecialModel special;
  final String? notes;
  final SpecialComponentsType type;
  final Set<SpecialComponent> selectedComponents;

  const _SpecialComponents({
    required this.special,
    this.notes,
    this.type = SpecialComponentsType.add,
    this.selectedComponents = const {},
  });

  @override
  State<_SpecialComponents> createState() => _SpecialComponentsState();
}

class _SpecialComponentsState extends State<_SpecialComponents> {
  final TextEditingController _specialRequirementsController = TextEditingController();
  final Set<SpecialComponent> _selectedComponents = {};
  final Set<String> _groupNames = {};

  @override
  void initState() {
    super.initState();
    _specialRequirementsController.text = widget.notes ?? '';
    widget.selectedComponents.isNotEmpty
      ? _selectedComponents.addAll(widget.selectedComponents) : {};
    _groupNames.addAll(widget.special.components.map((c) => c.name));
  }

  @override
  void dispose() {
    _specialRequirementsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          t.specials.single,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 90.0,
              height: 90.0,
              margin: const EdgeInsets.only(right: 10.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: MImage(
                  image: widget.special.coverUrl ?? '',
                  width: 90.0,
                  height: 90.0,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// Todo: Implement MRating widget where rating will done on backend
                  // const MRating(
                  //   rating: 3.2,
                  //   starSize: 12.0,
                  //   hasLabel: false,
                  // ),
                  Text(
                    widget.special.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      height: 1.1,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        widget.special.description,
                        style: Theme.of(context).textTheme.labelMedium,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4.0),
                      Text(
                        widget.special.price.inEuro,
                        style: Theme.of(context).textTheme.bodyMedium,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20.0),
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: displayWidth * .7,
          ),
          child: const Divider(
            height: 0.3,
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: Column(
                children: widget.special.components.map((component) {
                  return SpecialCheckboxExpansion(
                    label: component.name,
                    dishes: component.dishes,
                    component: component,
                    selectedDishes: _selectedComponents.singleWhereOrNull((c) =>
                      c.name == component.name)?.dishes.toSet() ?? {},
                    onSelect: (selectedDish) {
                      setState(() {
                        _selectedComponents.removeWhere((c) => c.name == component.name);
                        if (selectedDish.isEmpty) return;
                        _selectedComponents.add(SpecialComponent(
                          name: component.name,
                          dishes: selectedDish.toList(),
                          id: component.id,
                        ));
                      });
                    },
                  );
                }).toList(),
              ),
            )
          )
        ),
        const SizedBox(height: 16.0),
        Text(
          t.screens.cart.dialogNotes.title,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 20.0),
        MTextField(
          textEditingController: _specialRequirementsController,
          hintText: t.screens.cart.dialogNotes.hint,
          minLines: 2,
          maxLines: 2,
        ),
        const SizedBox(height: 20.0),
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: displayWidth * .7,
          ),
          child: const Divider(
            height: 0.3,
            color: MColors.onyxShadow,
          ),
        ),
        const SizedBox(height: 20.0),
        MElevatedButton(
          isDisabled: _groupNames.length != _selectedComponents.length,
          prefix: Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: widget.type == SpecialComponentsType.add ? const Icon(
              Icons.add,
              color: MColors.white,
            ) : const Icon(
              Icons.edit,
              color: MColors.white,
            ),
          ),
          label: widget.type == SpecialComponentsType.add ? t.buttons.add : t.buttons.edit,
          onPressed: () {
            context.pop(ReturnedSpecial(
              components: _selectedComponents,
              notes: _specialRequirementsController.text,
            ));
          }
        ),
      ],
    );
  }
}


