import 'package:flutter/material.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';

class InvoiceItem extends StatelessWidget {
  final String dishName;
  final String price;
  final String quantity;
  final List<ExtraModel> extras;
  final List<SpecialExtraModel> specialExtras;

  const InvoiceItem({
    this.dishName = '',
    this.price = '',
    this.quantity = '',
    this.extras = const [],
    this.specialExtras = const [],
    super.key
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.only(bottom: 12.0),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: MColors.slateGrayMist,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        children: [
          _InvoiceCell(
            label: '$quantity x $dishName',
            price: price,
          ),
          Visibility(
            visible: extras.isNotEmpty,
            child: Padding(
              padding: const EdgeInsets.only(left: 26.0),
              child: Column(
                children: extras.map((extra) => _InvoiceCell(
                  label: extra.name,
                  price: extra.price > 0 ? extra.price.inEuro : '',
                )).toList(),
              ),
            ),
          ),
          Visibility(
            visible: specialExtras.isNotEmpty,
            child: Padding(
              padding: const EdgeInsets.only(left: 26.0),
              child: Column(
                children: specialExtras.map((extra) => _InvoiceCell(
                  label: extra.name,
                  price: extra.price > 0 ? extra.price.inEuro : '',
                )).toList(),
              ),
            ),
          ),
        ],
      )
    );
  }
}

class _InvoiceCell extends StatelessWidget {
  final String label;
  final String price;

  const _InvoiceCell({
    this.label = '',
    this.price = '',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: MColors.slateGrayMist,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 12.0),
        ConstrainedBox(
          constraints: const BoxConstraints(
            minWidth: 60.0,
          ),
          child: Text(
            price,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: MColors.slateGrayMist,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}

