import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/shared/cart_indicator.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class OrderButton extends StatelessWidget {
  final int count;
  final VoidCallback onTap;
  final GlobalKey cartButtonKey;

  const OrderButton({
    required this.onTap,
    required this.cartButtonKey,
    this.count = 0,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Container(
      width: displayWidth * 0.7,
      margin: const EdgeInsets.only(right: 16.0),
      alignment: Alignment.centerRight,
      child: IntrinsicWidth(
        child: Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: onTap,
              child: SizedBox(
                width: 37.0,
                height: 34.0,
                child: TutorialShowcase(
                  showcaseKey: cartButtonKey,
                  tooltipPosition: TooltipPosition.bottom,
                  targetPadding: const EdgeInsets.only(top: 10, right: 10, left: 6, bottom: 10),
                  container: Container(
                    padding: EdgeInsets.only(right: 100, left: context.width * 0.2),
                    alignment: Alignment.centerLeft,
                    child: Transform.translate(
                      offset: const Offset(0, -20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Transform(
                            transform: Matrix4.identity()..rotateZ(-pi / 15),
                            alignment: Alignment.center,
                            child: SvgPicture.asset(MAssets.tutorialArrow2, height: 100),
                          ),
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsets.only(top: 24),
                              child: Text(
                                t.tutorial.orders.cart,
                                style: const TextStyle(fontSize: 14),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  child: CartIndicator(
                    count: count,
                  ),
                ),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: onTap,
              child: Container(
                margin: const EdgeInsets.only(left: 16.0),
                width: 30.0,
                height: 30.0,
                decoration: BoxDecoration(
                  color: MColors.iceGray,
                  borderRadius: BorderRadius.circular(5.0),
                ),
                alignment: Alignment.center,
                child: SvgPicture.asset(
                  MAssets.arrowRight,
                  width: 14.0,
                  height: 20.0,
                  colorFilter: const ColorFilter.mode(
                    MColors.onyxShadow,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
