import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/order_flow/order_flow_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_extras.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/ingridients/additions.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class OrderMenuItem extends StatefulWidget {
  final String restaurantId;
  final DishModel dish;
  final bool hasDivider;
  final bool isExtended;

  const OrderMenuItem({
    required this.restaurantId,
    required this.dish,
    this.hasDivider = true,
    this.isExtended = false,
    super.key
  });

  @override
  State<OrderMenuItem> createState() => _OrderMenuItemState();
}

class _OrderMenuItemState extends State<OrderMenuItem> {
  int _quantity = 1;

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    final uniqueDish = widget.dish.hasExtras;

    return Container(
      margin: const EdgeInsets.only(bottom: 16.0, left: 32.0, right: 32.0),
      width: displayWidth,
      constraints: const BoxConstraints(
        minHeight: 100.0,
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Visibility(
                visible: widget.dish.coverUrl != null && widget.isExtended,
                child: Container(
                  width: 90.0,
                  height: 90.0,
                  margin: const EdgeInsets.only(right: 10.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: MImage(
                      image: widget.dish.coverUrl ?? '',
                      width: 90.0,
                      height: 90.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// Todo: Implement MRating widget where rating will done on backend
                    // MRating(
                    //   rating: widget.dish.rating,
                    //   starSize: 12.0,
                    //   hasLabel: false,
                    // ),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.dish.name,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  height: 1.1,
                                ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(
                          width: 3.5,
                        ),
                        ...List.generate(
                            widget.dish.dietPreferences.length,
                            (index) => Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 1.5),
                                  padding: const EdgeInsets.all(2),
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: MColors.softCotton),
                                  child: SvgPicture.asset(widget
                                      .dish
                                      .dietPreferences[index]
                                      .dietPreferences
                                      .image),
                                )),
                      ],
                    ),
                    const SizedBox(height: 4.0),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          widget.dish.description,
                          style: Theme.of(context).textTheme.labelMedium,
                          maxLines: 20,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          widget.dish.price.inEuro,
                          style: Theme.of(context).textTheme.titleMedium,
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.only(left: 32.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (widget.dish.extras.isEmpty)
                      BlocBuilder<OrderFlowBloc, OrderFlowState>(
                        bloc: context.read<OrderFlowBloc>(),
                        builder: (BuildContext context, OrderFlowState state) {
                          if (!state.isCartEmpty &&
                              state.isDishInCart(widget.dish)) {
                            _quantity = state.getDishQuantity(widget.dish);
                          }

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 20.0),
                            child: MNumberPickerButton(
                              width: 70.0,
                              value: _quantity,
                              onSubtract: () {
                                if (_quantity == 1) return;

                                if (context
                                    .read<OrderFlowBloc>()
                                    .checkIsDishInCart(widget.dish)) {
                                  context.read<OrderFlowBloc>().add(
                                      OrderFlowEvent.addDishToCart(
                                          restaurantId: widget.restaurantId,
                                          dish: widget.dish,
                                          quantity: _quantity - 1));
                                }

                                if (_quantity > 1) {
                                  setState(() {
                                    _quantity = _quantity - 1;
                                  });
                                }
                              },
                              onAdd: () {
                                if (context
                                    .read<OrderFlowBloc>()
                                    .checkIsDishInCart(widget.dish)) {
                                  context.read<OrderFlowBloc>().add(
                                      OrderFlowEvent.addDishToCart(
                                          restaurantId: widget.restaurantId,
                                          dish: widget.dish,
                                          quantity: _quantity + 1));
                                }

                                setState(() {
                                  _quantity = _quantity + 1;
                                });
                              },
                            ),
                          );
                        },
                      ),
                    MElevatedButton(
                        label: t.buttons.add,
                        labelStyle:
                            Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: MColors.white,
                                ),
                        padding: EdgeInsets.zero,
                        prefix: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Icon(
                            uniqueDish
                                ? Icons.add
                                : !context
                                        .read<OrderFlowBloc>()
                                        .checkIsDishInCart(widget.dish)
                                    ? Icons.add
                                    : Icons.check,
                            color: MColors.white,
                            size: 18.0,
                          ),
                        ),
                        width: 70.0,
                        height: 30.0,
                        onPressed: () {
                          if (!uniqueDish) {
                            bool isInCart = context
                                .read<OrderFlowBloc>()
                                .checkIsDishInCart(widget.dish);

                            if (isInCart) {
                              context.read<OrderFlowBloc>().add(
                                  OrderFlowEvent.removeDishFromCart(
                                      restaurantId: widget.restaurantId,
                                      dish: widget.dish));
                              return;
                            }
                          }

                          if (!widget.dish.isSpecial &&
                              widget.dish.hasExtras) {
                            showAdditions(context: context, dish: widget.dish)
                                .then((response) {
                              if (response is ReturnedExtras) {
                                context.read<OrderFlowBloc>().add(
                                    OrderFlowEvent.addDishToCart(
                                        restaurantId: widget.restaurantId,
                                        dish: widget.dish,
                                        quantity: _quantity,
                                        groupedExtras: response.groupedData,
                                        notes: response.notes,
                                        withUnique: uniqueDish));
                                try {
                                  Platform.isAndroid
                                      ? HapticFeedback.vibrate()
                                      : HapticFeedback.heavyImpact();
                                } catch (e, s) {
                                  log('HapticFeedback.heavyImpact() error: $e, $s');
                                }
                              }
                            });
                          }

                          if (!widget.dish.hasExtras) {
                            context.read<OrderFlowBloc>().add(
                                OrderFlowEvent.addDishToCart(
                                    restaurantId: widget.restaurantId,
                                    dish: widget.dish, quantity: _quantity));
                            try {
                              Platform.isAndroid
                                  ? HapticFeedback.vibrate()
                                  : HapticFeedback.heavyImpact();
                            } catch (e, s) {
                              log('HapticFeedback.heavyImpact() error: $e, $s');
                            }
                          }
                        }),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16.0),
          Visibility(
            visible: widget.hasDivider,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: displayWidth * .7,
              ),
              child: const Divider(
                height: 0.3,
                color: MColors.onyxShadow,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
