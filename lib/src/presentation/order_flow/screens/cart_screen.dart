import 'dart:math';

import 'package:collection/collection.dart';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_extras.dart';
import 'package:mutualz/src/presentation/order_flow/models/ingridients/returned_special.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/cart/cart_item.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/cart/special_cart_item.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/ingridients/additions.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/ingridients/special_components.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/tutorial/utils/tutorial_utils.dart';
import 'package:mutualz/src/presentation/tutorial/widgets/tutorial_showcase.dart';
import 'package:mutualz/src/presentation/widgets/conditional_wrapper.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class CartScreen extends StatefulWidget {
  final String restaurantId;
  final String stripeAccountId;
  final bool isFirstOrder;
  final bool orderAndPayOnly;

  final VoidCallback? onReset;

  const CartScreen(
      {required this.restaurantId,
      required this.stripeAccountId,
      required this.isFirstOrder,
      required this.orderAndPayOnly,
      this.onReset,
      super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  late final OrderFlowBloc _orderFlowBloc;
  late final CouponsBloc _couponsBloc;
  final GlobalKey _tutorialItemKey = GlobalKey();
  final GlobalKey _paymentButtonsKey = GlobalKey();
  late final DiscoverBloc _discoverBloc;

  @override
  void initState() {
    super.initState();
    _orderFlowBloc = context.read<OrderFlowBloc>();
    _couponsBloc = context.read<CouponsBloc>();
    _discoverBloc = context.read<DiscoverBloc>();
    final selectedCouponId = _orderFlowBloc.state.preOrder?.couponId;
    if (selectedCouponId != null) {
      _couponsBloc.add(CouponsEvent.selectCoupon(selectedCouponId));
    }

    TutorialUtils.scheduleAfterBuild(this, () {
      final state = _orderFlowBloc.state;

      // Tutorial for cart items
      if (state.specialCart.isNotEmpty || state.cart.isNotEmpty) {
        Tutorials.showOrderCartInsideTutorial(context, itemKey: _tutorialItemKey);
      }
      
      // Temporary disabled
      // Tutorial for payment buttons - second order and not self-service
      // if (!widget.isFirstOrder && !widget.orderAndPayOnly) {
      //   debugPrint('Payment buttons tutorial should be shown: Second order (!isFirstOrder=${!widget.isFirstOrder}) and not self-service (!orderAndPayOnly=${!widget.orderAndPayOnly})');
      //   Tutorials.showOrderCartOrderPaymentTutorial(context, buttonsContainerKey: _paymentButtonsKey);
      // }
    });
  }

  /// This method is used to determine if the tutorial wrapper should be shown for the item at the given index.
  /// Logic: if there is only one active item, the tutorial wrapper should be shown for the first item.
  /// If there are two or more active items, the tutorial wrapper should be shown for the second item
  bool _shouldShowTutorialWrapper(int index, int totalActiveItems, bool isSpecialCart) {
    if (totalActiveItems == 1) {
      return index == 0;
    }
    
    if (isSpecialCart) {
      return index == 1;
    } else {
      return index == (1 - _orderFlowBloc.state.specialCart.length);
    }
  }

  RestaurantModel? _getRestaurant() {
    final restaurants = _discoverBloc.state.restaurants;
    return restaurants.firstWhereOrNull((restaurant) => restaurant.id == widget.restaurantId);
  }

  bool _isRestaurantOpen() {
    final restaurant = _getRestaurant();
    return restaurant?.isCurrentlyOpen ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return BlocConsumer<OrderFlowBloc, OrderFlowState>(
      bloc: _orderFlowBloc,
      listener: (context, state) {
        if (state.orderStatus == OrderProceedStatus.success) {
          showSuccessDialog(
            context: context,
            title: t.dialogs.success.title,
          ).whenComplete(() => widget.onReset?.call());
        }

        if (state.orderStatus == OrderProceedStatus.error &&
            state.orderError != null) {
          showErrorDialog(
            context: context,
            message: state.orderError!,
          );
        }
      },
      builder: (context, state) {
        final cart = state.cart;
        final order = state.order;
        final specialCart = state.specialCart;
        final orderSpecialCart = state.orderSpecialCart;

        return Container(
          width: displayWidth,
          padding: const EdgeInsets.only(top: 30.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: cart.isEmpty &&
                        order.isEmpty &&
                        specialCart.isEmpty &&
                        orderSpecialCart.isEmpty
                    ? Center(
                        child: Text(t.screens.cart.empty,
                            style: Theme.of(context).textTheme.titleMedium),
                      )
                    : SingleChildScrollView(
                        child: Column(
                          children: [
                            ...specialCart
                                .asMap()
                                .entries
                                .map((entry) {
                                  final e = entry.value;
                                  final totalActiveItems = specialCart.length + cart.length;
                                  return ConditionalWrapper(
                                    condition: _shouldShowTutorialWrapper(entry.key, totalActiveItems, true),
                                    wrapper: (child) => _tutorialWrapper(child),
                                    child: SpecialCartItem(
                                      restaurantId: widget.restaurantId,
                                      key: UniqueKey(),
                                      special: e.special,
                                      quantity: e.quantity,
                                      hasDivider: true,
                                      hasNotes: e.notes?.isNotEmpty ?? false,
                                      price: e.totalWithExtras,
                                      notes: e.notes,
                                      components: e.components,
                                      onEdit: () async {
                                        final response =
                                            await showSpecialComponents(
                                          context: context,
                                          special: e.special,
                                          selectedComponents:
                                              e.components.toSet(),
                                          notes: e.notes,
                                          type: SpecialComponentsType.edit,
                                        );
                                        if (response is ReturnedSpecial) {
                                          context.read<OrderFlowBloc>().add(
                                                OrderFlowEvent.addSpecialToCart(
                                                    restaurantId: widget.restaurantId,
                                                    special: e.special,
                                                    quantity: e.quantity,
                                                    selectedComponents:
                                                        response.components,
                                                    notes: response.notes),
                                              );
                                        }
                                      },
                                      onRemove: () {
                                        confirmDialog(
                                            context: context,
                                            title: t.screens.cart
                                                .dialogSpecialRemove.title,
                                            message: t
                                                .screens
                                                .cart
                                                .dialogSpecialRemove
                                                .description,
                                            confirmLabel: t
                                                .screens
                                                .cart
                                                .dialogSpecialRemove
                                                .buttonConfirm,
                                            cancelLabel: t
                                                .screens
                                                .cart
                                                .dialogSpecialRemove
                                                .buttonCancel,
                                            onConfirm: () {
                                              _orderFlowBloc.add(OrderFlowEvent
                                                  .removeSpecialFromCart(
                                                      widget.restaurantId,
                                                      e.special));
                                              context.pop();
                                            },
                                            onCancel: () => context.pop());
                                      },
                                    ),
                                  );
                                })
                                .toList(),
                            ...cart
                                .asMap()
                                .entries
                                .map((entry) {
                                  final e = entry.value;
                                  final totalActiveItems = specialCart.length + cart.length;
                                  return ConditionalWrapper(
                                    condition: _shouldShowTutorialWrapper(entry.key, totalActiveItems, false),
                                    wrapper: (child) => _tutorialWrapper(child),
                                    child: CartItem(
                                    restaurantId: widget.restaurantId,
                                    key: UniqueKey(),
                                    dish: e.dish,
                                    quantity: e.quantity,
                                    hasDivider: true,
                                    hasNotes: e.notes?.isNotEmpty ?? false,
                                    price: e.totalWithExtras,
                                    notes: e.notes,
                                    onAddData: () async {
                                      final result =
                                          await showSpecialRequirementsDialog(
                                        context: context,
                                        initialNotes: e.notes,
                                      );

                                      if (result != null) {
                                        _orderFlowBloc.add(OrderFlowEvent
                                          .addSpecialRequirementsToDish(
                                            widget.restaurantId,
                                            e.dish,
                                            result
                                          ),
                                        );
                                      }
                                    },
                                    onEdit: () {

                                      showAdditions(
                                        context: context,
                                        dish: e.dish,
                                        selectedGroupedExtras: e.selectedExtras,
                                        notes: e.notes,
                                        type: AdditionsType.edit,
                                      ).then((response) {
                                        if (response is ReturnedExtras) {
                                          _orderFlowBloc.add(
                                            OrderFlowEvent.addDishToCart(
                                              restaurantId: widget.restaurantId,
                                              dish: e.dish,
                                              quantity: e.quantity,
                                              groupedExtras: response.groupedData,
                                              notes: response.notes,
                                              uuid: e.uuid,
                                            ),
                                          );
                                        }
                                      });
                                    },
                                    onRemove: () {
                                      confirmDialog(
                                          context: context,
                                          title:
                                              t.screens.cart.dialogRemove.title,
                                          message: t.screens.cart.dialogRemove
                                              .description,
                                          confirmLabel: t.screens.cart
                                              .dialogRemove.buttonConfirm,
                                          cancelLabel: t.screens.cart
                                              .dialogRemove.buttonCancel,
                                          onConfirm: () {
                                            _orderFlowBloc.add(OrderFlowEvent
                                                .removeDishFromCart(
                                                    restaurantId:
                                                        widget.restaurantId,
                                                    dish: e.dish,
                                                    uuid: e.uuid));
                                            context.pop();
                                          },
                                          onCancel: () => context.pop());
                                    },
                                  ),
                                );
                                })
                                .toList(),
                            ...orderSpecialCart
                                .asMap()
                                .entries
                                .map((e) => SpecialCartItem(
                                      restaurantId: widget.restaurantId,
                                      special: e.value.special,
                                      quantity: e.value.quantity,
                                      hasDivider:
                                          e.key != orderSpecialCart.length - 1,
                                      hasNotes:
                                          e.value.notes?.isNotEmpty ?? false,
                                      price: e.value.special.price,
                                      isActive: false,
                                    ))
                                .toList(),
                            ...order
                                .asMap()
                                .entries
                                .map((e) => CartItem(
                                      restaurantId: widget.restaurantId,
                                      dish: e.value.dish,
                                      quantity: e.value.quantity,
                                      hasDivider: e.key != order.length - 1,
                                      hasNotes:
                                          e.value.notes?.isNotEmpty ?? false,
                                      price: e.value.totalWithExtras,
                                      isActive: false,
                                    ))
                                .toList(),
                          ],
                        ),
                      ),
              ),
              Container(
                width: displayWidth,
                constraints: const BoxConstraints(
                  minHeight: 174.0,
                ),
                padding: const EdgeInsets.symmetric(
                    horizontal: 32.0, vertical: 40.0),
                decoration: BoxDecoration(
                  color: MColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(MSizes.cardBorderRadius),
                    topRight: Radius.circular(MSizes.cardBorderRadius),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: MColors.black.withOpacity(.1),
                      blurRadius: 40.0,
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
                child: ConditionalWrapper(
                  condition: !widget.isFirstOrder && !widget.orderAndPayOnly,
                  wrapper: (child) => _paymentButtonsTutorialWrapper(child),
                  child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          t.screens.cart.total,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: MColors.slateGrayMist,
                                  ),
                        ),
                        Text(state.total,
                            style: Theme.of(context).textTheme.titleMedium),
                      ],
                    ),
                    const SizedBox(height: 20.0),
                    Visibility(
                      visible:
                          !widget.isFirstOrder && !widget.orderAndPayOnly,
                      child: MElevatedButton(
                          isLoading: state.orderStatus ==
                              OrderProceedStatus.loading,
                          onPressed: () {
                            if (!_isRestaurantOpen()) {
                              final restaurant = _getRestaurant();
                              showErrorDialog(
                                context: context,
                                message: t.scanner.errorRestaurantClosed(name: restaurant?.name ?? ''),
                              );
                              return;
                            }
                            _orderFlowBloc.add(
                                OrderFlowEvent.changeOrder(
                                    widget.restaurantId));
                          },
                          isDisabled: cart.isEmpty && specialCart.isEmpty,
                          label: t.buttons.order,
                        ),
                    ),
                    Visibility(
                        visible:
                            !widget.isFirstOrder && !widget.orderAndPayOnly,
                        child: const SizedBox(height: 20.0)),
                    MElevatedButton(
                      onPressed: () {
                        if (!_isRestaurantOpen()) {
                          final restaurant = _getRestaurant();
                          showErrorDialog(
                            context: context,
                            message: t.scanner.errorRestaurantClosed(name: restaurant?.name ?? ''),
                          );
                          return;
                        }

                        showConfirmOrderDialog(
                          context: context,
                          onConfirm: () {
                            _orderFlowBloc.add(
                              OrderFlowEvent.precalculateOrder(
                                restaurantId: widget.restaurantId,
                                couponId:
                                    _couponsBloc.state.selectedCouponId,
                                dishes: [...cart, ...order],
                                specials: [
                                  ...specialCart,
                                  ...orderSpecialCart
                                ],
                              ),
                            );
                            _orderFlowBloc.add(
                              const OrderFlowEvent.changeOrderFlowType(
                                  OrderFlowType.invoice),
                            );
                          },
                        );

                        /// Commented code is for the future functionality
                        // showConfirmOrderDialog(
                        //   context: context,
                        //   onConfirm: () {
                        //     _orderFlowBloc.add(
                        //       OrderFlowEvent.precalculateOrder(
                        //         restaurantId: widget.restaurantId,
                        //         couponId: _couponsBloc.state.selectedCouponId,
                        //         dishes: [...cart, ...order],
                        //         specials: [...specialCart, ...orderSpecialCart],
                        //       ),
                        //     );
                        //     showShareRestaurantDialog(
                        //       context: context,
                        //       onShare: () async {
                        //         /// Share restaurant functionality
                        //         _orderFlowBloc.add(
                        //           const OrderFlowEvent.changeOrderFlowType(OrderFlowType.invoice),
                        //         );
                        //       },
                        //       onNotShare: () {
                        //         _orderFlowBloc.add(
                        //           const OrderFlowEvent.changeOrderFlowType(OrderFlowType.invoice),
                        //         );
                        //       },
                        //     );
                        //   },
                        // );
                      },
                      isDisabled: cart.isEmpty &&
                          order.isEmpty &&
                          specialCart.isEmpty &&
                          orderSpecialCart.isEmpty,
                      label: !widget.isFirstOrder &&
                              state.cart.isEmpty &&
                              state.specialCart.isEmpty &&
                              state.preOrder != null
                          ? t.buttons.pay
                          : t.buttons.orderAndPay,
                      labelStyle: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(
                            color: MColors.onyxShadow,
                          ),
                      background: MColors.silverMist,
                    ),
                  ],
                ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _tutorialWrapper(Widget child) {
    return TutorialShowcase(
      showcaseKey: _tutorialItemKey,
      tooltipPosition: TooltipPosition.bottom,
      targetPadding: const EdgeInsets.only(top: 30),
      targetBorderRadius: BorderRadius.circular(16),
      container: Container(
        padding: EdgeInsets.only(right: context.width * 0.1, left: context.width * 0.2,),
        child: Transform.translate(
          offset: const Offset(20, -40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Transform(
                transform: Matrix4.identity()..scale(-1.0, 1.0)
                                        ..rotateZ(-pi / 2.5),
                alignment: Alignment.center,
                child: SvgPicture.asset(MAssets.tutorialArrow4, height: 80),
              ),
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.only(right: 80),
                  child: Text(
                    t.tutorial.orders.cartInside,
                    style: const TextStyle(fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      child: child,
    );
  }

  Widget _paymentButtonsTutorialWrapper(Widget child) {
    return TutorialShowcase(
      showcaseKey: _paymentButtonsKey,
      tooltipPosition: TooltipPosition.top,
      targetPadding: const EdgeInsets.all(16),
      targetBorderRadius: BorderRadius.circular(16),
      container: Container(
        padding: EdgeInsets.only(right: context.width * 0.1, left: context.width * 0.1),
        child: Transform.translate(
          offset: const Offset(0, 40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  "Please insert the design here, where the two buttons are below each other.",
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
              Transform(
                transform: Matrix4.identity()..rotateZ(pi / 2),
                alignment: Alignment.center,
                child: SvgPicture.asset(MAssets.tutorialArrow4, height: 60),
              ),
            ],
          ),
        ),
      ),
      child: child,
    );
  }

}
