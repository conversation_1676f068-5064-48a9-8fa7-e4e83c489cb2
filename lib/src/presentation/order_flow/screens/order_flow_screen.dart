import 'dart:math';

import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/loyalty/loyalty_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/order_nav_bundle.dart';
import 'package:mutualz/src/presentation/order_flow/screens/cart_screen.dart';
import 'package:mutualz/src/presentation/order_flow/screens/invoice_screen.dart';
import 'package:mutualz/src/presentation/order_flow/screens/order_screen.dart';
import 'package:mutualz/src/presentation/order_flow/screens/payment_screen.dart';
import 'package:mutualz/src/presentation/order_flow/screens/progress_reward_screen.dart';
import 'package:mutualz/src/presentation/order_flow/screens/review_screen.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/order/order_button.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/shared/cart_indicator.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/tutorial/tutorials.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

import 'coupon_selection_screen.dart';

class OrderFlowScreen extends StatefulWidget {
  final OrderNavBundle bundle;

  const OrderFlowScreen({required this.bundle, super.key});

  @override
  State<OrderFlowScreen> createState() => _OrderFlowScreenState();
}

class _OrderFlowScreenState extends State<OrderFlowScreen> {
  late final OrderFlowBloc _orderFlowBloc = getIt<OrderFlowBloc>();
  late final CouponsBloc _couponsBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;
  final GlobalKey _cartButtonKey = GlobalKey();
  ConfettiController _controller = ConfettiController();

  @override
  void initState() {
    super.initState();
    _couponsBloc = context.read<CouponsBloc>();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    _requestsInit();
    _controller = ConfettiController(duration: const Duration(seconds: 10));
    _controller.addListener(() {
      setState(() {});
    });
  }

  void _requestsInit() {
    _orderFlowBloc.add(OrderFlowEvent.fetchMenus(widget.bundle.restaurantId));
    _orderFlowBloc
        .add(OrderFlowEvent.fetchSpecials(widget.bundle.restaurantId));
    _orderFlowBloc.add(OrderFlowEvent.getPreOrder(widget.bundle.restaurantId));
    _orderFlowBloc.add(OrderFlowEvent.changeFavorite(
        widget.bundle.restaurantId, widget.bundle.isRestaurantFavorite));
    _orderFlowBloc.add(OrderFlowEvent.changeDietPreferences(
        widget.bundle.dietPreferences.toSet()));
    _couponsBloc.add(CouponsEvent.fetchCoupons(widget.bundle.restaurantId));
  }

  Future _showNumberTableDialog(BuildContext context) async {
    final result = await showNumberTableDialog(
      context: context,
      goBack: () => context.pop(),
    );
    if (result != null && result is int) {
      _orderFlowBloc.add(OrderFlowEvent.changeTableNumber(result.toString()));

      if (context.mounted) {
        Tutorials.showOrderCartTutorial(context, cartButtonKey: _cartButtonKey);
      }
    }
  }

  Path drawStar(Size size) {
    // Method to convert degree to radians
    double degToRad(double deg) => deg * (pi / 180.0);

    const numberOfPoints = 5;
    final halfWidth = size.width / 2;
    final externalRadius = halfWidth;
    final internalRadius = halfWidth / 2.5;
    final degreesPerStep = degToRad(360 / numberOfPoints);
    final halfDegreesPerStep = degreesPerStep / 2;
    final path = Path();
    final fullAngle = degToRad(360);
    path.moveTo(size.width, halfWidth);

    for (double step = 0; step < fullAngle; step += degreesPerStep) {
      path.lineTo(halfWidth + externalRadius * cos(step),
          halfWidth + externalRadius * sin(step));
      path.lineTo(halfWidth + internalRadius * cos(step + halfDegreesPerStep),
          halfWidth + internalRadius * sin(step + halfDegreesPerStep));
    }
    path.close();
    return path;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _orderFlowBloc,
      child: MultiBlocListener(
        listeners: [
          BlocListener<OrderFlowBloc, OrderFlowState>(
            bloc: _orderFlowBloc,
            listener: (context, state) {
              if (state.preOrderStatus == PreOrderStatus.notExists) {
                _showNumberTableDialog(context);
              }
            },
            listenWhen: (previous, current) =>
                previous.preOrderStatus != current.preOrderStatus &&
                previous.preOrderStatus == PreOrderStatus.idle &&
                current.preOrderStatus != PreOrderStatus.idle,
          ),
        ],
        child: BlocBuilder<OrderFlowBloc, OrderFlowState>(
          bloc: _orderFlowBloc,
          builder: (context, state) {
            final isBackgroundGray = state.type == OrderFlowType.invoice ||
                state.type == OrderFlowType.coupon ||
                state.type == OrderFlowType.payment;
            return PopScope(
              canPop:
                  state.preOrder == null && state.type != OrderFlowType.review,
              child: Stack(
                children: [
                  Scaffold(
                    backgroundColor:
                        isBackgroundGray ? MColors.softCotton : MColors.white,
                    appBar: ComplexAppBar(
                      backgroundColor:
                          isBackgroundGray ? MColors.softCotton : MColors.white,
                      backButtonColor: isBackgroundGray
                          ? MColors.white.withOpacity(.7)
                          : MColors.iceGray.withOpacity(.7),
                      withShadow: isBackgroundGray,
                      hasLeading: (state.preOrder == null &&
                              state.type != OrderFlowType.review) ||
                          state.preOrder != null &&
                              state.type == OrderFlowType.cart,
                      onBack: switch (state.type) {
                        OrderFlowType.order => () {
                            // _couponsBloc.add(const CouponsEvent.clearCoupons());
                            context.pop();
                          },
                        OrderFlowType.cart => () => _orderFlowBloc.add(
                            const OrderFlowEvent.changeOrderFlowType(
                                OrderFlowType.order)),
                        OrderFlowType.invoice => () => _orderFlowBloc.add(
                            const OrderFlowEvent.changeOrderFlowType(
                                OrderFlowType.cart)),
                        OrderFlowType.coupon => () => _orderFlowBloc.add(
                            const OrderFlowEvent.changeOrderFlowType(
                                OrderFlowType.invoice)),
                        OrderFlowType.payment => () => _orderFlowBloc.add(
                            const OrderFlowEvent.changeOrderFlowType(
                                OrderFlowType.coupon)),
                        OrderFlowType.reward => () {
                            _couponsBloc.add(const CouponsEvent.clearCoupons());
                            context.pop(true);
                          },
                        OrderFlowType.review => () {
                            _couponsBloc.add(const CouponsEvent.clearCoupons());
                            context.pop(true);
                          }
                      },
                      title: state.type == OrderFlowType.reward ||
                              state.type == OrderFlowType.review
                          ? Text(
                              widget.bundle.restaurantName,
                              style: Theme.of(context).textTheme.headlineSmall,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            )
                          : null,
                      actions: [
                        switch (state.type) {
                          OrderFlowType.order => OrderButton(
                              cartButtonKey: _cartButtonKey,
                              count: state.cartItemsCount,
                              onTap: () => _orderFlowBloc.add(
                                  const OrderFlowEvent.changeOrderFlowType(
                                      OrderFlowType.cart)),
                            ),
                          OrderFlowType.cart => Center(
                              child: CartIndicator(
                                count: state.cartItemsCount,
                                padding: const EdgeInsets.only(right: 32.0),
                              ),
                            ),
                          OrderFlowType.invoice => const SizedBox(),
                          OrderFlowType.coupon => Center(
                                child: GestureDetector(
                              onTap: () {
                                _couponsBloc.add(CouponsEvent.fetchCoupons(
                                    widget.bundle.restaurantId));
                              },
                              child: BlocBuilder<UserLoyaltyBloc,
                                      UserLoyaltyState>(
                                  bloc: _userLoyaltyBloc,
                                  builder: (BuildContext context,
                                          UserLoyaltyState state) =>
                                      CouponIndicator(
                                          count: state.loyalty
                                                  ?.getBalanceByRestaurant(
                                                      widget.bundle
                                                          .restaurantId) ??
                                              0)),
                            )),
                          OrderFlowType.payment => const SizedBox(),
                          OrderFlowType.reward => Visibility(
                              visible: state.favorite != null,
                              child: Center(
                                child: LikeButton(
                                  margin: const EdgeInsets.only(right: 24.0),
                                  isLiked: state.favorite!.$2,
                                  onTap: () => _orderFlowBloc.add(
                                    OrderFlowEvent.changeFavorite(
                                      widget.bundle.restaurantId,
                                      !state.favorite!.$2,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          OrderFlowType.review => Center(
                              child: LikeButton(
                                margin: const EdgeInsets.only(right: 24.0),
                                isLiked: state.favorite!.$2,
                                onTap: () => _orderFlowBloc.add(
                                  OrderFlowEvent.changeFavorite(
                                    widget.bundle.restaurantId,
                                    !state.favorite!.$2,
                                  ),
                                ),
                              ),
                            ),
                        },
                      ],
                    ),
                    body: SafeArea(
                      bottom: false,
                      child: switch (state.type) {
                        OrderFlowType.order => OrderScreen(
                            restaurantId: widget.bundle.restaurantId,
                          ),
                        OrderFlowType.cart => CartScreen(
                            orderAndPayOnly: widget.bundle.orderAndPayOnly,
                            restaurantId: widget.bundle.restaurantId,
                            stripeAccountId: widget.bundle.stripeAccountId,
                            isFirstOrder: widget.bundle.isFirstOrder,
                            onReset: () {
                              _orderFlowBloc.add(
                                  OrderFlowEvent.storeOrder(StoredOrderModel(
                                restaurantId: widget.bundle.restaurantId,
                                restaurantName: widget.bundle.restaurantName,
                                isRestaurantFavorite: state.favorite != null
                                    ? state.favorite!.$2
                                    : false,
                                stripeAccountId: widget.bundle.stripeAccountId,
                                dietPreferences: state.dietPreferences,
                                isFirstOrder: widget.bundle.isFirstOrder,
                              )));
                              _orderFlowBloc
                                  .add(const OrderFlowEvent.resetOrderFlow());
                              _requestsInit();
                            },
                          ),
                        OrderFlowType.invoice => const InvoiceScreen(),
                        OrderFlowType.coupon => CouponSelectionScreen(
                          preOrder: state.precalculatedOrderRequest,
                            restaurantId: widget.bundle.restaurantId,
                          ),
                        OrderFlowType.payment => PaymentScreen(
                            selfService: widget.bundle.selfService,
                            restaurantId: widget.bundle.restaurantId,
                            stripeAccountId: widget.bundle.stripeAccountId,
                            onPaymentSuccess: () {
                              _orderFlowBloc
                                  .add(const OrderFlowEvent.clearStoredOrder());
                              _orderFlowBloc
                                  .add(const OrderFlowEvent.clearStoredCart());
                            },
                          ),
                        OrderFlowType.reward => ProgressRewardScreen(
                            selfService: widget.bundle.selfService,
                            restaurantId: widget.bundle.restaurantId,
                            onConfettiStart: () => _controller.play(),
                            onConfettiStop: () => _controller.stop(),
                          ),
                        OrderFlowType.review => ReviewScreen(
                            restaurantId: widget.bundle.restaurantId,
                          ),
                      },
                    ),
                  ),
                  Align(
                    alignment: Alignment.topCenter,
                    child: ConfettiWidget(
                      confettiController: _controller,
                      blastDirectionality: BlastDirectionality.explosive,
                      blastDirection: pi / 2,
                      minBlastForce: 2,
                      gravity: 0.1,
                      // set a lower min blast force
                      emissionFrequency: 0.05,
                      numberOfParticles: 10,
                      shouldLoop: true,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
