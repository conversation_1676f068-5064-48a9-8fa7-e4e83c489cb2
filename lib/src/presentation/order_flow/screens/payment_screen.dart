import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/models/constants/loyalty_status.dart';
import 'package:mutualz/src/domain/models/constants/extra_type.dart';
import 'package:mutualz/src/domain/models/extras/extra_model.dart';
import 'package:mutualz/src/domain/models/extras/special_extra_model.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/order_flow/widgets/invoice/invoice_item.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class PaymentScreen extends StatefulWidget {
  final String restaurantId;
  final String stripeAccountId;
  final VoidCallback? onPaymentSuccess;
  final bool selfService;

  const PaymentScreen({
    required this.restaurantId,
    required this.stripeAccountId,
    this.onPaymentSuccess,
    required this.selfService,
    super.key
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  late final OrderFlowBloc _orderFlowBloc;
  late final CouponsBloc _couponsBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;
  late final AuthBloc _authBloc;

  @override
  void initState() {
    super.initState();
    _orderFlowBloc = context.read<OrderFlowBloc>();
    _couponsBloc = context.read<CouponsBloc>();
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    _authBloc = context.read<AuthBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;

    return BlocConsumer<OrderFlowBloc, OrderFlowState>(
      bloc: _orderFlowBloc,
      listener: (context, state) {
        if (state.paymentStatus == PaymentProceedStatus.error &&
            state.paymentError != null) {
          showErrorDialog(
            context: context,
            message: state.paymentError!,
          );
        }

        if (state.paymentStatus == PaymentProceedStatus.success) {
          if (state.successfulOrderId != null) {
            _orderFlowBloc.add(
                OrderFlowEvent.earnPoints(orderId: state.successfulOrderId!));
          }

          /// We need to keep queue of orders in sync with the loyalty points
          /// 1. We need to earn points for the order
          /// 2. We need to update the loyalty points
          Future.delayed(const Duration(milliseconds: 500), () {
            _couponsBloc.add(const CouponsEvent.clearCoupon());
            _couponsBloc.add(CouponsEvent.fetchCoupons(widget.restaurantId));
            _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(OrderFlowType.reward));

            if (_authBloc.state.user?.isFirstOrder == true) {
              _authBloc.add(const AuthEvent.fetchUser());
            }

            widget.onPaymentSuccess?.call();
          });

          /// Show success dialog after payment
          // showSuccessPaymentDialog(
          //   context: context,
          //   onAction: () {
          //     _couponsBloc.add(const CouponsEvent.clearCoupon());
          //     _couponsBloc.add(CouponsEvent.fetchCoupons(widget.restaurantId));
          //     _orderFlowBloc.add(const OrderFlowEvent.changeOrderFlowType(OrderFlowType.reward));
          //     if (_authBloc.state.user?.isFirstOrder == true) {
          //       _authBloc.add(const AuthEvent.fetchUser());
          //     }
          //   }
          // );
        }
      },
      builder: (context, state) {
        final mixed = [...state.cart, ...state.order];
        final specialMixed = [...state.specialCart, ...state.orderSpecialCart];
        final precalculatedOrder = state.precalculatedOrder;

        return Container(
          width: displayWidth,
          padding: const EdgeInsets.only(top: 30.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: 32.0,
                    right: 32.0,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          t.screens.invoice.title.toUpperCase(),
                          style: Theme.of(context).textTheme.headlineLarge,
                        ),
                        const SizedBox(height: 32.0),
                        Visibility(
                          visible: specialMixed.isNotEmpty,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                t.specials.title,
                                style:
                                    Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 20.0),
                              Column(
                                children: specialMixed.map((special) {
                                  return InvoiceItem(
                                    key: UniqueKey(),
                                    dishName: special.special.name,
                                    quantity: special.quantity.toString(),
                                    price: special.totalInEuro,
                                    specialExtras: special.components
                                        .expand((c) =>
                                            c.dishes.expand((d) => d.extras))
                                        .cast<SpecialExtraModel>()
                                        .toList(),
                                  );
                                }).toList(),
                              ),
                              const SizedBox(height: 20.0),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: mixed.isNotEmpty,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                t.screens.invoice.dishes,
                                style:
                                    Theme.of(context).textTheme.headlineSmall,
                              ),
                              const SizedBox(height: 20.0),
                              Column(
                                children: mixed.map((item) {
                                  return InvoiceItem(
                                    key: UniqueKey(),
                                    dishName: item.dish.name,
                                    quantity: item.quantity.toString(),
                                    price: item.totalInEuro,
                                    extras: item.extrasForDisplay,
                                  );
                                }).toList(),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                width: displayWidth,
                constraints: const BoxConstraints(
                  minHeight: 174.0,
                ),
                padding: const EdgeInsets.symmetric(
                    horizontal: 32.0, vertical: 40.0),
                decoration: BoxDecoration(
                  color: MColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(MSizes.cardBorderRadius),
                    topRight: Radius.circular(MSizes.cardBorderRadius),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: MColors.black.withOpacity(.1),
                      blurRadius: 40.0,
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    switch (state.precalculatedOrderStatus) {
                      PrecalculatedOrderStatus.idle => const SizedBox(),
                      PrecalculatedOrderStatus.loading =>
                        const CircularProgressIndicator(
                          color: MColors.onyxShadow,
                        ),
                      PrecalculatedOrderStatus.success => state
                                  .precalculatedOrder !=
                              null
                          ? Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      t.paymentInfo.total,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            color: MColors.onyxShadow,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                    Text(precalculatedOrder!.totalAmount.inEuro,
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium),
                                  ],
                                ),
                                const SizedBox(height: 4.0),
                                Visibility(
                                  visible: precalculatedOrder.memberStatus !=
                                      LoyaltyStatus.test,
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 4.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        GradientText(
                                            '${precalculatedOrder.memberStatus.label} ${t.paymentInfo.discount}'
                                                .toUpperCase(),
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  color: MColors.slateGrayMist,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                            gradientType: GradientType.linear,
                                            gradientDirection:
                                                GradientDirection.ttb,
                                            stops: const [0.0, 1.0],
                                            colors: switch (precalculatedOrder
                                                .memberStatus) {
                                              LoyaltyStatus.test => [
                                                  MColors.azureSky,
                                                  MColors.azureSky,
                                                ],
                                              LoyaltyStatus.member => [
                                                  MColors.scarletEmber,
                                                  MColors.scarletEmber,
                                                ],
                                              LoyaltyStatus.silver => [
                                                  MColors.charcoalBlack,
                                                  MColors.charcoalBlack
                                                ],
                                              LoyaltyStatus.gold => [
                                                  MColors.goldenRod,
                                                  MColors.pumpkin
                                                ],
                                            }),
                                        GradientText(
                                            '- ${precalculatedOrder.memberDiscount.inEuro}',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium,
                                            gradientType: GradientType.linear,
                                            stops: const [0.0, 1.0],
                                            colors: switch (precalculatedOrder
                                                .memberStatus) {
                                              LoyaltyStatus.test => [
                                                  MColors.azureSky,
                                                  MColors.azureSky,
                                                ],
                                              LoyaltyStatus.member => [
                                                  MColors.scarletEmber,
                                                  MColors.scarletEmber,
                                                ],
                                              LoyaltyStatus.silver => [
                                                  MColors.charcoalBlack,
                                                  MColors.charcoalBlack
                                                ],
                                              LoyaltyStatus.gold => [
                                                  MColors.goldenRod,
                                                  MColors.pumpkin
                                                ],
                                            }),
                                      ],
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible: precalculatedOrder.coupon != null,
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 4.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          // width: displayWidth * .5,
                                          child: Text(
                                            '${precalculatedOrder.coupon?.name}',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  color: MColors.scarletEmber,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        Text(
                                            '- ${precalculatedOrder.couponDiscount.inEuro}',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  color: MColors.scarletEmber,
                                                )),
                                      ],
                                    ),
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      t.paymentInfo.totalWithMutualz,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            color: MColors.onyxShadow,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                    Text(
                                        precalculatedOrder
                                            .totalAmountAfterDiscount.inEuro,
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium),
                                  ],
                                ),
                              ],
                            )
                          : const SizedBox(),
                      PrecalculatedOrderStatus.error => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              t.screens.cart.total,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color: MColors.slateGrayMist,
                                  ),
                            ),
                            Text(state.total,
                                style: Theme.of(context).textTheme.titleMedium),
                          ],
                        ),
                    },
                    const SizedBox(height: 20.0),
                    Column(
                      children: [
                        MElevatedButton(
                          isLoading: state.paymentStatus ==
                              PaymentProceedStatus.loading,
                          onPressed: () {
                            if (state.paymentStatus ==
                                PaymentProceedStatus.loading) return;
                            _orderFlowBloc.add(OrderFlowEvent.pay(
                              widget.restaurantId,
                              widget.stripeAccountId,
                              _couponsBloc.state.selectedCouponId,
                            ));
                          },
                          isDisabled: mixed.isEmpty && specialMixed.isEmpty,
                          label: t.screens.invoice.proceedToPayment,
                          suffix: Padding(
                            padding: const EdgeInsets.only(left: 16.0),
                            child: SvgPicture.asset(
                              MAssets.arrowRight,
                              colorFilter: const ColorFilter.mode(
                                MColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
