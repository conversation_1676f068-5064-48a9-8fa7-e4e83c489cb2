import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/presentation/auth/widgets/dialogs/otp_code_dialog.dart';
import 'package:mutualz/src/presentation/auth/widgets/registration/registration_privacy.dart';
import 'package:mutualz/src/presentation/auth/widgets/social/social_type_selector.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class RegistrationForm extends StatefulWidget {
  const RegistrationForm({super.key});

  @override
  State<RegistrationForm> createState() => _RegistrationFormState();
}

class _RegistrationFormState extends State<RegistrationForm> {
  final RegistrationBloc _registrationBloc = getIt<RegistrationBloc>();

  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _birthDateController = TextEditingController();

  final FocusNode _userNameFN = FocusNode();
  final FocusNode _emailFN = FocusNode();
  final FocusNode _passwordFN = FocusNode();
  final FocusNode _confirmPasswordFN = FocusNode();
  final FocusNode _birthDateFN = FocusNode();


  @override
  void dispose() {
    _userNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _birthDateController.dispose();
    _userNameFN.dispose();
    _emailFN.dispose();
    _passwordFN.dispose();
    _confirmPasswordFN.dispose();
    _birthDateFN.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final t = Translations.of(context);

    return BlocProvider(
      create: (_) => _registrationBloc,
      child: BlocConsumer(
        bloc: _registrationBloc,
        listener: (BuildContext context, RegistrationState state) {
          if (state.status == RegistrationStatus.success && state.isRegistered ||
              state.socialStatus == RegistrationWithSocialStatus.success && state.isRegistered) {
            context.goNamed(MRoutes.discover);
          }

          if (state.socialStatus == RegistrationWithSocialStatus.success &&
              !state.isRegistered) {
            context.goNamed(
              MRoutes.socialOnboarding,
              extra: state.socialAuthOnBoardingRequest
            );
          }

          if (state.socialStatus == RegistrationWithSocialStatus.error &&
          state.socialRegistrationError != null) {
            showErrorDialog(
              context: context,
              message: state.socialRegistrationError ?? '',
            );
          }

          if (state.status == RegistrationStatus.success) {
            _userNameController.clear();
            _emailController.clear();
            _passwordController.clear();
            _confirmPasswordController.clear();
            _birthDateController.clear();

            showOTPCodeDialog(
              context: context,
              email: state.email,
              onComplete: () {
                context.read<AuthBloc>().add(const AuthEvent.setIsEmailVerified(true));
                context.goNamed(MRoutes.onboarding);
              }
            );
          }

          if (state.status == RegistrationStatus.error && state.registrationError != null) {
            showErrorDialog(
              context: context,
              message: state.registrationError ?? '',
            );
          }

          if (_userNameFN.hasFocus && state.userNameValidationStatus ==
              ValidationStatus.valid && state.userNameError == null) {
            _userNameFN.unfocus();
            _emailFN.requestFocus();
          }

          if (_emailFN.hasFocus && state.email.isNotEmpty && state.emailError == null &&
          state.emailValidationStatus == ValidationStatus.valid) {
            _emailFN.unfocus();
            _passwordFN.requestFocus();
          }
        },
        builder: (BuildContext context, RegistrationState state) {
          return ListView(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                  children: [
                    SocialTypeSelector(
                      padding: const EdgeInsets.only(top: 32.0, bottom: 20.0),
                      // onMetaPressed: () =>
                      //   _registrationBloc.add(const RegistrationEvent.registerWithSocial(SocialAuthType.meta)),
                      onGooglePressed: () =>
                        _registrationBloc.add(const RegistrationEvent.registerWithSocial(SocialAuthType.google)),
                      onApplePressed: Platform.isIOS ? () =>
                        _registrationBloc.add(const RegistrationEvent.registerWithSocial(SocialAuthType.apple)) : null,
                    ),
                    MTextField(
                      hintText: t.screens.auth.registration.labels.username,
                      textEditingController: _userNameController,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(regExpLatinExtendedWithNumbers),
                      ],
                      focusNode: _userNameFN,
                      keyboardType: TextInputType.text,
                      error: state.userNameError,
                      onEditingComplete: () =>
                        _registrationBloc.add(const RegistrationEvent.validateUserName()),
                      onChanged: (value) =>
                        _registrationBloc.add(RegistrationEvent.changeUserName(value)),
                    ),
                    const SizedBox(height: 16.0),
                    MTextField(
                      hintText: t.screens.auth.registration.labels.email,
                      textEditingController: _emailController,
                      focusNode: _emailFN,
                      keyboardType: TextInputType.emailAddress,
                      error: state.emailError,
                      onEditingComplete: () =>
                        _registrationBloc.add(const RegistrationEvent.validateEmail()),
                      onChanged: (value) =>
                        _registrationBloc.add(RegistrationEvent.changeEmail(value)),
                    ),
                    const SizedBox(height: 16.0),
                    MDatePicker(
                      hintText: t.screens.auth.registration.labels.birthDate,
                      textEditingController: _birthDateController,
                      focusNode: _birthDateFN,
                      error: state.birthDateError,
                      minimumDate: DateTime(1920),
                      maximumDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
                      initialDate: state.birthDate.isEmpty
                          ? DateFormat('dd-MM-yyyy').format(DateTime.now().subtract(const Duration(days: 365 * 18)))
                          : state.birthDate,
                      onChanged: (value) =>
                        _registrationBloc.add(RegistrationEvent.changeDateOfBirth(value)),
                    ),
                    const SizedBox(height: 16.0),
                    MTextField(
                      hintText: t.screens.auth.registration.labels.password,
                      textEditingController: _passwordController,
                      focusNode: _passwordFN,
                      type: MTextFieldType.password,
                      obscure: state.isObscured,
                      obscureChanged: (value) =>
                        _registrationBloc.add(const RegistrationEvent.changeObscureText()),
                      keyboardType: TextInputType.visiblePassword,
                      error: state.passwordError,
                      onChanged: (value) {
                        _registrationBloc.add(RegistrationEvent.changePassword(value));
                        _registrationBloc.add(const RegistrationEvent.validatePassword());
                      },
                      onEditingComplete: () {
                        _registrationBloc.add(const RegistrationEvent.validatePassword());
                        _passwordFN.unfocus();
                        if (state.passwordError == null) {
                          _confirmPasswordFN.requestFocus();
                        }
                      },
                      onTapOutside: (onTapOutside) {
                        _passwordFN.unfocus();
                      },
                    ),
                    const SizedBox(height: 16.0),
                    MTextField(
                      hintText: t.screens.auth.registration.labels.confirmPassword,
                      textEditingController: _confirmPasswordController,
                      focusNode: _confirmPasswordFN,
                      type: MTextFieldType.password,
                      obscure: state.isConfirmObscured,
                      obscureChanged: (value) =>
                        _registrationBloc.add(const RegistrationEvent.changeConfirmObscureText()),
                      keyboardType: TextInputType.visiblePassword,
                      error: state.confirmPasswordError,
                      onChanged: (value) {
                        _registrationBloc.add(RegistrationEvent.changeConfirmPassword(value));
                        _registrationBloc.add(const RegistrationEvent.validateConfirmPassword());
                      },
                      onEditingComplete: () {
                        _registrationBloc.add(const RegistrationEvent.validateConfirmPassword());
                        _confirmPasswordFN.unfocus();
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24.0),
              RegistrationPrivacy(
                onChanged: (value) =>
                  _registrationBloc.add(RegistrationEvent.acceptPrivacyPolicy(value)),
                value: state.isPrivacyAccepted,
                error: state.privacyError,
              ),
              const SizedBox(height: 36.0),
              MElevatedButton(
                isLoading: state.status == RegistrationStatus.loading,
                margin: const EdgeInsets.symmetric(horizontal: 32.0),
                onPressed: () {
                  _registrationBloc.add(const RegistrationEvent.submit());
                },
                label: t.screens.auth.registration.buttonLabel,
              ),
              const SizedBox(height: 32.0),
            ],
          );
        }
      ),
    );
  }
}
