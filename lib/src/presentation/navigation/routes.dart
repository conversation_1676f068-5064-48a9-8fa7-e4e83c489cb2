class MRoutes {
  static const String splash = 'splash';
  static const String auth = 'auth';
  static const String onboarding = 'onboarding';
  static const String socialOnboarding = 'socialOnboarding';
  static const String discover = 'discover';
  static const String feed = 'feed';
  static const String search = 'search';
  static const String profile = 'profile';
  static const String chat = 'chat';
  static const String chats = 'chats';
  static const String scanner = 'scanner';
  static const String collection = 'collection';
  static const String restaurant = 'restaurant';
  static const String generalSettings = 'generalSettings';
  static const String userProfileSettings = 'userProfileSettings';
  static const String paymentSettings = 'paymentSettings';
  static const String feedback = 'feedback';
  static const String faq = 'faq';
  static const String contact = 'contact';
  static const String orders = 'orders';
  static const String subscriptionOverview = 'subscriptionOverview';
  static const String comments = 'comments';
  static const String deals = 'deals';
  static const String orderFlow = 'orderFlow';
  static const String coupon = 'coupon';
  static const String addPost = 'addPost';
  static const String following = 'following';
  static const String followers = 'followers';
  static const String feedSearch = 'feedSearch';
  static const String participant = 'participant';
  static const String reportUserPost = 'reportUserPost';
  static const String reportUser = 'reportUser';
  static const String blockUser = 'blockUser';
  static const String blockUserPost = 'blockUserPost';
  static const String review = 'review';
  static const String loyaltyTutorial = 'loyaltyTutorial';
  static const String notificationPermission = 'notificationPermission';

  static String getPath(String routeName) => '/$routeName';
}
