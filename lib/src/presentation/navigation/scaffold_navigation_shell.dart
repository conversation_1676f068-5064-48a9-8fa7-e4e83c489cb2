import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/presentation/auth/widgets/dialogs/otp_code_dialog.dart';
import 'package:mutualz/src/presentation/keys/global_keys.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';
import 'package:showcaseview/showcaseview.dart';

class ScaffoldNavigationShell extends StatefulWidget {
  final StatefulNavigationShell navigationShell;

  const ScaffoldNavigationShell({required this.navigationShell, super.key});

  @override
  State<ScaffoldNavigationShell> createState() => _ScaffoldNavigationShellState();
}

class _ScaffoldNavigationShellState extends State<ScaffoldNavigationShell> {
  late final AuthBloc _authBloc;
  late final AppBloc _appBloc;
  late final UserLoyaltyBloc _userLoyaltyBloc;
  late final DiscoverBloc _discoverBloc;
  late final SearchBloc _searchBloc;

  @override
  void initState() {
    super.initState();
    _authBloc = context.read<AuthBloc>();
    _appBloc = context.read<AppBloc>();
    _appBloc.add(const AppEvent.readStoredOrder());
    _userLoyaltyBloc = context.read<UserLoyaltyBloc>();
    _userLoyaltyBloc.add(const UserLoyaltyEvent.fetchUserLoyalty());
    _discoverBloc = context.read<DiscoverBloc>();
    _searchBloc = context.read<SearchBloc>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authBloc.getUserEmail().then((data) {
        if (!data.$1 && data.$2 != null) {
          showOTPCodeDialog(
            context: context,
            email: data.$2!,
            sendOtpForce: true,
            canBeClosed: false,
            onComplete: () {
              context.read<AuthBloc>().add(const AuthEvent.setIsEmailVerified(true));
              context.pop();
            }
          );
        }
      });
      _authBloc.add(const AuthEvent.fetchUser());
    });

  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;

    return MultiBlocListener(
      listeners: [
        BlocListener<AuthBloc, AuthState>(
          bloc: _authBloc,
          listener: (context, state) {
            if (state.user != null) {
              _appBloc.add(const AppEvent.setUserLoaded());
            }
          },
          listenWhen: (previous, current) {
            return previous.user == null &&
              current.user != null;
          }
        ),
        BlocListener<AppBloc, AppState>(
          bloc: _appBloc,
          listener: (context, state) {
            if (state.isUserDeactivated) {
              _appBloc.add(const AppEvent.setUserDeactivated(false));
              _authBloc.add(const AuthEvent.logout());
            }
          },
        ),
        BlocListener<DiscoverBloc, DiscoverState>(
          bloc: _discoverBloc,
          listener: (context, state) {
            if (state.restaurants.isNotEmpty) {
              _appBloc.add(const AppEvent.setRestaurantsLoaded(true));
            }
          },
          listenWhen: (previous, current) {
            return previous.restaurants.isEmpty && current.restaurants.isNotEmpty;
          }
        ),
      ],
      child: ShowCaseWidget(
        builder: (context) => Stack(
          key: rootGlobalKey,
          children: [
            Positioned.fill(
              child: widget.navigationShell,
            ),
            BlocBuilder<AppBloc, AppState>(
              bloc: _appBloc,
              builder: (context, state) {
                return AnimatedPositioned(
                  duration: const Duration(milliseconds: 300),
                  bottom: state.isBNBVisible ? 0.0 : -(displayWidth * MSizes.kHeightBnB),
                  child: Material(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(MSizes.cardBorderRadius),
                      topRight: Radius.circular(MSizes.cardBorderRadius),
                    ),
                    child: MBottomNavigationBar(
                      key: ValueKey(state.locale.name),
                      onItemSelected: (index) => widget.navigationShell.goBranch(index),
                      index: widget.navigationShell.currentIndex,
                    ),
                  ),
                );
              }
            ),
          ],
        ),
      ),
    );
  }
}