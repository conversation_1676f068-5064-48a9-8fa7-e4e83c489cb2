import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/presentation/auth/screens/auth_screen.dart';
import 'package:mutualz/src/presentation/auth/screens/social_auth_onboarding_screen.dart';
import 'package:mutualz/src/presentation/chat/screens/chat_screen.dart';
import 'package:mutualz/src/presentation/chat/screens/chats_screen.dart';
import 'package:mutualz/src/presentation/collection/screens/collection_screen.dart';
import 'package:mutualz/src/presentation/comments/screens/comments_screen.dart';
import 'package:mutualz/src/presentation/deals/screens/deals_screen.dart';
import 'package:mutualz/src/presentation/feed_search/screens/feed_search_screen.dart';
import 'package:mutualz/src/presentation/followers/screens/followers_screen.dart';
import 'package:mutualz/src/presentation/followers/screens/following_screen.dart';
import 'package:mutualz/src/presentation/home/<USER>/discover_screen.dart';
import 'package:mutualz/src/presentation/home/<USER>/feed_screen.dart';
import 'package:mutualz/src/presentation/home/<USER>/profile_screen.dart';
import 'package:mutualz/src/presentation/home/<USER>/search_screen.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/restaurant_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/navigation/scaffold_navigation_shell.dart';
import 'package:mutualz/src/presentation/onboarding/screens/onboarding_screen.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/order_nav_bundle.dart';
import 'package:mutualz/src/presentation/order_flow/screens/order_flow_screen.dart';
import 'package:mutualz/src/presentation/participants/screens/participant_screen.dart';
import 'package:mutualz/src/presentation/posts/screens/add_post_screen.dart';
import 'package:mutualz/src/presentation/reports/screens/block_user_post_screen.dart';
import 'package:mutualz/src/presentation/reports/screens/report_user_post_screen.dart';
import 'package:mutualz/src/presentation/reports/screens/report_user_screen.dart';
import 'package:mutualz/src/presentation/restaurant/screens/restaurant_screen.dart';
import 'package:mutualz/src/presentation/scanner/models/scanner_nav_bundle.dart';
import 'package:mutualz/src/presentation/scanner/screens/scanner_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/contact_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/faq_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/feedback_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/general_settings_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/order_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/payment_method_screen.dart';
import 'package:mutualz/src/presentation/settings/screens/user_profile_settings.dart';
import 'package:mutualz/src/presentation/splash/screens/splash_screen.dart';
import 'package:mutualz/src/presentation/subscription/screens/subscription_overview.dart';
import 'package:mutualz/src/presentation/tutorial/screens/loyalty_tutorial_screen.dart';
import 'package:mutualz/src/presentation/notification_permission/screens/notification_permission_screen.dart';

import '../../domain/domain.dart';
import '../reports/screens/block_user_screen.dart';

final GoRouter routerConfig = GoRouter(
  initialLocation: MRoutes.getPath(MRoutes.splash),
  // initialLocation: MRoutes.getPath(MRoutes.review),

  debugLogDiagnostics: true,
  redirect: (context, state) {
    if (state.uri.path.contains('terms-of-use') || state.uri.path.contains('privacy-policy')) {
      throw const IgnoredDeeplinkException('Terms and privacy policy URLs are not handled by the app');
    }
    
    if (context.read<AppBloc>().state.isUserDeactivated) {
      return MRoutes.getPath(MRoutes.auth);
    }
    return null;
  },
  routes: <RouteBase>[
    GoRoute(
      name: MRoutes.splash,
      path: MRoutes.getPath(MRoutes.splash),
      builder: (BuildContext context, GoRouterState state) {
        return const SplashScreen();
      },
    ),
    GoRoute(
      name: MRoutes.auth,
      path: MRoutes.getPath(MRoutes.auth),
      builder: (BuildContext context, GoRouterState state) {
        return const AuthScreen();
      },
    ),
    GoRoute(
      name: MRoutes.socialOnboarding,
      path: MRoutes.getPath(MRoutes.socialOnboarding),
      builder: (BuildContext context, GoRouterState state) {
        SocialAuthOnBoardingRequest? request =
            state.extra is SocialAuthOnBoardingRequest
                ? state.extra as SocialAuthOnBoardingRequest
                : null;
        return SocialAuthOnboardingScreen(request: request);
      },
    ),
    GoRoute(
      name: MRoutes.onboarding,
      path: MRoutes.getPath(MRoutes.onboarding),
      builder: (BuildContext context, GoRouterState state) {
        return const OnboardingScreen();
      },
    ),
    StatefulShellRoute.indexedStack(
      builder: (BuildContext context, GoRouterState state,
          StatefulNavigationShell navigationShell) {
        return ScaffoldNavigationShell(navigationShell: navigationShell);
      },
      branches: <StatefulShellBranch>[
        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: MRoutes.getPath(MRoutes.discover),
              name: MRoutes.discover,
              builder: (BuildContext context, GoRouterState state) {
                return const DiscoverScreen();
              },
            ),
          ],
        ),
        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: MRoutes.getPath(MRoutes.feed),
              name: MRoutes.feed,
              builder: (BuildContext context, GoRouterState state) {
                return const FeedScreen();
              },
            ),
          ],
        ),
        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: MRoutes.getPath(MRoutes.search),
              name: MRoutes.search,
              builder: (BuildContext context, GoRouterState state) {
                return const SearchScreen();
              },
            ),
          ],
        ),
        StatefulShellBranch(
          routes: <RouteBase>[
            GoRoute(
              path: MRoutes.getPath(MRoutes.profile),
              name: MRoutes.profile,
              builder: (BuildContext context, GoRouterState state) {
                return const ProfileScreen();
              },
            ),
          ],
        ),
      ],
    ),
    GoRoute(
      name: MRoutes.chat,
      path: MRoutes.getPath(MRoutes.chat),
      builder: (BuildContext context, GoRouterState state) {
        return const ChatScreen();
      },
    ),
    GoRoute(
      name: MRoutes.scanner,
      path: MRoutes.getPath(MRoutes.scanner),
      builder: (BuildContext context, GoRouterState state) {
        final ScannerNavBundle bundle = state.extra as ScannerNavBundle;
        return ScannerScreen(bundle: bundle);
      },
    ),
    GoRoute(
      name: MRoutes.collection,
      path: MRoutes.getPath(MRoutes.collection),
      builder: (BuildContext context, GoRouterState state) {
        final collection = state.extra as CollectionModel;
        return CollectionScreen(collection: collection);
      },
    ),
    GoRoute(
      name: MRoutes.restaurant,
      path: MRoutes.getPath(MRoutes.restaurant),
      builder: (BuildContext context, GoRouterState state) {
        final bundle = state.extra as RestaurantNavBundle;
        return RestaurantScreen(
          bundle: bundle,
        );
      },
    ),
    GoRoute(
      name: MRoutes.generalSettings,
      path: MRoutes.getPath(MRoutes.generalSettings),
      builder: (BuildContext context, GoRouterState state) {
        return const GeneralSettingsScreen();
      },
    ),
    GoRoute(
      name: MRoutes.userProfileSettings,
      path: MRoutes.getPath(MRoutes.userProfileSettings),
      builder: (BuildContext context, GoRouterState state) {
        return const UserProfileSettings();
      },
    ),
    GoRoute(
      name: MRoutes.feedback,
      path: MRoutes.getPath(MRoutes.feedback),
      builder: (BuildContext context, GoRouterState state) {
        return const FeedbackScreen();
      },
    ),
    GoRoute(
      name: MRoutes.faq,
      path: MRoutes.getPath(MRoutes.faq),
      builder: (BuildContext context, GoRouterState state) {
        return const FAQScreen();
      },
    ),
    GoRoute(
      name: MRoutes.contact,
      path: MRoutes.getPath(MRoutes.contact),
      builder: (BuildContext context, GoRouterState state) {
        return const ContactScreen();
      },
    ),
    GoRoute(
      name: MRoutes.orders,
      path: MRoutes.getPath(MRoutes.orders),
      builder: (BuildContext context, GoRouterState state) {
        return const OrderScreen();
      },
    ),
    GoRoute(
      name: MRoutes.subscriptionOverview,
      path: MRoutes.getPath(MRoutes.subscriptionOverview),
      builder: (BuildContext context, GoRouterState state) {
        return const SubscriptionOverview();
      },
    ),
    GoRoute(
      name: MRoutes.comments,
      path: MRoutes.getPath(MRoutes.comments),
      builder: (BuildContext context, GoRouterState state) {
        final postId = state.extra as String;
        return CommentsScreen(postId: postId);
      },
    ),
    GoRoute(
      name: MRoutes.deals,
      path: MRoutes.getPath(MRoutes.deals),
      builder: (BuildContext context, GoRouterState state) {
        final specials = state.extra is List<SpecialModel>
            ? state.extra as List<SpecialModel>
            : List<SpecialModel>.from([]);
        return DealsScreen(
          specials: specials,
        );
      },
    ),
    GoRoute(
      name: MRoutes.chats,
      path: MRoutes.getPath(MRoutes.chats),
      builder: (BuildContext context, GoRouterState state) {
        return const ChatsScreen();
      },
    ),
    GoRoute(
      name: MRoutes.orderFlow,
      path: MRoutes.getPath(MRoutes.orderFlow),
      builder: (BuildContext context, GoRouterState state) {
        final bundle = state.extra as OrderNavBundle;
        return OrderFlowScreen(
          bundle: bundle,
        );
      },
    ),
    GoRoute(
      name: MRoutes.paymentSettings,
      path: MRoutes.getPath(MRoutes.paymentSettings),
      builder: (BuildContext context, GoRouterState state) {
        return const PaymentMethodScreen();
      },
    ),
    GoRoute(
      name: MRoutes.addPost,
      path: MRoutes.getPath(MRoutes.addPost),
      builder: (BuildContext context, GoRouterState state) {
        final restaurants = state.extra as List<RestaurantModel>;
        return AddPostScreen(
          restaurants: restaurants,
        );
      },
    ),
    GoRoute(
      name: MRoutes.following,
      path: MRoutes.getPath(MRoutes.following),
      builder: (BuildContext context, GoRouterState state) {
        return const FollowingScreen();
      },
    ),
    GoRoute(
      name: MRoutes.followers,
      path: MRoutes.getPath(MRoutes.followers),
      builder: (BuildContext context, GoRouterState state) {
        return const FollowersScreen();
      },
    ),
    GoRoute(
      name: MRoutes.feedSearch,
      path: MRoutes.getPath(MRoutes.feedSearch),
      builder: (BuildContext context, GoRouterState state) {
        return const FeedSearchScreen();
      },
    ),
    GoRoute(
      name: MRoutes.participant,
      path: MRoutes.getPath(MRoutes.participant),
      builder: (BuildContext context, GoRouterState state) {
        final participantId = state.extra as String;
        return ParticipantScreen(
          participantId: participantId,
        );
      },
    ),
    GoRoute(
      name: MRoutes.reportUserPost,
      path: MRoutes.getPath(MRoutes.reportUserPost),
      builder: (BuildContext context, GoRouterState state) {
        final postId = state.extra as String;
        // final params = state.extra as ReportUserPostScreenParams;
        return ReportUserPostScreen(postId: postId);
      },
    ),
    GoRoute(
      name: MRoutes.reportUser,
      path: MRoutes.getPath(MRoutes.reportUser),
      builder: (BuildContext context, GoRouterState state) {
        final userId = state.extra as String;
        return ReportUserScreen(userId: userId);
      },
    ),
    GoRoute(
      name: MRoutes.blockUser,
      path: MRoutes.getPath(MRoutes.blockUser),
      builder: (BuildContext context, GoRouterState state) {
        final userId = state.extra as String;
        return BlockUserScreen(userId: userId);
      },
    ),
    GoRoute(
      name: MRoutes.blockUserPost,
      path: MRoutes.getPath(MRoutes.blockUserPost),
      builder: (BuildContext context, GoRouterState state) {
        final postId = state.extra as String;
        return BlockUserPostScreen(postId: postId);
      },
    ),
    GoRoute(
      name: MRoutes.loyaltyTutorial,
      path: MRoutes.getPath(MRoutes.loyaltyTutorial),
      builder: (BuildContext context, GoRouterState state) {
        return const LoyaltyTutorialScreen();
      },
    ),
    GoRoute(
      name: MRoutes.notificationPermission,
      path: MRoutes.getPath(MRoutes.notificationPermission),
      builder: (BuildContext context, GoRouterState state) {
        return const NotificationPermissionScreen();
      },
    ),
  ],
);
