import 'dart:async';
import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/blocs/auth/auth_bloc.dart';
import 'package:mutualz/src/domain/blocs/blocs.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/utils/scanner_utils.dart';
import 'package:mutualz/src/presentation/navigation/navigation_models/order_nav_bundle.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/scanner/models/scanner_nav_bundle.dart';
import 'package:mutualz/src/presentation/theme/massets.dart';
import 'package:mutualz/src/presentation/theme/mcolors.dart';
import 'package:mutualz/src/presentation/widgets/dialogs/error_dialog.dart';

class ScannerScreen extends StatefulWidget {
  final ScannerNavBundle bundle;
  final bool isFromMenuScreen;

  const ScannerScreen(
      {required this.bundle, this.isFromMenuScreen = false, super.key});

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen>
    with WidgetsBindingObserver {
  late final AuthBloc _authBloc;
  MobileScannerController cameraController = MobileScannerController(
    useNewCameraSelector: true,
  );
  StreamSubscription<BarcodeCapture>? _subscription;
  String? _qrCode;
  RestaurantModel? _restaurant;
  String? _token;
  String? _error;
  bool _isNeedToTransition = false;

  final StreamController<bool> _isNeedToTransitionController =
      StreamController<bool>();

  @override
  void initState() {
    super.initState();
    _authBloc = context.read<AuthBloc>();
    _isNeedToTransitionController.stream.listen((needToTransition) {
      if (!_isNeedToTransition &&
          needToTransition &&
          _restaurant != null &&
          _token != null &&
          mounted) {
        Future.delayed(const Duration(milliseconds: 350), () {
          _moveToMenu();
        });
      }
      _isNeedToTransition = needToTransition;
    });
  }

  Future<void> _moveToMenu() async {
    if (!_restaurant!.isCurrentlyOpen) {
      showErrorDialog(
        context: context,
        message: t.scanner.errorRestaurantClosed(name: _restaurant!.name),
      );
      return;
    }

    _authBloc.add(AuthEvent.initiateRestaurantSession(_token!));

    final OrderNavBundle orderNavBundle = OrderNavBundle(
      restaurantId: _restaurant!.id,
      restaurantName: _restaurant!.name,
      isRestaurantFavorite: _restaurant!.isFavorite,
      isFirstOrder: widget.bundle.isFirstOrder,
      stripeAccountId: _restaurant!.owner?.stripeAccountId ?? '',
      orderAndPayOnly: _restaurant!.orderAndPayOnly,
      selfService: _restaurant!.selfService,
    );

    if (!widget.isFromMenuScreen) {
      context.pushReplacementNamed(
        MRoutes.orderFlow,
        extra: orderNavBundle,
      );
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _isNeedToTransitionController.close();
    cameraController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final displayWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: MColors.onyxShadow,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        automaticallyImplyLeading: true,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: MColors.white,
            size: 20.0,
          ),
          onPressed: () => context.pop(),
        ),
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: MobileScanner(
              controller: cameraController,
              fit: BoxFit.cover,
              onDetect: (BarcodeCapture capture) {
                setState(() {
                  _error = null;
                });
                try {
                  final List<Barcode> barcodes = capture.barcodes;
                  if (barcodes.isNotEmpty) {
                    final parsedUri =
                        Uri.tryParse(barcodes.last.displayValue ?? '');
                    final token = parsedUri?.queryParameters['token'];

                    if (parsedUri != null &&
                        token != null &&
                        token.isNotEmpty) {
                      final restaurantId =
                          ScannerUtils.getRestaurantIdByToken(token ?? '');

                      final restaurant = widget.bundle.restaurants
                          .singleWhereOrNull(
                              (restaurant) => restaurant.id == restaurantId);

                      if (widget.bundle.scanningFromRestaurantId != null &&
                          widget.bundle.scanningFromRestaurantId !=
                              restaurantId) {
                        setState(() {
                          _error = t.scanner.errorDifferentRestaurant;
                          _restaurant = null;
                          _qrCode = null;
                          _token = null;
                        });
                      } else {
                        setState(() {
                          _restaurant = restaurant;
                          _qrCode = restaurant?.name;
                          _token = token;
                        });
                        _isNeedToTransitionController.add(true);
                      }
                    }
                  }
                } catch (e) {
                  log('Error in barcode detection: $e');
                }
              },
              overlayBuilder: (context, constrains) => Container(
                width: 300,
                height: 300,
                alignment: Alignment.center,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SvgPicture.asset(MAssets.frames),
                  ],
                ),
              ),
            ),
          ),
          // Positioned.fill(
          //   child: Container(
          //     padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
          //     color: Colors.black.withOpacity(0.15),
          //     alignment: Alignment.bottomCenter,
          //     child: GestureDetector(
          //       onTap: () {
          //         if (_restaurant != null && _token != null) {
          //           _moveToMenu();
          //         }
          //       },
          //       child: Container(
          //         padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
          //         decoration: BoxDecoration(
          //           color: _qrCode == null ?
          //             MColors.slateGrayMist.withOpacity(0.8) :
          //             MColors.scarletEmber.withOpacity(0.8),
          //           borderRadius: BorderRadius.circular(10.0),
          //         ),
          //         width: displayWidth * 0.7,
          //         child: Row(
          //           mainAxisAlignment: MainAxisAlignment.center,
          //           children: [
          //             Flexible(
          //               child: Text(
          //                 _qrCode ?? 'Scanning...',
          //                 style: Theme.of(context).textTheme.titleLarge?.copyWith(
          //                   color: MColors.white,
          //                 ),
          //                 maxLines: 1,
          //                 overflow: TextOverflow.ellipsis,
          //               ),
          //             ),
          //             Visibility(
          //               visible: _qrCode != null,
          //               child: SvgPicture.asset(
          //                 MAssets.arrowRight,
          //                 width: 24.0,
          //                 height: 24.0,
          //               ),
          //             )
          //           ],
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
          Positioned.fill(
            top: -MediaQuery.of(context).size.width * 0.3 - 300.0,
            child: Center(
              child: Text(
                t.scanner.label,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: MColors.white,
                    ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Positioned.fill(
            bottom: -MediaQuery.of(context).size.width * 0.3 - 300.0,
            child: Visibility(
              visible: _error != null,
              child: Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16.0),
                  padding: const EdgeInsets.symmetric(
                      vertical: 4.0, horizontal: 8.0),
                  decoration: BoxDecoration(
                    color: MColors.white,
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  child: Text(
                    _error ?? '',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: MColors.scarletEmber,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
