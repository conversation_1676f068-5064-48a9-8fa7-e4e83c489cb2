import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/services/notifications/notification_service.dart';
import 'package:mutualz/src/presentation/navigation/routes.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:mutualz/src/presentation/widgets/widgets.dart';

class NotificationPermissionScreen extends StatefulWidget {
  const NotificationPermissionScreen({
    super.key,
    this.onComplete,
    this.onSkip,
  });

  /// Called when user grants permission or completes the flow
  final VoidCallback? onComplete;

  /// Called when user skips permission request
  final VoidCallback? onSkip;

  @override
  State<NotificationPermissionScreen> createState() => _NotificationPermissionScreenState();

  /// Shows notification permission screen as a modal in order flow
  static Future<bool> showAsModal(
    BuildContext context, {
    bool barrierDismissible = false,
  }) async {
    bool permissionGranted = false;

    await showGeneralDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: 'Notification Permission',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        var offsetAnimation = animation.drive(tween);
        var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: curve),
        );

        return SlideTransition(
          position: offsetAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              child: child,
            ),
          ),
        );
      },
      pageBuilder: (BuildContext dialogContext, Animation<double> animation, Animation<double> secondaryAnimation) {
        return NotificationPermissionScreen(
          onComplete: () {
            permissionGranted = true;
            Navigator.of(dialogContext).pop();
          },
          onSkip: () {
            permissionGranted = false;
            Navigator.of(dialogContext).pop();
          },
        );
      },
    );

    return permissionGranted;
  }

  /// Checks permission and shows screen if needed (for order flow)
  /// Returns true if user has or grants permission, false if they decline
  static Future<bool> checkAndRequestIfNeeded(BuildContext context) async {
    final notificationService = getIt<NotificationService>();
    final hasPermission = await notificationService.hasNotificationPermission();

    if (!hasPermission && context.mounted) {
      return await showAsModal(context);
    }

    return hasPermission;
  }
}

class _NotificationPermissionScreenState extends State<NotificationPermissionScreen> {
  final NotificationService _notificationService = getIt<NotificationService>();
  bool _isRequesting = false;

  Future<void> _handleContinue() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      // Check if permission was previously denied
      final isPermanentlyDenied = await _notificationService.isPermissionPermanentlyDenied();

      if (isPermanentlyDenied) {
        // Open system settings instead of requesting permission
        await _notificationService.openNotificationSettings();
      } else {
        // Try to request permission (may or may not show system dialog)
        await _notificationService.requestNotificationPermission();
      }
    } catch (e) {
      debugPrint('DEBUG, error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });

        // Use callback if provided, otherwise default navigation
        if (widget.onComplete != null) {
          widget.onComplete!();
        } else {
          context.goNamed(MRoutes.discover);
        }
      }
    }
  }

  void _handleDecideLater() {
    // Use callback if provided, otherwise default navigation
    if (widget.onSkip != null) {
      widget.onSkip!();
    } else {
      context.goNamed(MRoutes.discover);
    }
  }

  @override
  Widget build(BuildContext context) {
    final notificationStrings = t.screens.notificationPermission;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark,
      child: Scaffold(
        body: SafeArea(
          bottom: true,
          child: Padding(
            padding: const EdgeInsets.only(top: 40, left: 40, right: 40, bottom: 10),
            child: Column(
              children: [
                Text(
                  notificationStrings.title,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        color: MColors.black,
                        fontSize: 30,
                      ),
                ),
                const SizedBox(height: 40.0),
                Expanded(
                  child: Image.asset(
                    MAssets.tutorialNotificationPermission,
                    fit: BoxFit.contain,
                  ),
                ),
                const SizedBox(height: 30.0),
                Text(
                  notificationStrings.description,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: MColors.midnightNavySlate,
                        height: 1.5,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 30.0),
                MElevatedButton(
                  onPressed: _handleDecideLater,
                  isLoading: _isRequesting,
                  label: notificationStrings.buttons.decideLater,
                  background: const Color(0xFFC1DADE),
                ),
                const SizedBox(height: 16.0),
                MElevatedButton(
                  onPressed: () {
                    if (_isRequesting) return;

                    _handleContinue();
                  },
                  isLoading: _isRequesting,
                  label: notificationStrings.buttons.next,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
