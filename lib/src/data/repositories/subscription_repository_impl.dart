import 'package:mutualz/src/data/sources/remote/subscription/subscription_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionRemoteSource _subscriptionRemoteSource;

  SubscriptionRepositoryImpl(this._subscriptionRemoteSource);

  @override
  Future<EphemeralKeyResponse> obtainEphemeralKey() async {
    return _subscriptionRemoteSource.obtainEphemeralKey();
  }

  @override
  Future<SubscriptionDataResponse> createMemberSubscription() async {
    return _subscriptionRemoteSource.createMemberSubscription();
  }

  @override
  Future cancelMemberSubscription() async {
    return _subscriptionRemoteSource.cancelMemberSubscription();
  }

  @override
  Future<void> resumeMemberSubscription() async {
    return _subscriptionRemoteSource.resumeMemberSubscription();
  }
}