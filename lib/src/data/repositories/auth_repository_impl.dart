import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/data/sources/remote/auth/auth_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/auth/logout_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/user/user_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/requests/auth/request_password_reset_request.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteSource _authRemoteSource;
  final LogoutRemoteSource _logoutRemoteSource;
  final StorageService _storageService;
  final UserRemoteSource _userRemoteSource;

  static const String _isEmailVerifiedKey = 'isEmailVerified';

  AuthRepositoryImpl(
    this._storageService,
    this._authRemoteSource,
    this._logoutRemoteSource,
    this._userRemoteSource,
  );

  @override
  Future<TokensResponse> login(LoginRequest request) async {
    return _authRemoteSource.login(request);
  }

  @override
  Future<TokensResponse> registration(RegistrationRequest request) async {
    return _authRemoteSource.registration(request);
  }

  @override
  Future<void> verifyEmail(VerifyEmailRequest request) async {
    return _authRemoteSource.verifyEmail(request);
  }

  @override
  Future<void> resendOtp(ResendOtpRequest request) async {
    return _authRemoteSource.resendOtp(request);
  }

  @override
  Future<void> logout() async {
    return _logoutRemoteSource.logout();
  }

  @override
  Future<bool> getIsEmailVerified() async {
    final String? value = await _storageService.storage.read(key: _isEmailVerifiedKey);
    return value != null ? bool.tryParse(value.toString()) ?? false : false;
  }

  @override
  Future<void> saveIsEmailVerified(bool isEmailVerified) async {
    return _storageService.storage
        .write(key: _isEmailVerifiedKey, value: isEmailVerified.toString());
  }

  @override
  Future<void> deleteUser() async {
    await _storageService.storage.delete(key: _isEmailVerifiedKey);
    return _userRemoteSource.deleteUser();
  }

  @override
  Future<void> requestPasswordReset(RequestPasswordResetRequest request) async {
    return _authRemoteSource.requestPasswordReset(request);
  }

  @override
  Future<void> resetPassword(ResetPasswordRequest request) async {
    return _authRemoteSource.resetPassword(request);
  }

  @override
  Future<TokensResponse> signInWithApple(SocialAuthRequest request) async {
    return _authRemoteSource.signInWithApple(request);
  }

  @override
  Future<TokensResponse> signInWithGoogle(SocialAuthRequest request) async {
    return _authRemoteSource.signInWithGoogle(request);
  }

  @override
  Future<TokensResponse> signInWithMeta(SocialAuthRequest request) {
    return _authRemoteSource.signInWithMeta(request);
  }
}
