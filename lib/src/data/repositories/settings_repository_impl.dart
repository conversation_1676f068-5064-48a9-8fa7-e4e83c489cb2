import 'package:mutualz/src/data/sources/remote/settings/settings_remote_source.dart';
import 'package:mutualz/src/data/sources/storage/settings/settings_storage_source.dart';
import 'package:mutualz/src/domain/models/requests/settings/feedback_request.dart';
import 'package:mutualz/src/domain/models/requests/settings/support_request.dart';
import 'package:mutualz/src/domain/models/responses/settings/faqs_response.dart';
import 'package:mutualz/src/domain/repositories/settings_repository.dart';

class SettingsRepositoryImpl implements SettingsRepository {
  final SettingsRemoteSource _settingsRemoteSource;
  final SettingsStorageSource _settingsStorageSource;

  const SettingsRepositoryImpl(this._settingsRemoteSource, this._settingsStorageSource);

  @override
  Future<FAQsResponse> fetchFAQs() async {
    return _settingsRemoteSource.fetchFAQs();
  }

  @override
  Future<void> sendFeedback(FeedbackRequest request) async {
    return _settingsRemoteSource.sendFeedback(request);
  }

  @override
  Future<void> sendSupport(SupportRequest request) async {
    return _settingsRemoteSource.sendSupport(request);
  }

  @override
  Future<String?> getLocale() async {
    return _settingsStorageSource.getLocale();
  }

  @override
  Future<void> setLocale(String locale) async {
    return _settingsStorageSource.setLocale(locale);
  }

  @override
  Future<bool> getIsFirstLaunch() async {
    return _settingsStorageSource.getIsFirstLaunch();
  }

  @override
  Future<void> setIsFirstLaunch(bool isFirstLaunch) async {
    return _settingsStorageSource.setIsFirstLaunch(isFirstLaunch);
  }

  @override
  Future<void> clear() async {
    return _settingsStorageSource.clear();
  }
}