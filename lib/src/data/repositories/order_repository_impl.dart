import 'package:mutualz/src/data/sources/storage/order/order_storage_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/order_repository.dart';
import 'package:mutualz/src/data/sources/remote/order/order_remote_source.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderRemoteSource _orderRemoteSource;
  final OrderStorageSource _orderStorageSource;

  OrderRepositoryImpl(this._orderRemoteSource, this._orderStorageSource);

  @override
  Future<OrderResponse> createOrder(OrderRequestModel request) async {
    return _orderRemoteSource.createOrder(request);
  }

  @override
  Future<OrdersResponse> fetchOrder(String? restaurantId, PaymentStatus? status, SortType? sort) async {
    return _orderRemoteSource.fetchOrder(restaurantId, status?.name, sort?.name);
  }

  @override
  Future<PaymentIntentResponse> fetchPaymentIntent(String restaurantId) async {
    return _orderRemoteSource.fetchPaymentIntent(restaurantId);
  }

  @override
  Future<OrderResponse> updateOrder(String orderId, OrderRequestModel request) async {
    return _orderRemoteSource.updateOrder(orderId, request);
  }

  @override
  Future<EarnPointsResponse> earnPoints(String orderId) async {
    return _orderRemoteSource.earnPoints(orderId);
  }

  @override
  Future<PrecalculatedOrderResponse> precalculateOrder(PrecalculatedOrderRequest request) async {
    return _orderRemoteSource.precalculateOrder(request);
  }

  @override
  Future<void> attachCoupon(String orderId, String couponId) async {
    return _orderRemoteSource.attachCoupon(orderId, couponId);
  }

  @override
  Future<void> detachCoupon(String orderId) async {
    return _orderRemoteSource.detachCoupon(orderId);
  }

  @override
  Future<void> notifyServerForCouponsNotification() async {
    return _orderRemoteSource.notifyServerForCouponsNotification();
  }

  @override
  Future<void> clearStoredOrder() async {
    return _orderStorageSource.clearStoredOrder();
  }

  @override
  Future<void> createStoredOrder(StoredOrderModel model) async {
    return _orderStorageSource.createStoredOrder(model);
  }

  @override
  Future<StoredOrderModel?> readStoredOrder() async {
    return _orderStorageSource.readStoredOrder();
  }

  @override
  Future<void> createStoredCart(StoredCartModel model) {
    return _orderStorageSource.createStoredCart(model);
  }

  @override
  Future<StoredCartModel?> readStoredCart() {
    return _orderStorageSource.readStoredCart();
  }

  @override
  Future<void> clearStoredCart() {
    return _orderStorageSource.clearStoredCart();
  }

  @override
  Future<void> sendInvoice(String orderId) async {
    return _orderRemoteSource.sendInvoice(orderId);
  }
}