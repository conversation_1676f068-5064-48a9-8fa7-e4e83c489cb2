import 'dart:io';
import 'package:mutualz/src/data/sources/remote/user/user_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/responses/user/user_stranger_response.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteSource _userRemoteSource;

  UserRepositoryImpl(this._userRemoteSource);

  @override
  Future<void> autoRegister() async {
    return _userRemoteSource.autoRegister();
  }

  @override
  Future<void> applyOnBoarding(OnBoardingRequest request) async {
    List<Future<void>> futures = [
      _userRemoteSource.applyUserPreferences(request.preferences),
      _userRemoteSource.applyDietaryPreferences(request.dietary),
      _userRemoteSource.applyIntolerances(request.intolerances),
      if (request.occupation.occupation.isNotEmpty)
        _userRemoteSource.applyOccupation(request.occupation),
    ];

    await Future.wait(futures);
  }

  @override
  Future<UserResponse> getUser() async {
    return _userRemoteSource.getUserProfile();
  }

  @override
  Future<void> updateUser(UpdateUserRequest user) async {
    return _userRemoteSource.updateUser(user);
  }

  @override
  Future<void> changePassword(ChangePasswordRequest request) async {
    return _userRemoteSource.changePassword(request);
  }

  @override
  Future<UserImageResponse> uploadUserImage({required File request}) async {
    return _userRemoteSource.uploadUserImage(request);
  }

  @override
  Future<LoyaltyResponse> fetchUserLoyalty() async {
    return _userRemoteSource.fetchUserLoyalty();
  }

  @override
  Future<RestaurantSessionResponse> initiateRestaurantSession(RestaurantSessionRequest request) async {
    return _userRemoteSource.initiateRestaurantSession(request);
  }

  @override
  Future<void> deleteFcmToken(String deviceId) async {
    return _userRemoteSource.deleteFcmToken(deviceId);
  }

  @override
  Future<void> updateFcmToken(FcmTokenRequest request) async {
    return _userRemoteSource.updateFcmToken(request);
  }

  @override
  Future<UserStrangerResponse> getUserStranger(String userId) async {
    return _userRemoteSource.getUserStranger(userId);
  }

  @override
  Future<void> followUser(String userId) async {
    return _userRemoteSource.followUser(userId);
  }

  @override
  Future<void> unfollowUser(String userId) async {
    return _userRemoteSource.unfollowUser(userId);
  }

  @override
  Future<FollowersResponse> fetchFollowers() async {
    return _userRemoteSource.fetchFollowers();
  }

  @override
  Future<FollowingResponse> fetchFollowing() async  {
    return _userRemoteSource.fetchFollowing();
  }

  @override
  Future<UsersStrangerResponse> fetchUsersStranger() async {
    return _userRemoteSource.fetchUserList();
  }
}