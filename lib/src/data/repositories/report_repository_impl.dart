import 'package:mutualz/src/data/sources/remote/report/report_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/report_repository.dart';

class ReportRepositoryImpl implements ReportRepository {
  final ReportRemoteSource _reportRemoteSource;

  ReportRepositoryImpl(this._reportRemoteSource);

  @override
  Future<void> sendReportUserPost(
      String postId, ReportUserPostRequest request) async {
    return _reportRemoteSource.sendReportUserPost(postId, request);
  }

  @override
  Future<void> sendBlockUserPost(
      String postId, ReportUserPostRequest request) async {
    return _reportRemoteSource.sendBlockUserPost(postId, request);
  }

  @override
  Future<void> sendReportUser(String userId, ReportUserRequest request) async {
    return _reportRemoteSource.sendReportUser(userId, request);
  }

  @override
  Future<void> sendBlockUser(String userId, ReportUserRequest request) {
    return _reportRemoteSource.sendBlocUser(userId, request);
  }
}
