import 'dart:io';

import 'package:mutualz/src/data/sources/remote/post/post_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/post_repository.dart';

class PostRepositoryImpl implements PostRepository {
  final PostRemoteSource _postRemoteSource;

  PostRepositoryImpl(this._postRemoteSource);

  @override
  Future<PostResponse> addPostCover(String postId, File file) async {
    return _postRemoteSource.addRestaurantPostCover(postId, file);
  }

  @override
  Future<PostResponse> createPost(AddPostRequest request) async {
    return _postRemoteSource.addRestaurantPost(request);
  }

  @override
  Future<PostsResponse> fetchPosts(String? restaurantId, String? userId, bool? isMyPost) async {
    return _postRemoteSource.fetchPosts(restaurantId, userId, isMyPost);
  }

  @override
  Future<CommentsResponse> fetchComments(String postId) async {
    return _postRemoteSource.fetchComments(postId);
  }

  @override
  Future<CommentResponse> addComment(String postId, AddCommentRequest request) async {
    return _postRemoteSource.addComment(postId, request);
  }

  @override
  Future<void> deletePost(String postId) async {
    return _postRemoteSource.deletePost(postId);
  }

  @override
  Future<FavoritePostResponse> markPostFavorite(String postId) async {
    return _postRemoteSource.markPostFavorite(postId);
  }

  @override
  Future<FavoritePostResponse> unmarkPostFavorite(String postId) async {
    return _postRemoteSource.unmarkPostFavorite(postId);
  }

  @override
  Future<void> markCommentFavorite(String commentId) async {
    return _postRemoteSource.markCommentFavorite(commentId);
  }

  @override
  Future<void> unmarkCommentFavorite(String commentId) async {
    return _postRemoteSource.unmarkCommentFavorite(commentId);
  }
}