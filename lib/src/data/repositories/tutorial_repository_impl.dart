import '../../domain/models/requests/tutorial/tutorial_states_request.dart';
import '../../domain/models/responses/tutorial/tutorial_states_response.dart';
import '../../domain/models/responses/tutorial/tutorial_states_update_response.dart';
import '../../domain/repositories/tutorial_repository.dart';
import '../sources/remote/tutorial/tutorial_remote_source.dart';

class TutorialRepositoryImpl implements TutorialRepository {
  final TutorialRemoteSource _tutorialRemoteSource;

  const TutorialRepositoryImpl({
    required TutorialRemoteSource tutorialRemoteSource,
  }) : _tutorialRemoteSource = tutorialRemoteSource;

  @override
  Future<TutorialStatesResponse> getTutorialStates() async {
    return _tutorialRemoteSource.getTutorialStates();
  }

  @override
  Future<TutorialStatesUpdateResponse> updateTutorialStates(
    TutorialStatesRequest request,
  ) async {
    return _tutorialRemoteSource.updateTutorialStates(request);
  }

  @override
  Future<void> resetAllTutorialStates() async {
    await _tutorialRemoteSource.updateTutorialStates(
      TutorialStatesRequest.resetAll(),
    );
  }
} 