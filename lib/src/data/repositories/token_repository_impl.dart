import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/data/sources/remote/token/token_remote_source.dart';
import 'package:mutualz/src/domain/models/requests/token/refresh_token_request.dart';
import 'package:mutualz/src/domain/models/responses/token/tokens_response.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';

class TokenRepositoryImpl implements TokenRepository {
  final StorageService _service;
  final TokenRemoteSource _remoteSource;

  TokenRepositoryImpl(this._service, this._remoteSource);

  static const String _accessTokenKey = 'accessToken';
  static const String _refreshTokenKey = 'refreshToken';

  @override
  Future<String?> getAccessToken() async {
    return _service.storage.read(key: _accessTokenKey);
  }

  @override
  Future<String?> getRefreshToken() async {
    return _service.storage.read(key: _refreshTokenKey);
  }

  @override
  Future<void> saveAccessToken(String token) async {
    return _service.storage.write(key: _accessTokenKey, value: token);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    return _service.storage.write(key: _refreshTokenKey, value: token);
  }

  @override
  Future<TokensResponse> refreshToken(RefreshTokenRequest refreshToken) async {
    return _remoteSource.refreshToken(refreshToken);
  }

  @override
  Future<void> clear() async {
    await _service.storage.delete(key: _accessTokenKey);
    await _service.storage.delete(key: _refreshTokenKey);
  }
}
