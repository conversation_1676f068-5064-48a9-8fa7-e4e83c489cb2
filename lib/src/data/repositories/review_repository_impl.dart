import 'dart:io';
import 'package:mutualz/src/data/sources/remote/review/review_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/review_repository.dart';

class ReviewRepositoryImpl implements ReviewRepository {
  final ReviewRemoteSource _reviewRemoteSource;

  const ReviewRepositoryImpl(this._reviewRemoteSource);

  @override
  Future<ReviewResponse> createReview(ReviewRequest request) async {
    return _reviewRemoteSource.createReview(request);
  }

  @override
  Future<void> createPostReview(String reviewId) async {
    return _reviewRemoteSource.createPostReview(reviewId);
  }

  @override
  Future<void> addCoverToReview(String reviewId, File image) async {
    return _reviewRemoteSource.addCoverToReview(reviewId, image);
  }
}