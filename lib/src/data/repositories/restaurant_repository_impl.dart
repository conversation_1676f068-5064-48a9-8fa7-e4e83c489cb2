import 'package:mutualz/src/data/sources/remote/restaurant/restaurant_remote_source.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';

class RestaurantRepositoryImpl implements RestaurantRepository {
  final RestaurantRemoteSource _restaurantRemoteSource;

  RestaurantRepositoryImpl(this._restaurantRemoteSource);

  @override
  Future<RestaurantsResponse> getRestaurants(
      [String? cityName, String? name]) async {
    return _restaurantRemoteSource.getRestaurants(cityName, name);
  }

  @override
  Future<void> addToFavorite(String id) async {
    return _restaurantRemoteSource.addToFavorite(id);
  }

  @override
  Future<void> removeFromFavorite(String id) async {
    return _restaurantRemoteSource.removeFromFavorite(id);
  }

  @override
  Future<CollectionsResponse> getCollections() async {
    return _restaurantRemoteSource.getCollections();
  }

  @override
  Future<void> bookTable(BookingTableRequest request) async {
    return _restaurantRemoteSource.bookTable(request);
  }

  @override
  Future<CollectionResponse> getCollectionById(String id) async {
    return _restaurantRemoteSource.getCollectionById(id);
  }

  @override
  Future<MenusResponse> fetchMenus(String restaurantId) async {
    return _restaurantRemoteSource.fetchMenus(restaurantId);
  }

  @override
  Future<RestaurantCouponData> getCoupons(String restaurantId) async {
    return _restaurantRemoteSource.getCoupons(restaurantId);
  }

  @override
  Future<void> refreshCoupon(String couponId, String restaurantId) async {
    return _restaurantRemoteSource.refreshCoupon(couponId, restaurantId);
  }

  @override
  Future<SpecialsResponse> fetchSpecials(String restaurantId) async {
    return _restaurantRemoteSource.fetchSpecials(restaurantId);
  }

  @override
  Future<MenuTemplateResponse> fetchMenuTemplate(String restaurantId) async {
    return _restaurantRemoteSource.fetchMenuTemplate(restaurantId);
  }

  @override
  Future<RestaurantCouponData> getCouponsByMenuOrder(
      PrecalculatedOrderRequest menuOrder, String restaurantId) {
    return _restaurantRemoteSource.getCouponsByMenuOrder(
        menuOrder, restaurantId);
  }
}
