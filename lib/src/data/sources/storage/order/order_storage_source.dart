import 'dart:convert';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/domain/models/models.dart';

class OrderStorageSource {
  final StorageService _storageService;

  static const String _storedOrderKey = 'STORED_ORDER';
  static const String _storedCartKey = 'STORED_CART';

  const OrderStorageSource(this._storageService);

  Future<void> createStoredOrder(StoredOrderModel model) async {
    final jsonString = jsonEncode(model.toJson());
    final prefs = await _storageService.sharedPreferences;
    await prefs.setString(_storedOrderKey, jsonString);
  }

  Future<StoredOrderModel?> readStoredOrder() async {
    final prefs = await _storageService.sharedPreferences;
    final jsonString = prefs.getString(_storedOrderKey);
    if (jsonString == null) {
      return null;
    }
    final json = jsonDecode(jsonString);
    return StoredOrderModel.fromJson(json);
  }

  Future<void> clearStoredOrder() async {
    final prefs = await _storageService.sharedPreferences;
    await prefs.remove(_storedOrderKey);
  }

  Future<void> createStoredCart(StoredCartModel model) async {
    final jsonString = jsonEncode(model.toJson());
    final prefs = await _storageService.sharedPreferences;
    await prefs.setString(_storedCartKey, jsonString);
  }

  Future<StoredCartModel?> readStoredCart() async {
    final prefs = await _storageService.sharedPreferences;
    final jsonString = prefs.getString(_storedCartKey);
    if (jsonString == null) {
      return null;
    }
    final json = jsonDecode(jsonString);
    return StoredCartModel.fromJson(json);
  }

  Future<void> clearStoredCart() async {
    final prefs = await _storageService.sharedPreferences;
    await prefs.remove(_storedCartKey);
  }
}