import 'package:mutualz/src/core/core.dart';

class SettingsStorageSource {
  final StorageService _storageService;

  static const String _localeKey = 'LOCALE';
  static const String _isFirstLaunchKey = 'IS_FIRST_LAUNCH';

  const SettingsStorageSource(this._storageService);

  Future<String?> getLocale () async {
    return _storageService.storage.read(key: _localeKey);
  }

  Future<void> setLocale (String locale) async {
    return _storageService.storage.write(key: _localeKey, value: locale);
  }

  Future<bool> getIsFirstLaunch () async {
    final sharedPreferences = await _storageService.sharedPreferences;
    return sharedPreferences.getBool(_isFirstLaunchKey) ?? true;
  }

  Future<void> setIsFirstLaunch (bool isFirstLaunch) async {
    final sharedPreferences = await _storageService.sharedPreferences;
    await sharedPreferences.setBool(_isFirstLaunchKey, isFirstLaunch);
  }

  Future<void> clear () async {
    await _storageService.storage.deleteAll();
    final sharedPreferences = await _storageService.sharedPreferences;
    await sharedPreferences.clear();
  }
}