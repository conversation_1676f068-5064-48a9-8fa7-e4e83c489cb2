import 'dart:io';
import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/retrofit.dart';

part 'user_remote_source.g.dart';

@RestApi()
abstract class UserRemoteSource {
  static const String v1 = '/v1';

  factory UserRemoteSource(Dio dio) = _UserRemoteSource;

  @GET('$v1/user/profile')
  Future<UserResponse> getUserProfile();

  @PATCH('$v1/user/profile')
  Future<void> updateUser(@Body() UpdateUserRequest user);

  @POST('$v1/auth/auto-register')
  Future<void> autoRegister();

  @DELETE('$v1/user/profile')
  Future<void> deleteUser();

  @PUT('$v1/user/preferences')
  Future<void> applyUserPreferences(@Body() UserPreferencesRequest request);

  @PUT('$v1/user/dietary-preferences')
  Future<void> applyDietaryPreferences(@Body() DietaryRequest request);

  @PUT('$v1/user/intolerances')
  Future<void> applyIntolerances(@Body() IntolerancesRequest request);

  @PUT('$v1/user/occupation')
  Future<void> applyOccupation(@Body() OccupationRequest request);

  @POST('$v1/auth/change-password')
  Future<void> changePassword(@Body() ChangePasswordRequest request);

  @POST('$v1/user/profile/image')
  @MultiPart()
  Future<UserImageResponse> uploadUserImage(@Part(name: 'profileImage') File image);

  @GET('$v1/user/loyalty')
  Future<LoyaltyResponse> fetchUserLoyalty();

  @POST('$v1/user/restaurant-session')
  Future<RestaurantSessionResponse> initiateRestaurantSession(@Body() RestaurantSessionRequest request);

  @POST('$v1/user/fcm-token')
  Future<void> updateFcmToken(@Body() FcmTokenRequest request);

  @DELETE('$v1/user/fcm-token/{deviceId}')
  Future<void> deleteFcmToken(@Path('deviceId') String deviceId);

  @GET('$v1/user/profile/{userId}')
  Future<UserStrangerResponse> getUserStranger(@Path('userId') String userId);

  @POST('$v1/user/{userId}/follow')
  Future<void> followUser(@Path('userId') String userId);

  @DELETE('$v1/user/{userId}/follow')
  Future<void> unfollowUser(@Path('userId') String userId);

  @GET('$v1/user/followers')
  Future<FollowersResponse> fetchFollowers();

  @GET('$v1/user/following')
  Future<FollowingResponse> fetchFollowing();

  @GET('$v1/user/list')
  Future<UsersStrangerResponse> fetchUserList();
}