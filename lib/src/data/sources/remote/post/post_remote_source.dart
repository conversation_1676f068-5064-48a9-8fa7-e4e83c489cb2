import 'dart:io';
import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/retrofit.dart';

part 'post_remote_source.g.dart';

@RestApi()
abstract class PostRemoteSource {
  static const String v1 = '/v1';

  factory PostRemoteSource(Dio dio) = _PostRemoteSource;

  @POST('$v1/post')
  Future<PostResponse> addRestaurantPost(@Body() AddPostRequest request);

  @POST('$v1/post/{postId}/cover')
  @MultiPart()
  Future<PostResponse> addRestaurantPostCover(@Path('postId') String postId, @Part() File coverImage);

  @GET('$v1/post')
  Future<PostsResponse> fetchPosts(
    @Query('restaurantId') String? restaurantId,
    @Query('userId') String? userId,
    @Query('isMyPost') bool? isMyPost,
  );

  @GET('$v1/post/{postId}/comment')
  Future<CommentsResponse> fetchComments(@Path('postId') String postId);

  @POST('$v1/post/{postId}/comment')
  Future<CommentResponse> addComment(@Path('postId') String postId, @Body() AddCommentRequest request);

  @DELETE('$v1/post/{postId}')
  Future<void> deletePost(@Path('postId') String postId);

  @POST('$v1/post/{postId}/favorite')
  Future<FavoritePostResponse> markPostFavorite(@Path('postId') String postId);

  @DELETE('$v1/post/{postId}/favorite')
  Future<FavoritePostResponse> unmarkPostFavorite(@Path('postId') String postId);

  @POST('$v1/comment/{commentId}/favorite')
  Future<void> markCommentFavorite(@Path('commentId') String commentId);

  @DELETE('$v1/comment/{commentId}/favorite')
  Future<void> unmarkCommentFavorite(@Path('commentId') String commentId);
}
