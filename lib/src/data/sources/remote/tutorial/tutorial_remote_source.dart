import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../../../../domain/models/requests/tutorial/tutorial_states_request.dart';
import '../../../../domain/models/responses/tutorial/tutorial_states_response.dart';
import '../../../../domain/models/responses/tutorial/tutorial_states_update_response.dart';

part 'tutorial_remote_source.g.dart';

@RestApi()
abstract class TutorialRemoteSource {
  static const String v1 = '/v1';

  factory TutorialRemoteSource(Dio dio) = _TutorialRemoteSource;

  @GET('$v1/user/tutorial-states')
  Future<TutorialStatesResponse> getTutorialStates();

  @PUT('$v1/user/tutorial-states')
  Future<TutorialStatesUpdateResponse> updateTutorialStates(
    @Body() TutorialStatesRequest request,
  );
}