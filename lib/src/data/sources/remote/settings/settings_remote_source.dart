import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/retrofit.dart';

part 'settings_remote_source.g.dart';

@RestApi()
abstract class SettingsRemoteSource {
  static const String v1 = '/v1';

  factory SettingsRemoteSource(Dio dio) = _SettingsRemoteSource;

  @GET('$v1/faq')
  Future<FAQsResponse> fetchFAQs();

  @POST('$v1/feedback')
  Future<void> sendFeedback(@Body() FeedbackRequest request);

  @POST('$v1/support')
  Future<void> sendSupport(@Body() SupportRequest request);
}