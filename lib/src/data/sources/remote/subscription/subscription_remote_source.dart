import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/responses/subscription/subscription_data_response.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'subscription_remote_source.g.dart';

@RestApi()
abstract class SubscriptionRemoteSource {
  static const String v1 = '/v1';

  factory SubscriptionRemoteSource(Dio dio) = _SubscriptionRemoteSource;

  @POST('$v1/payment/ephemeral-key')
  Future<EphemeralKeyResponse> obtainEphemeralKey();

  @POST('$v1/payment/method/{paymentMethodId}/change')
  Future<void> setDefaultPaymentMethod(@Path('paymentMethodId') String paymentMethodId);

  @POST('$v1/payment/subscription')
  Future<SubscriptionDataResponse> createMemberSubscription();

  @PATCH('$v1/payment/subscription')
  Future<void> resumeMemberSubscription();

  @DELETE('$v1/payment/subscription')
  Future<void> cancelMemberSubscription();
}