import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/retrofit.dart';

part 'report_remote_source.g.dart';

@RestApi()
abstract class ReportRemoteSource {
  static const String v1 = '/v1';

  factory ReportRemoteSource(Dio dio) = _ReportRemoteSource;

  @POST('$v1/post/{postId}/report')
  Future<void> sendReportUserPost(
      @Path('postId') String postId, @Body() ReportUserPostRequest request);

  @POST('$v1/post/{postId}/block')
  Future<void> sendBlockUserPost(
      @Path('postId') String postId, @Body() ReportUserPostRequest request);

  @POST('$v1/user/{userId}/block')
  Future<void> sendBlocUser(
      @Path('userId') String userId, @Body() ReportUserRequest request);

  @POST('$v1/user/{userId}/report')
  Future<void> sendReportUser(
      @Path('userId') String userId, @Body() ReportUserRequest request);
}
