import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/retrofit.dart';

part 'token_remote_source.g.dart';

@RestApi()
abstract class TokenRemoteSource {
  static const String v1 = '/v1';

  factory TokenRemoteSource(Dio dio) = _TokenRemoteSource;

  @POST('$v1/auth/refresh-token')
  Future<TokensResponse> refreshToken(@Body() RefreshTokenRequest request);
}