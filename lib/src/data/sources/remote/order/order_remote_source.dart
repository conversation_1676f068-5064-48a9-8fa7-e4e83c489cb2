import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'order_remote_source.g.dart';

@RestApi()
abstract class OrderRemoteSource {
  static const String v1 = '/v1';

  factory OrderRemoteSource(Dio dio) = _OrderRemoteSource;

  @POST('$v1/order')
  Future<OrderResponse> createOrder(@Body() OrderRequestModel request);

  @PATCH('$v1/order/{orderId}')
  Future<OrderResponse> updateOrder(@Path() String orderId, @Body() OrderRequestModel request);

  @GET('$v1/order')
  Future<OrdersResponse> fetchOrder(
    @Query('restaurantId') String? restaurantId,
    @Query('paymentStatus') String? paymentStatus,
    @Query('sort') String? sort,
  );

  @POST('$v1/payment/intent/order/{restaurantId}')
  Future<PaymentIntentResponse> fetchPaymentIntent(
    @Path('restaurantId') String restaurantId
  );

  @POST('$v1/order/{orderId}/earn-points')
  Future<EarnPointsResponse> earnPoints(@Path() String orderId);

  @POST('$v1/order/pre-order')
  Future<PrecalculatedOrderResponse> precalculateOrder(@Body() PrecalculatedOrderRequest request);

  @POST('$v1/order/{orderId}/coupon/{couponId}/apply')
  Future<void> attachCoupon(@Path() String orderId, @Path() String couponId);

  @DELETE('$v1/order/{orderId}/coupon/remove')
  Future<void> detachCoupon(@Path() String orderId);

  @POST('$v1/coupons/refreshment-notification')
  Future<void> notifyServerForCouponsNotification();

  @POST('$v1/order/{orderId}/send-invoice')
  Future<void> sendInvoice(@Path() String orderId);
}