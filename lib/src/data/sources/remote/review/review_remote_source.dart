import 'dart:io';

import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'review_remote_source.g.dart';

@RestApi()
abstract class ReviewRemoteSource {
  static const String v1 = '/v1';

  factory ReviewRemoteSource(Dio dio) = _ReviewRemoteSource;

  @POST('$v1/reviews')
  Future<ReviewResponse> createReview(@Body() ReviewRequest request);

  @POST('$v1/reviews/{reviewId}/post')
  Future<void> createPostReview(@Path() String reviewId);

  @POST('$v1/reviews/{reviewId}/cover')
  @MultiPart()
  Future<void> addCoverToReview(@Path() String reviewId, @Part() File coverImage);
}