import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:retrofit/http.dart';
import 'package:retrofit/retrofit.dart';

part 'restaurant_remote_source.g.dart';

@RestApi()
abstract class RestaurantRemoteSource {
  static const String v1 = '/v1';

  factory RestaurantRemoteSource(Dio dio) = _RestaurantRemoteSource;

  @GET('$v1/restaurants')
  Future<RestaurantsResponse> getRestaurants(
      @Query('city') String? cityName, @Query('name') String? name);

  @POST('$v1/restaurants/{id}/favorite')
  Future<void> addToFavorite(@Path('id') String id);

  @DELETE('$v1/restaurants/{id}/favorite')
  Future<void> removeFromFavorite(@Path('id') String id);

  @GET('$v1/collections')
  Future<CollectionsResponse> getCollections();

  @GET('$v1/collections/{id}')
  Future<CollectionResponse> getCollectionById(@Path('id') String id);

  @POST('$v1/booking')
  Future<void> bookTable(@Body() BookingTableRequest request);

  @GET('$v1/menu/by-restaurant/{restaurantId}')
  Future<MenusResponse> fetchMenus(@Path('restaurantId') String restaurantId);

  @GET('$v1/coupons')
  Future<RestaurantCouponData> getCoupons(
      @Query('restaurantId') String restaurantId);

  @POST('$v1/coupons')
  Future<RestaurantCouponData> getCouponsByMenuOrder(
      @Body() PrecalculatedOrderRequest menuOrder,
      @Query('restaurantId') String restaurantId);

  @POST('$v1/coupons/{couponId}/restaurants/{restaurantId}/refresh')
  Future<void> refreshCoupon(
      @Path() String couponId, @Path() String restaurantId);

  @GET('$v1/specials')
  Future<SpecialsResponse> fetchSpecials(
      @Query('restaurantId') String restaurantId);

  @GET('$v1/menu-template/default')
  Future<MenuTemplateResponse> fetchMenuTemplate(
      @Path('restaurantId') String restaurantId);
}
