import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/models/models.dart';
import 'package:mutualz/src/domain/models/requests/auth/request_password_reset_request.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_remote_source.g.dart';

@RestApi()
abstract class AuthRemoteSource {
  static const String v1 = 'v1';
  static const String prefix = 'auth';

  factory AuthRemoteSource(Dio dio) = _AuthRemoteSource;

  @POST('/$v1/$prefix/login')
  Future<TokensResponse> login(@Body() LoginRequest request);

  @POST('/$v1/$prefix/register')
  Future<TokensResponse> registration(@Body() RegistrationRequest request);

  @POST('/$v1/$prefix/verify-email')
  Future verifyEmail(@Body() VerifyEmailRequest request);

  @POST('/$v1/$prefix/resend-otp')
  Future resendOtp(@Body() ResendOtpRequest request);

  @POST('/$v1/$prefix/social/google')
  Future<TokensResponse> signInWithGoogle(@Body() SocialAuthRequest request);

  @POST('/$v1/$prefix/social/apple')
  Future<TokensResponse> signInWithApple(@Body() SocialAuthRequest request);

  @POST('/$v1/$prefix/social/meta')
  Future<TokensResponse> signInWithMeta(@Body() SocialAuthRequest request);

  @POST('/$v1/$prefix/request-password-reset')
  Future requestPasswordReset(@Body() RequestPasswordResetRequest request);

  @POST('/$v1/$prefix/reset-password')
  Future resetPassword(@Body() ResetPasswordRequest request);
}
