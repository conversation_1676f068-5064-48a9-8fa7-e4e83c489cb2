extension Validation on String {
  bool get isHasEnoughPasswordLength => length >= 8 && length < 60;

  bool get isHasEnoughUserNameLength => length >= 6 && length < 30;

  bool get isHasNumber => contains(RegExp(r'\d'));

  bool get isHasUpperCase => contains(RegExp(r'[A-Z]'));

  bool get isHasLowerCase => contains(RegExp(r'[a-z]'));

  bool get isHasSpecialChar => contains(RegExp(r'[!@#$%^&*(),.?":;{}|\[\]<>+_-]'));

  bool isIdentical(String secondValue) =>
      this == secondValue;

  bool get isValidEmailAddress => RegExp(
    r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
    .hasMatch(this);
}