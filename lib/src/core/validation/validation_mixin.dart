import 'dart:io';

import 'package:mutualz/src/core/core.dart';

mixin ValidationMixin {
  String? validateTextField(String value) {
    if (value.length < 2 || value.length > 60) return ValidationError.baseTextLength.message;

    return null;
  }

  String? validateEmailField(String value) {
    if (value.isEmpty) return ValidationError.emptyField.message;
    if (!value.isValidEmailAddress) return ValidationError.invalidEmail.message;
    if (value.length > 60) return ValidationError.invalidEmail.message;
    return null;
  }

  String? validatePasswordField(String value) {
    if (value.isEmpty) return ValidationError.emptyField.message;
    if (!value.isHasEnoughPasswordLength) return ValidationError.invalidPassword.message;
    if (!value.isHasNumber) return ValidationError.doesNotContainNumber.message;
    if (!value.isHasUpperCase) return ValidationError.doesNotContainUppercase.message;
    if (!value.isHasLowerCase) return ValidationError.doesNotContainLowercase.message;
    if (!value.isHasSpecialChar) return ValidationError.doesNotContainSpecialCharacter.message;

    return null;
  }

  String? validatePhoneNumberField(String value) {
    if (value.length < 12 || value.length > 20) return ValidationError.invalidPhoneNumber.message;
    return null;
  }

  String? validateUserNameField(String value) {
    if (value.isEmpty) return ValidationError.emptyField.message;
    if (!value.isHasEnoughUserNameLength) return ValidationError.invalidUserName.message;
    return null;
  }

  String? validateMatchPasswords(String password, String confirmPassword) {
    if (!password.isIdentical(confirmPassword)) return ValidationError.passwordsNotMatch.message;
    return null;
  }

  String? validatePrivacyCheckbox(bool value) {
    if (!value) return ValidationError.privacyNotAccepted.message;
    return null;
  }

  String? validatePlainValue (String? value) {
    if (value == null || value.isEmpty) return ValidationError.emptyField.message;
    return null;
  }

  String? validateImageValue(File? value) {
    if (value == null) return ValidationError.emptyField.message;
    return null;
  }
}