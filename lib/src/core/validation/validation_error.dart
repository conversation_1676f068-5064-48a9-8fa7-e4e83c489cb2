import 'package:mutualz/i18n/strings.g.dart';

enum ValidationError {
  emptyField,
  baseTextLength,
  invalidEmail,
  invalidPhoneNumber,
  invalidPassword,
  invalidUserName,
  passwordsNotMatch,
  privacyNotAccepted,
  doesNotContainNumber,
  doesNotContainSpecialCharacter,
  doesNotContainUppercase,
  doesNotContainLowercase,
  emptyRating;

  String get message {
    switch (this) {
      case ValidationError.emptyField:
        return t.errors.fieldEmpty;
      case ValidationError.baseTextLength:
        return t.errors.baseTextLength;
      case ValidationError.invalidEmail:
        return t.errors.emailInvalid;
      case ValidationError.invalidPhoneNumber:
        return t.errors.phoneNumberInvalid;
      case ValidationError.invalidPassword:
        return t.errors.passwordInvalid;
      case ValidationError.invalidUserName:
        return t.errors.userNameInvalid;
      case ValidationError.passwordsNotMatch:
        return t.errors.passwordNotMatch;
      case ValidationError.privacyNotAccepted:
        return t.errors.privacyNotAccepted;
      case ValidationError.doesNotContainNumber:
        return t.errors.doesNotContainNumber;
      case ValidationError.doesNotContainSpecialCharacter:
        return t.errors.doesNotContainSpecialCharacter;
      case ValidationError.doesNotContainUppercase:
        return t.errors.doesNotContainUppercase;
      case ValidationError.doesNotContainLowercase:
        return t.errors.doesNotContainLowercase;
      case ValidationError.emptyRating:
        return t.errors.emptyRating;
      default:
        return '';
    }
  }
}