import 'package:flutter/material.dart';
import 'package:mutualz/src/core/flavors/flavor_config.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:package_info_plus/package_info_plus.dart';

class FlavorBanner extends StatefulWidget {
  final Widget child;
  final Color color;
  final BannerLocation? bannerLocation;

  const FlavorBanner({
    required this.child,
    this.color = MColors.scarletEmber,
    this.bannerLocation,
    super.key,
  });

  @override
  State<FlavorBanner> createState() => _FlavorBannerState();
}

class _FlavorBannerState extends State<FlavorBanner> {
  @override
  Widget build(BuildContext context) {
    if (FlavorConfig.instance.name.isEmpty) {
      return widget.child;
    }

    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        if (snapshot.data != null) {
          return Directionality(
            textDirection: TextDirection.ltr,
            child: Banner(
              color: widget.color,
              message: '${snapshot.data?.version}(${snapshot.data?.buildNumber})',
              location: widget.bannerLocation != null
                  ? widget.bannerLocation!
                  : BannerLocation.bottomStart,
              textStyle: Theme.of(context)
                  .textTheme
                  .titleSmall!
                  .copyWith(color: MColors.white, fontSize: 10.0),
              child: widget.child,
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}
