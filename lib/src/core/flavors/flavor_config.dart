

import 'package:mutualz/src/core/flavors/flavor.dart';

class FlavorConfig {
  final Flavor flavour;
  final String name;
  final FlavorValues values;
  static late FlavorConfig _instance;

  factory FlavorConfig({
    required Flavor flavor,
    required String name,
    required FlavorValues values
  }) => _instance = FlavorConfig._(flavor, name, values);

  FlavorConfig._(this.flavour, this.name, this.values);

  static FlavorConfig get instance => _instance;

  String get envName => _instance.name;

  bool get isProd => _instance.flavour == Flavor.prod;

  bool get isDev => _instance.flavour == Flavor.dev;
}

class FlavorValues {
  final String baseApiUrl;

  const FlavorValues({required this.baseApiUrl});
}
