import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/core/services/dio/error_interceptor.dart';
import 'package:mutualz/src/core/services/dio/language_interceptor.dart';
import 'package:mutualz/src/core/services/dio/pretty_logger_interceptor.dart';
import 'package:mutualz/src/core/services/dio/token_interceptor.dart';
import 'package:mutualz/src/data/repositories/auth_repository_impl.dart';
import 'package:mutualz/src/data/repositories/order_repository_impl.dart';
import 'package:mutualz/src/data/repositories/post_repository_impl.dart';
import 'package:mutualz/src/data/repositories/report_repository_impl.dart';
import 'package:mutualz/src/data/repositories/restaurant_repository_impl.dart';
import 'package:mutualz/src/data/repositories/review_repository_impl.dart';
import 'package:mutualz/src/data/repositories/settings_repository_impl.dart';
import 'package:mutualz/src/data/repositories/subscription_repository_impl.dart';
import 'package:mutualz/src/data/repositories/token_repository_impl.dart';
import 'package:mutualz/src/data/repositories/tutorial_repository_impl.dart';
import 'package:mutualz/src/data/repositories/user_repository_impl.dart';
import 'package:mutualz/src/data/sources/remote/auth/auth_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/auth/logout_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/order/order_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/post/post_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/report/report_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/restaurant/restaurant_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/review/review_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/settings/settings_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/subscription/subscription_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/token/token_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/token/verify_token_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/tutorial/tutorial_remote_source.dart';
import 'package:mutualz/src/data/sources/remote/user/user_remote_source.dart';
import 'package:mutualz/src/data/sources/storage/order/order_storage_source.dart';
import 'package:mutualz/src/data/sources/storage/settings/settings_storage_source.dart';
import 'package:mutualz/src/domain/blocs/coupons/coupons_bloc.dart';
import 'package:mutualz/src/domain/domain.dart';
import 'package:mutualz/src/domain/repositories/order_repository.dart';
import 'package:mutualz/src/domain/repositories/post_repository.dart';
import 'package:mutualz/src/domain/repositories/report_repository.dart';
import 'package:mutualz/src/domain/repositories/repositories.dart';
import 'package:mutualz/src/domain/repositories/review_repository.dart';
import 'package:mutualz/src/domain/repositories/tutorial_repository.dart';
import 'package:mutualz/src/domain/services/navigation/navigation_event_service.dart';
import 'package:mutualz/src/domain/services/social_auth/social_auth_service.dart';
import 'package:mutualz/src/domain/usecases/orders/create_order_usecase.dart';
import 'package:mutualz/src/domain/usecases/restaurants/fetch_available_coupons_usecase.dart';
import 'package:mutualz/src/domain/usecases/settings/fetch_faqs_usecase.dart';
import 'package:mutualz/src/domain/usecases/onboarding/apply_onboarding_usecase.dart';
import 'package:mutualz/src/domain/usecases/settings/send_support_usecase.dart';
import 'package:mutualz/src/domain/usecases/tutorial/get_tutorial_states_usecase.dart';
import 'package:mutualz/src/domain/usecases/tutorial/reset_tutorial_states_usecase.dart';
import 'package:mutualz/src/domain/usecases/tutorial/update_tutorial_states_usecase.dart';
import 'package:mutualz/src/presentation/navigation/router.dart';

import '../../domain/blocs/block_user/block_user_bloc.dart';
import '../../domain/blocs/block_user_post/block_user_post_bloc.dart';
import '../../domain/blocs/late_review/late_review_bloc.dart';
import '../../domain/usecases/block/send_block_user_post_usecase.dart';
import '../../domain/usecases/block/send_block_user_usecase.dart';

final getIt = GetIt.instance;

Future<void> configureDependencies(FlavorConfig config) async {
  await dotenv.load(fileName: '.env');
  final baseApiUrl = config.isDev
      ? dotenv.get('BASE_API_URL_DEV')
      : dotenv.get('BASE_API_URL_PROD');
  final stripePublishableKey = config.isDev
      ? dotenv.get('STRIPE_PUBLISHABLE_KEY_DEV')
      : dotenv.get('STRIPE_PUBLISHABLE_KEY_PROD');

  final merchantIdentifier = dotenv.get('STRIPE_MERCHANT_IDENTIFIER');

  /// Pre-interceptors registration
  getIt.registerLazySingleton<StorageService>(() => StorageService());
  getIt.registerLazySingleton<SettingsStorageSource>(
      () => SettingsStorageSource(getIt<StorageService>()));
  getIt.registerLazySingleton<DeactivateService>(() => DeactivateService());
  getIt<DeactivateService>();

  final List<Interceptor> interceptors = [
    //if (config.isDev)
    prettyDioLogger,
    ErrorInterceptor(
      getIt<DeactivateService>(),
    ),
    LanguageInterceptor(getIt<SettingsStorageSource>()),
  ];

  getIt.registerFactory<Dio>(() => Dio(BaseOptions(
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
      receiveTimeout: const Duration(seconds: 30),
      baseUrl: baseApiUrl))
    ..interceptors.addAll(interceptors));

  final dio = getIt<Dio>();

  /// Registering services
  getIt.registerSingleton<MLogger>(MLogger());
  getIt.registerSingleton<GoRouter>(routerConfig);
  getIt.registerLazySingleton<AnalyticsService>(() => AnalyticsService());

  /// Tokens
  getIt.registerLazySingleton<TokenRemoteSource>(() => TokenRemoteSource(dio));
  getIt.registerFactory<TokenRepository>(() => TokenRepositoryImpl(
        getIt<StorageService>(),
        getIt<TokenRemoteSource>(),
      ));
  getIt.registerLazySingleton<TokenHandler>(() => TokenHandler(
        getIt<TokenRepository>(),
        getIt<MLogger>(),
      ));

  getIt.registerLazySingleton(() => TokenInterceptor(getIt<TokenHandler>()));

  final dioWithToken = getIt<Dio>()
    ..interceptors.add(getIt<TokenInterceptor>());
  getIt.registerLazySingleton<VerifyTokenRemoteSource>(
      () => VerifyTokenRemoteSource(dioWithToken));

  /// Services
  getIt.registerLazySingleton<SocialAuthService>(() => SocialAuthService());
  getIt.registerLazySingleton<DeepLinksService>(() => DeepLinksService());
  getIt.registerLazySingleton<GeoPositionService>(() => GeoPositionService());
  getIt.registerLazySingleton<FavoriteCheckerService>(
      () => FavoriteCheckerService());
  getIt.registerLazySingleton<PostUpdatesService>(() => PostUpdatesService());
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<DeviceInfoPlugin>(() => DeviceInfoPlugin());
  getIt.registerLazySingleton<NavigationEventService>(() => NavigationEventService());

  /// Sources
  getIt.registerLazySingleton<AuthRemoteSource>(() => AuthRemoteSource(dio));
  getIt.registerLazySingleton<LogoutRemoteSource>(
      () => LogoutRemoteSource(dioWithToken));
  getIt.registerLazySingleton<UserRemoteSource>(
      () => UserRemoteSource(dioWithToken));
  getIt.registerLazySingleton<RestaurantRemoteSource>(
      () => RestaurantRemoteSource(dioWithToken));
  getIt.registerLazySingleton<SettingsRemoteSource>(
      () => SettingsRemoteSource(dioWithToken));
  getIt.registerLazySingleton<OrderRemoteSource>(
      () => OrderRemoteSource(dioWithToken));
  getIt.registerLazySingleton<PostRemoteSource>(
      () => PostRemoteSource(dioWithToken));
  getIt.registerLazySingleton<ReviewRemoteSource>(
      () => ReviewRemoteSource(dioWithToken));
  getIt.registerLazySingleton<SubscriptionRemoteSource>(
      () => SubscriptionRemoteSource(dioWithToken));
  getIt.registerLazySingleton<ReportRemoteSource>(
      () => ReportRemoteSource(dioWithToken));
  getIt.registerLazySingleton<TutorialRemoteSource>(
      () => TutorialRemoteSource(dioWithToken));
  getIt.registerLazySingleton<OrderStorageSource>(
      () => OrderStorageSource(getIt<StorageService>()));

  /// Repositories
  getIt.registerFactory<AuthRepository>(() => AuthRepositoryImpl(
        getIt<StorageService>(),
        getIt<AuthRemoteSource>(),
        getIt<LogoutRemoteSource>(),
        getIt<UserRemoteSource>(),
      ));
  getIt.registerFactory<UserRepository>(() => UserRepositoryImpl(
        getIt<UserRemoteSource>(),
      ));
  getIt.registerLazySingleton<RestaurantRepository>(
      () => RestaurantRepositoryImpl(
            getIt<RestaurantRemoteSource>(),
          ));
  getIt.registerLazySingleton<SettingsRepository>(() => SettingsRepositoryImpl(
        getIt<SettingsRemoteSource>(),
        getIt<SettingsStorageSource>(),
      ));
  getIt.registerLazySingleton<OrderRepository>(() => OrderRepositoryImpl(
        getIt<OrderRemoteSource>(),
        getIt<OrderStorageSource>(),
      ));
  getIt.registerLazySingleton<PostRepository>(() => PostRepositoryImpl(
        getIt<PostRemoteSource>(),
      ));
  getIt.registerLazySingleton<ReviewRepository>(() => ReviewRepositoryImpl(
        getIt<ReviewRemoteSource>(),
      ));
  getIt.registerLazySingleton<SubscriptionRepository>(
      () => SubscriptionRepositoryImpl(
            getIt<SubscriptionRemoteSource>(),
          ));
  getIt.registerLazySingleton<ReportRepository>(() => ReportRepositoryImpl(
        getIt<ReportRemoteSource>(),
      ));
  getIt.registerLazySingleton<TutorialRepository>(() => TutorialRepositoryImpl(
        tutorialRemoteSource: getIt<TutorialRemoteSource>(),
      ));

  /// Usecases
  getIt.registerFactory<LoginUseCase>(
      () => LoginUseCase(getIt<AuthRepository>()));
  getIt.registerFactory<RegistrationUseCase>(
      () => RegistrationUseCase(getIt<AuthRepository>()));
  getIt.registerFactory<SignInWithSocialUseCase>(() => SignInWithSocialUseCase(
        getIt<SocialAuthService>(),
        getIt<AuthRepository>(),
      ));
  getIt.registerFactory<VerifyOtpUseCase>(
      () => VerifyOtpUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<ResendOtpUseCase>(
      () => ResendOtpUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<LogoutUseCase>(() => LogoutUseCase(
        getIt<AuthRepository>(),
        getIt<SocialAuthService>(),
      ));
  getIt.registerLazySingleton<UpdateSocialAuthOnBoardingUseCase>(
      () => UpdateSocialAuthOnBoardingUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<AutoRegisterUseCase>(
      () => AutoRegisterUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<SetIsEmailVerifiedUseCase>(
      () => SetIsEmailVerifiedUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<GetIsEmailVerifiedUseCase>(
      () => GetIsEmailVerifiedUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<RemoveUserUseCase>(
      () => RemoveUserUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<RequestPasswordResetUseCase>(
      () => RequestPasswordResetUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<ResetPasswordUseCase>(
      () => ResetPasswordUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton<ApplyOnBoardingUseCase>(
      () => ApplyOnBoardingUseCase(
            getIt<UserRepository>(),
          ));
  getIt.registerLazySingleton<FetchRestaurantsUseCase>(
      () => FetchRestaurantsUseCase(
            getIt<RestaurantRepository>(),
          ));
  getIt.registerLazySingleton<AddToFavoriteUseCase>(() => AddToFavoriteUseCase(
        getIt<RestaurantRepository>(),
      ));
  getIt.registerLazySingleton<RemoveFromFavoriteUseCase>(
      () => RemoveFromFavoriteUseCase(
            getIt<RestaurantRepository>(),
          ));
  getIt.registerLazySingleton<FetchCollectionsUseCase>(
      () => FetchCollectionsUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<BookTheTableUseCase>(() => BookTheTableUseCase(
        getIt<RestaurantRepository>(),
      ));
  getIt.registerLazySingleton<FetchCollectionByIdUseCase>(
      () => FetchCollectionByIdUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<GetUserUseCase>(
      () => GetUserUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<UpdateUserUseCase>(
      () => UpdateUserUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<ChangePasswordUseCase>(
      () => ChangePasswordUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<UploadUserImageUseCase>(
      () => UploadUserImageUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FetchMenuUseCase>(
      () => FetchMenuUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<FetchFAQsUseCase>(
      () => FetchFAQsUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<SendFeedbackUseCase>(
      () => SendFeedbackUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<SendSupportUseCase>(
      () => SendSupportUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<GetLocaleUseCase>(
      () => GetLocaleUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<SetLocaleUseCase>(
      () => SetLocaleUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<ChangeOrderUseCase>(
      () => ChangeOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<FetchOrderUseCase>(
      () => FetchOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<AddPostUseCase>(() => AddPostUseCase(
        getIt<PostRepository>(),
      ));
  getIt.registerLazySingleton<DeletePostUseCase>(() => DeletePostUseCase(
        getIt<PostRepository>(),
        getIt<PostUpdatesService>(),
      ));
  getIt.registerLazySingleton<AddPostCoverUseCase>(
      () => AddPostCoverUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<FetchPostsUseCase>(
      () => FetchPostsUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<AddCommentUseCase>(() => AddCommentUseCase(
        getIt<PostRepository>(),
        getIt<PostUpdatesService>(),
      ));
  getIt.registerLazySingleton<FetchCommentsUseCase>(
      () => FetchCommentsUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<MarkPostFavoriteUseCase>(
      () => MarkPostFavoriteUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<UnMarkPostFavoriteUseCase>(
      () => UnMarkPostFavoriteUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<MarkCommentFavoriteUseCase>(
      () => MarkCommentFavoriteUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<UnmarkCommentFavoriteUseCase>(
      () => UnmarkCommentFavoriteUseCase(getIt<PostRepository>()));
  getIt.registerLazySingleton<FetchUserLoyaltyUseCase>(
      () => FetchUserLoyaltyUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<InitiateRestaurantSessionUseCase>(
      () => InitiateRestaurantSessionUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FetchCouponsUseCase>(
      () => FetchCouponsUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<FetchAvailableCouponsUsecase>(
      () => FetchAvailableCouponsUsecase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<EarnPointsUseCase>(
      () => EarnPointsUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<SendInvoiceUseCase>(
      () => SendInvoiceUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<PrecalculateOrderUseCase>(
      () => PrecalculateOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<AttachCouponUseCase>(
      () => AttachCouponUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<RefreshCouponUseCase>(
      () => RefreshCouponUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<DetachCouponUseCase>(
      () => DetachCouponUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<FetchSpecialsUseCase>(
      () => FetchSpecialsUseCase(getIt<RestaurantRepository>()));
  getIt.registerLazySingleton<RateUseCase>(
      () => RateUseCase(getIt<ReviewRepository>()));
  getIt.registerLazySingleton<RateAndPostUseCase>(
      () => RateAndPostUseCase(getIt<ReviewRepository>()));
  getIt.registerLazySingleton<UpdateFcmTokenUseCase>(
      () => UpdateFcmTokenUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<DeleteFcmTokenUseCase>(
      () => DeleteFcmTokenUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<GetFirstLaunchUseCase>(
      () => GetFirstLaunchUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<SetFirstLaunchUseCase>(
      () => SetFirstLaunchUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<ClearSettingsUseCase>(
      () => ClearSettingsUseCase(getIt<SettingsRepository>()));
  getIt.registerLazySingleton<SendReportUserPostUseCase>(
      () => SendReportUserPostUseCase(getIt<ReportRepository>()));
  getIt.registerLazySingleton<SendReportUserUseCase>(
      () => SendReportUserUseCase(getIt<ReportRepository>()));
  getIt.registerLazySingleton<SendBlockUserUseCase>(
      () => SendBlockUserUseCase(getIt<ReportRepository>()));
  getIt.registerLazySingleton<SendBlockUserPostUseCase>(
      () => SendBlockUserPostUseCase(getIt<ReportRepository>()));
  getIt.registerLazySingleton<GetUserStrangerUseCase>(
      () => GetUserStrangerUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FollowUserUseCase>(
      () => FollowUserUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<UnFollowUserUseCase>(
      () => UnFollowUserUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FetchFollowersUseCase>(
      () => FetchFollowersUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FetchFollowingUseCase>(
      () => FetchFollowingUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<FetchUsersUseCase>(
      () => FetchUsersUseCase(getIt<UserRepository>()));
  getIt.registerLazySingleton<CreateStoredOrderUseCase>(
      () => CreateStoredOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<ReadStoredOrderUseCase>(
      () => ReadStoredOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<ClearStoredOrderUseCase>(
      () => ClearStoredOrderUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<CreateStoredCartUseCase>(
      () => CreateStoredCartUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<ClearStoredCartUseCase>(
      () => ClearStoredCartUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<ReadStoredCartUseCase>(
      () => ReadStoredCartUseCase(getIt<OrderRepository>()));
  getIt.registerLazySingleton<NotifyServerForCouponsNotificationUseCase>(
      () => NotifyServerForCouponsNotificationUseCase(getIt<OrderRepository>()));

  getIt.registerLazySingleton<GetTutorialStatesUseCase>(
      () => GetTutorialStatesUseCase(tutorialRepository: getIt<TutorialRepository>()));
  getIt.registerLazySingleton<UpdateTutorialStatesUseCase>(
      () => UpdateTutorialStatesUseCase(tutorialRepository: getIt<TutorialRepository>()));
  getIt.registerLazySingleton<ResetTutorialStatesUseCase>(
      () => ResetTutorialStatesUseCase(tutorialRepository: getIt<TutorialRepository>()));

  /// Payment service
  getIt.registerLazySingleton<PaymentService>(() => PaymentService(
        publishableKey: stripePublishableKey,
        merchantIdentifier: merchantIdentifier,
        orderRepository: getIt<OrderRepository>(),
        subscriptionRepository: getIt<SubscriptionRepository>(),
      ));

  /// Blocs
  getIt.registerFactory<LoginBloc>(() => LoginBloc(
        getIt<LoginUseCase>(),
        getIt<SignInWithSocialUseCase>(),
        getIt<TokenHandler>(),
        getIt<SetIsEmailVerifiedUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<RegistrationBloc>(() => RegistrationBloc(
        getIt<RegistrationUseCase>(),
        getIt<SignInWithSocialUseCase>(),
        getIt<TokenHandler>(),
        getIt<SetIsEmailVerifiedUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<VerifyBloc>(() => VerifyBloc(
        getIt<VerifyOtpUseCase>(),
        getIt<ResendOtpUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<AuthBloc>(() => AuthBloc(
        getIt<TokenHandler>(),
        getIt<GetIsEmailVerifiedUseCase>(),
        getIt<SetIsEmailVerifiedUseCase>(),
        getIt<LogoutUseCase>(),
        getIt<RemoveUserUseCase>(),
        getIt<DeepLinksService>(),
        getIt<GetUserUseCase>(),
        getIt<InitiateRestaurantSessionUseCase>(),
        getIt<GetTutorialStatesUseCase>(),
        getIt<UpdateTutorialStatesUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<AppBloc>(() => AppBloc(
        getIt<GetLocaleUseCase>(),
        getIt<SetLocaleUseCase>(),
        getIt<NotificationService>(),
        getIt<DeviceInfoPlugin>(),
        getIt<UpdateFcmTokenUseCase>(),
        getIt<DeleteFcmTokenUseCase>(),
        getIt<DeepLinksService>(),
        getIt<GetFirstLaunchUseCase>(),
        getIt<SetFirstLaunchUseCase>(),
        getIt<ClearSettingsUseCase>(),
        getIt<DeactivateService>(),
        getIt<ReadStoredOrderUseCase>(),
        getIt<NavigationEventService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<OnboardingBloc>(() => OnboardingBloc(
        getIt<ApplyOnBoardingUseCase>(),
        getIt<UpdateUserUseCase>(),
        getIt<AuthBloc>(),
        getIt<MLogger>(),
      ));
  getIt
      .registerFactory<SocialAuthOnboardingBloc>(() => SocialAuthOnboardingBloc(
            getIt<UpdateSocialAuthOnBoardingUseCase>(),
            getIt<AutoRegisterUseCase>(),
            getIt<MLogger>(),
          ));
  getIt.registerFactory<ForgotPasswordBloc>(() => ForgotPasswordBloc(
        getIt<RequestPasswordResetUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ResetPasswordBloc>(() => ResetPasswordBloc(
        getIt<ResetPasswordUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<SearchBloc>(() => SearchBloc(
        getIt<FetchRestaurantsUseCase>(),
        getIt<FetchCollectionsUseCase>(),
        getIt<FavoriteCheckerService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<DiscoverBloc>(() => DiscoverBloc(
        getIt<GeoPositionService>(),
        getIt<FetchRestaurantsUseCase>(),
        getIt<AddToFavoriteUseCase>(),
        getIt<RemoveFromFavoriteUseCase>(),
        getIt<FavoriteCheckerService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<RestaurantBloc>(() => RestaurantBloc(
        getIt<AddToFavoriteUseCase>(),
        getIt<RemoveFromFavoriteUseCase>(),
        getIt<FavoriteCheckerService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<TableBookingBloc>(() => TableBookingBloc(
        getIt<BookTheTableUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<CollectionBloc>(() => CollectionBloc(
        getIt<FetchCollectionByIdUseCase>(),
        getIt<FavoriteCheckerService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<UserProfileBloc>(() => UserProfileBloc(
        getIt<UpdateUserUseCase>(),
        getIt<UploadUserImageUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ChangePasswordBloc>(() => ChangePasswordBloc(
        getIt<ChangePasswordUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ChangePreferencesBloc>(() => ChangePreferencesBloc(
        getIt<UpdateUserUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<FeedbackBloc>(() => FeedbackBloc(
        getIt<SendFeedbackUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ContactBloc>(() => ContactBloc(
        getIt<SendSupportUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<FaqBloc>(() => FaqBloc(
        getIt<FetchFAQsUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<OrdersBloc>(() => OrdersBloc(
        getIt<FetchOrderUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<RestaurantMenuBloc>(() => RestaurantMenuBloc(
        getIt<FetchMenuUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<CommentsBloc>(() => CommentsBloc(
        getIt<FetchCommentsUseCase>(),
        getIt<AddCommentUseCase>(),
        getIt<MarkCommentFavoriteUseCase>(),
        getIt<UnmarkCommentFavoriteUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<OrderFlowBloc>(() => OrderFlowBloc(
        getIt<FetchMenuUseCase>(),
        getIt<ChangeOrderUseCase>(),
        getIt<FetchOrderUseCase>(),
        getIt<PaymentService>(),
        getIt<AddToFavoriteUseCase>(),
        getIt<RemoveFromFavoriteUseCase>(),
        getIt<FavoriteCheckerService>(),
        getIt<EarnPointsUseCase>(),
        getIt<PrecalculateOrderUseCase>(),
        getIt<AttachCouponUseCase>(),
        getIt<DetachCouponUseCase>(),
        getIt<FetchSpecialsUseCase>(),
        getIt<CreateStoredOrderUseCase>(),
        getIt<ClearStoredOrderUseCase>(),
        getIt<CreateStoredCartUseCase>(),
        getIt<ClearStoredCartUseCase>(),
        getIt<ReadStoredCartUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<AddPostBloc>(() => AddPostBloc(
        getIt<AddPostUseCase>(),
        getIt<AddPostCoverUseCase>(),
        getIt<PostUpdatesService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<FeedBloc>(() => FeedBloc(
        getIt<FetchPostsUseCase>(),
        getIt<PostUpdatesService>(),
        getIt<DeletePostUseCase>(),
        getIt<MarkPostFavoriteUseCase>(),
        getIt<UnMarkPostFavoriteUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<UserPostsBloc>(() => UserPostsBloc(
        getIt<FetchPostsUseCase>(),
        getIt<PostUpdatesService>(),
        getIt<DeletePostUseCase>(),
        getIt<MarkPostFavoriteUseCase>(),
        getIt<UnMarkPostFavoriteUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<RestaurantFeedBloc>(() => RestaurantFeedBloc(
        getIt<FetchPostsUseCase>(),
        getIt<DeletePostUseCase>(),
        getIt<PostUpdatesService>(),
        getIt<MarkPostFavoriteUseCase>(),
        getIt<UnMarkPostFavoriteUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<UserLoyaltyBloc>(() => UserLoyaltyBloc(
        getIt<FetchUserLoyaltyUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<CouponsBloc>(() => CouponsBloc(
      getIt<FetchCouponsUseCase>(),
      getIt<RefreshCouponUseCase>(),
      getIt<NotifyServerForCouponsNotificationUseCase>(),
      getIt<FetchAvailableCouponsUsecase>(),
      getIt<MLogger>(),
  ));
  getIt.registerFactory<RestaurantSpecialsBloc>(() => RestaurantSpecialsBloc(
        getIt<FetchSpecialsUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ReviewBloc>(() => ReviewBloc(
        getIt<RateUseCase>(),
        getIt<RateAndPostUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<LateReviewBloc>(() => LateReviewBloc(
        getIt<RateUseCase>(),
        getIt<RateAndPostUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<SubscriptionBloc>(() => SubscriptionBloc(
        getIt<PaymentService>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ReportUserPostBloc>(() => ReportUserPostBloc(
        getIt<SendReportUserPostUseCase>(),
        getIt<SendReportUserUseCase>(),
        getIt<MLogger>(),
      ));

  getIt.registerFactory<BlockUserPostBloc>(() => BlockUserPostBloc(
        getIt<SendBlockUserPostUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<ReportUserBloc>(() => ReportUserBloc(
        getIt<SendReportUserUseCase>(),
        getIt<MLogger>(),
      ));

  getIt.registerFactory<BlockUserBloc>(() => BlockUserBloc(
        getIt<SendBlockUserUseCase>(),
        getIt<MLogger>(),
      ));
  getIt.registerFactory<UserStrangerProfileBloc>(() => UserStrangerProfileBloc(
        getIt<GetUserStrangerUseCase>(),
        getIt<FetchPostsUseCase>(),
        getIt<PostUpdatesService>(),
        getIt<MarkPostFavoriteUseCase>(),
        getIt<UnMarkPostFavoriteUseCase>(),
        getIt<FollowUserUseCase>(),
        getIt<UnFollowUserUseCase>(),
        getIt<MLogger>(),
      ));

  getIt.registerFactory<FollowersBloc>(() => FollowersBloc(
        getIt<FetchFollowersUseCase>(),
        getIt<MLogger>(),
      ));

  getIt.registerFactory<FollowingBloc>(() => FollowingBloc(
        getIt<FetchFollowingUseCase>(),
        getIt<MLogger>(),
      ));

  getIt.registerFactory<UsersBloc>(() => UsersBloc(
        getIt<FetchUsersUseCase>(),
        getIt<MLogger>(),
      ));
}
