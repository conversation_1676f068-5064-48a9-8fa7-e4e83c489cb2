import 'dart:async';
import 'dart:developer';
import 'package:app_links/app_links.dart';

class DeepLinksService {
  late AppLinks _appLinks;
  final StreamController<Uri?> _controller = StreamController.broadcast();

  DeepLinksService() {
    _appLinks = AppLinks();
    _appLinks.uriLinkStream.listen((Uri? link) {
      log('DeepLinksService: $link');
      _controller.add(link);
    });
  }

  Future<Uri?> getInitialDeepLink() => _appLinks.getInitialLink();

  Stream<Uri?> get stream => _controller.stream;

  void dispose() {
    _controller.close();
  }
}




