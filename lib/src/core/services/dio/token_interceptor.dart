import 'package:dio/dio.dart';
import 'package:mutualz/src/domain/services/token_handler/token_handler.dart';

class TokenInterceptor implements Interceptor {
  final TokenHandler _tokenHandler;

  TokenInterceptor(this._tokenHandler);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final isTokensValid = await _tokenHandler.tokensValidityCheck();

    if (isTokensValid) {
      final accessToken = await _tokenHandler.getAccessToken();
      options.headers['Authorization'] = 'Bearer $accessToken';
    }

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    return handler.next(response);
  }

  @override
  void onError(DioException e, ErrorInterceptorHandler handler) async {

    /// If the request is unauthorized, clear the tokens
    if (e.response?.statusCode == 401) {
      await _tokenHandler.clear();
      _tokenHandler.changeTokenValidity(false);
    }

    return handler.next(e);
  }
}