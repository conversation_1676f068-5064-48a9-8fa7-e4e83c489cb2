import 'package:dio/dio.dart';
import 'package:mutualz/i18n/strings.g.dart';
import 'package:mutualz/src/data/sources/storage/settings/settings_storage_source.dart';

class LanguageInterceptor implements Interceptor {
  final SettingsStorageSource _settingsStorageSource;

  const LanguageInterceptor(this._settingsStorageSource);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final Object getStoredRawLocale = await _settingsStorageSource.getLocale() ?? LocaleSettings.useDeviceLocale();

    final locale = AppLocaleUtils.parse(getStoredRawLocale is AppLocale ?
      getStoredRawLocale.languageCode : getStoredRawLocale.toString());

    options.headers['x-lang'] = locale.languageCode;

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    return handler.next(err);
  }
}