import 'package:dio/dio.dart';
import 'package:mutualz/src/core/core.dart';

class ErrorInterceptor implements Interceptor {
  final DeactivateService _deactivateService;

  const ErrorInterceptor(this._deactivateService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (response.headers['x-deactivated'] == null) {
      _deactivateService.deactivate(false);
    }
    return handler.next(response);
  }

  @override
  void onError(DioException e, ErrorInterceptorHandler handler) async {
    if (e.response?.statusCode == 403 &&
        e.response?.headers['x-deactivated'] is List &&
        (e.response?.headers['x-deactivated'] as List).first.toString() == 'true') {
      _deactivateService.deactivate(true);
    }

    var message = e.message;
    if (e.response?.data != null &&
        e.response?.data is Map<String, dynamic> &&
        e.response?.data['message'] != null) {
      message = e.response?.data['message'].toString();
    }

    if (e.response?.statusCode == 404) {
      message = 'Resource not found';
    }

    final dioException = DioException(
      message: message,
      requestOptions: e.requestOptions,
      response: e.response,
    );

    return handler.next(dioException);
  }
}