import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

/// Logger proxy class
class MLogger {
  late final Logger _logger;

  MLogger() {
    _logger = Logger(
      printer: PrettyPrinter(
        stackTraceBeginIndex: 2,
        methodCount: 2,
        errorMethodCount: 5,
        lineLength: 120,
        printEmojis: true,
      ),
    );
  }

  void info(String message) {
    final detailedMessage = _getDetailedMessage(message, Level.info);
    _logger.i(detailedMessage);
  }

  void error(String message) {
    final detailedMessage = _getDetailedMessage(message, Level.error);
    _logger.e(detailedMessage);
  }

  void warning(String message) {
    final detailedMessage = _getDetailedMessage(message, Level.warning);
    _logger.w(detailedMessage);
  }

  /// Generic logging method
  String _getDetailedMessage(
    String msg,
    Level level,
  ) {
    final levelString = level.toString().split('.').last;
    final formattedTime = DateFormat('HH:mm:ss').format(DateTime.now());
    return '[$formattedTime] [${levelString.toUpperCase()}] - $msg';
  }
}
