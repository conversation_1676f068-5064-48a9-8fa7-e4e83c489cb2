import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  final FlutterSecureStorage _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  final Future<SharedPreferences> _sharedPreferences = SharedPreferences.getInstance();

  FlutterSecureStorage get storage => _storage;

  Future<SharedPreferences> get sharedPreferences => _sharedPreferences;
}