import 'package:flutter/foundation.dart';

class OrderFlowNotificationTracker {
  static final ValueNotifier<bool> _hasChecked = ValueNotifier<bool>(false);
  
  static bool get hasChecked => _hasChecked.value;
  
  static void markAsChecked() {
    _hasChecked.value = true;
  }
  
  static void reset() {
    _hasChecked.value = false;
  }
  
  static ValueListenable<bool> get notifier => _hasChecked;
}