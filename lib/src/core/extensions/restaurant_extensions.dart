import 'package:mutualz/src/domain/models/restaurant/restaurant_model.dart';
import 'package:mutualz/src/domain/models/schedule/schedule_model.dart';

extension RestaurantOpeningHoursX on RestaurantModel {
  bool get isCurrentlyOpen {
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    final openingTime = schedule.getOpeningTime(now);
    final closingTime = schedule.getClosingTime(now);
    
    if (openingTime.isEmpty || closingTime.isEmpty) {
      return false;
    }
    
    return _isTimeWithinRange(currentTime, openingTime, closingTime);
  }
  
  bool _isTimeWithinRange(String currentTime, String openingTime, String closingTime) {
    final current = _parseTimeToMinutes(currentTime);
    final opening = _parseTimeToMinutes(openingTime);
    final closing = _parseTimeToMinutes(closingTime);
    
    if (current == null || opening == null || closing == null) {
      return false;
    }
    
    if (opening <= closing) {
      return current >= opening && current <= closing;
    } else {
      return current >= opening || current <= closing;
    }
  }
  
  int? _parseTimeToMinutes(String time) {
    final parts = time.split(':');
    if (parts.length != 2) return null;
    
    final hours = int.tryParse(parts[0]);
    final minutes = int.tryParse(parts[1]);
    
    if (hours == null || minutes == null || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      return null;
    }
    
    return hours * 60 + minutes;
  }
}