extension DateTimeExtension on DateTime {
  /// Returns the number of days from now until this DateTime
  int get daysFromNow {
    final now = DateTime.now();
    final difference = this.difference(now);
    return difference.inDays + 1;
  }

  /// Returns the number of days until the end of the current month
  static int get daysUntilEndOfCurrentMonth {
    final now = DateTime.now();
    final lastDayOfMonth = DateTime(now.year, now.month + 1, 0);
    final difference = lastDayOfMonth.difference(now);
    return difference.inDays + 1;
  }

  /// Returns the number of days from now until the specified date
  static int daysUntil(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);
    return difference.inDays + 1;
  }
}