import 'package:mutualz/i18n/strings.g.dart';

enum ErrorCode {
  unknown,
  errorAuthConflict,
  errorAuthTokenExpired,
  errorSocialAuthFailed,
  errorAuthCancelled,
  errorAuthOperationInProgress;

  String get message {
    switch (this) {
      case unknown:
        return t.errors.unknown;
      case errorAuthConflict:
        return t.errors.errorAuthConflict;
      case errorAuthTokenExpired:
        return t.errors.errorAuthTokenExpired;
      case errorSocialAuthFailed:
        return t.errors.errorSocialAuthFailed;
      case errorAuthOperationInProgress:
        return t.errors.errorAuthOperationInProgress;
      case errorAuthCancelled:
        return t.errors.errorAuthCancelled;
    }
  }
}