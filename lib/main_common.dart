import 'dart:ui';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mutualz/src/app.dart';
import 'package:mutualz/src/core/core.dart';
import 'package:mutualz/src/presentation/theme/theme.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:showcaseview/showcaseview.dart';

import 'firebase_background.dart';
import 'i18n/strings.g.dart';

Future<void> mainCommon(FlavorConfig config) async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Register background message handler BEFORE dependency injection
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  
  await configureDependencies(config);

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  
  // Exceptions to ignore:
  // - IgnoredDeeplinkException - deeplink is not handled by the app

  FlutterError.onError = (FlutterErrorDetails details) {
    if (details.exception is IgnoredDeeplinkException) return;

    FlutterError.presentError(details);
  };
  
  PlatformDispatcher.instance.onError = (error, stack) {
    if (error is IgnoredDeeplinkException) return true;

    FlutterError.reportError(FlutterErrorDetails(
      exception: error,
      stack: stack,
    ));

    return true;
  };

  await SentryFlutter.init(
    (options) {
      options.dsn = config.isProd ?
        dotenv.get('SENTRY_DSN_PROD') : dotenv.get('SENTRY_DSN_DEV');
      options.tracesSampleRate = 1.0;
      options.profilesSampleRate = 1.0;
      
      options.beforeSend = (event, hint) {
        if (event.throwable is IgnoredDeeplinkException) return null;
        
        return event;
      };
    },
    appRunner: () => runApp(
      ShowCaseWidget( builder: (context) => TranslationProvider(
      child: config.isProd ?
      const App() :
      const FlavorBanner(
        color: MColors.scarletEmber,
        bannerLocation: BannerLocation.bottomEnd,
        child: App()
      ),
    ),),),
  );
}

